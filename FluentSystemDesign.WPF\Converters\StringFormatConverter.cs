using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace FluentSystemDesign.WPF.Converters
{
    /// <summary>
    /// 字符串格式化转换器
    /// 使用指定的格式字符串对输入值进行格式化
    /// </summary>
    /// <remarks>
    /// 支持标准的.NET字符串格式化功能：
    /// - 复合格式字符串："{0} - {1}"
    /// - 格式说明符："{0:C}", "{0:D}", "{0:F2}"
    /// - 自定义格式：任何有效的格式字符串
    /// 
    /// 特殊参数：
    /// - "Null=替换文本": 当输入为null时显示的文本
    /// - "Empty=替换文本": 当输入为空字符串时显示的文本
    /// - "Trim": 对字符串输入进行Trim操作
    /// </remarks>
    /// <example>
    /// 在XAML中使用：
    /// <code>
    /// &lt;TextBlock Text="{Binding Price, Converter={StaticResource StringFormatConverter}, ConverterParameter='价格: {0:C}'}"/&gt;
    /// &lt;TextBlock Text="{Binding Count, Converter={StaticResource StringFormatConverter}, ConverterParameter='共 {0} 项'}"/&gt;
    /// &lt;TextBlock Text="{Binding UserName, Converter={StaticResource StringFormatConverter}, ConverterParameter='用户: {0},Null=未登录'}"/&gt;
    /// </code>
    /// </example>
    [ValueConversion(typeof(object), typeof(string))]
    public class StringFormatConverter : IValueConverter
    {
        /// <summary>
        /// 默认格式字符串
        /// </summary>
        public string DefaultFormat { get; set; } = "{0}";

        /// <summary>
        /// 默认的null值替换文本
        /// </summary>
        public string DefaultNullText { get; set; } = "";

        /// <summary>
        /// 默认的空字符串替换文本
        /// </summary>
        public string DefaultEmptyText { get; set; } = "";

        /// <summary>
        /// 默认是否对字符串进行Trim
        /// </summary>
        public bool DefaultTrimString { get; set; } = false;

        /// <summary>
        /// 格式化输入值
        /// </summary>
        /// <param name="value">输入值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">格式字符串和选项</param>
        /// <param name="culture">文化信息</param>
        /// <returns>格式化后的字符串</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var (format, nullText, emptyText, trimString) = ParseParameter(parameter?.ToString());

            // 处理null值
            if (value == null)
            {
                return nullText;
            }

            // 处理字符串值
            if (value is string stringValue)
            {
                if (trimString)
                {
                    stringValue = stringValue.Trim();
                }

                if (string.IsNullOrEmpty(stringValue))
                {
                    return emptyText;
                }

                value = stringValue;
            }

            // 应用格式化
            try
            {
                // 检查格式字符串是否包含占位符
                if (format.Contains("{0}"))
                {
                    return string.Format(culture, format, value);
                }
                else
                {
                    // 如果没有占位符，直接返回格式字符串
                    return format;
                }
            }
            catch (FormatException)
            {
                // 如果格式化失败，返回原始值的字符串表示
                return value.ToString();
            }
        }

        /// <summary>
        /// 反向转换（尝试从格式化字符串中提取原始值）
        /// </summary>
        /// <param name="value">格式化后的字符串</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">格式字符串和选项</param>
        /// <param name="culture">文化信息</param>
        /// <returns>提取的原始值</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not string stringValue)
            {
                return DependencyProperty.UnsetValue;
            }

            var (format, nullText, emptyText, _) = ParseParameter(parameter?.ToString());

            // 检查是否为特殊值
            if (stringValue == nullText)
            {
                return null;
            }

            if (stringValue == emptyText)
            {
                return string.Empty;
            }

            // 尝试从格式化字符串中提取值
            if (format.Contains("{0}"))
            {
                var extractedValue = ExtractValueFromFormattedString(stringValue, format);
                if (extractedValue != null)
                {
                    return ConvertToTargetType(extractedValue, targetType, culture);
                }
            }

            // 如果无法提取，直接返回字符串值
            return ConvertToTargetType(stringValue, targetType, culture);
        }

        /// <summary>
        /// 从格式化字符串中提取原始值
        /// </summary>
        /// <param name="formattedString">格式化后的字符串</param>
        /// <param name="format">格式字符串</param>
        /// <returns>提取的值</returns>
        private static string ExtractValueFromFormattedString(string formattedString, string format)
        {
            try
            {
                // 简单的提取逻辑：查找{0}的位置并提取相应部分
                var placeholderIndex = format.IndexOf("{0}");
                if (placeholderIndex == -1)
                {
                    return null;
                }

                var beforePlaceholder = format.Substring(0, placeholderIndex);
                var afterPlaceholderIndex = format.IndexOf("}", placeholderIndex) + 1;
                var afterPlaceholder = afterPlaceholderIndex < format.Length ? format.Substring(afterPlaceholderIndex) : "";

                // 移除前缀和后缀
                var result = formattedString;
                if (!string.IsNullOrEmpty(beforePlaceholder) && result.StartsWith(beforePlaceholder))
                {
                    result = result.Substring(beforePlaceholder.Length);
                }

                if (!string.IsNullOrEmpty(afterPlaceholder) && result.EndsWith(afterPlaceholder))
                {
                    result = result.Substring(0, result.Length - afterPlaceholder.Length);
                }

                return result;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 将字符串值转换为目标类型
        /// </summary>
        /// <param name="value">字符串值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="culture">文化信息</param>
        /// <returns>转换后的值</returns>
        private static object ConvertToTargetType(string value, Type targetType, CultureInfo culture)
        {
            if (targetType == typeof(string))
            {
                return value;
            }

            try
            {
                if (targetType == typeof(int) || targetType == typeof(int?))
                {
                    return int.Parse(value, culture);
                }
                else if (targetType == typeof(double) || targetType == typeof(double?))
                {
                    return double.Parse(value, culture);
                }
                else if (targetType == typeof(decimal) || targetType == typeof(decimal?))
                {
                    return decimal.Parse(value, culture);
                }
                else if (targetType == typeof(float) || targetType == typeof(float?))
                {
                    return float.Parse(value, culture);
                }
                else if (targetType == typeof(bool) || targetType == typeof(bool?))
                {
                    return bool.Parse(value);
                }
                else if (targetType == typeof(DateTime) || targetType == typeof(DateTime?))
                {
                    return DateTime.Parse(value, culture);
                }
                else
                {
                    return System.Convert.ChangeType(value, targetType, culture);
                }
            }
            catch
            {
                return DependencyProperty.UnsetValue;
            }
        }

        /// <summary>
        /// 解析参数字符串
        /// </summary>
        /// <param name="parameter">参数字符串</param>
        /// <returns>解析结果</returns>
        private (string format, string nullText, string emptyText, bool trimString) ParseParameter(string parameter)
        {
            var format = DefaultFormat;
            var nullText = DefaultNullText;
            var emptyText = DefaultEmptyText;
            var trimString = DefaultTrimString;

            if (string.IsNullOrWhiteSpace(parameter))
            {
                return (format, nullText, emptyText, trimString);
            }

            // 解析特殊选项
            var workingParameter = parameter;

            // 提取Null选项
            var nullMatch = System.Text.RegularExpressions.Regex.Match(parameter, @"Null\s*=\s*([^,]+)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            if (nullMatch.Success)
            {
                nullText = nullMatch.Groups[1].Value.Trim();
                workingParameter = workingParameter.Replace(nullMatch.Value, "").Trim();
            }

            // 提取Empty选项
            var emptyMatch = System.Text.RegularExpressions.Regex.Match(parameter, @"Empty\s*=\s*([^,]+)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            if (emptyMatch.Success)
            {
                emptyText = emptyMatch.Groups[1].Value.Trim();
                workingParameter = workingParameter.Replace(emptyMatch.Value, "").Trim();
            }

            // 检查Trim选项
            if (parameter.ToLowerInvariant().Contains("trim"))
            {
                trimString = true;
                workingParameter = System.Text.RegularExpressions.Regex.Replace(workingParameter, @"\btrim\b", "", System.Text.RegularExpressions.RegexOptions.IgnoreCase).Trim();
            }

            // 移除多余的逗号
            workingParameter = System.Text.RegularExpressions.Regex.Replace(workingParameter, @"^,+|,+$", "").Trim();
            workingParameter = System.Text.RegularExpressions.Regex.Replace(workingParameter, @",+", ",").Trim();

            // 剩余的部分作为格式字符串
            if (!string.IsNullOrWhiteSpace(workingParameter))
            {
                format = workingParameter;
            }

            return (format, nullText, emptyText, trimString);
        }
    }
}
