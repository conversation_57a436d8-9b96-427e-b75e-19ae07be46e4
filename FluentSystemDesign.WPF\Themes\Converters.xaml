<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:converters="clr-namespace:FluentSystemDesign.WPF.Converters">

    <!-- ========================================== -->
    <!-- FluentSystemDesign WPF 值转换器资源字典 -->
    <!-- ========================================== -->

    <!-- 布尔转换器 -->
    <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter" />
    <converters:InverseBoolConverter x:Key="InverseBoolConverter" />
    <converters:BoolToOpacityConverter x:Key="BoolToOpacityConverter" />

    <!-- 数值转换器 -->
    <converters:NumberToStringConverter x:Key="NumberToStringConverter" />
    <converters:PercentageConverter x:Key="PercentageConverter" />
    <converters:ThicknessConverter x:Key="ThicknessConverter" />

    <!-- 颜色转换器 -->
    <converters:ColorToBrushConverter x:Key="ColorToBrushConverter" />
    <converters:HexToColorConverter x:Key="HexToColorConverter" />
    <converters:ColorToContrastConverter x:Key="ColorToContrastConverter" />

    <!-- 枚举转换器 -->
    <converters:EnumToStringConverter x:Key="EnumToStringConverter" />
    <converters:EnumToBoolConverter x:Key="EnumToBoolConverter" />
    <converters:EnumToVisibilityConverter x:Key="EnumToVisibilityConverter" />

    <!-- 集合转换器 -->
    <converters:CollectionToVisibilityConverter x:Key="CollectionToVisibilityConverter" />
    <converters:CountToVisibilityConverter x:Key="CountToVisibilityConverter" />
    <converters:IsNullOrEmptyConverter x:Key="IsNullOrEmptyConverter" />

    <!-- 字符串转换器 -->
    <converters:StringToUpperConverter x:Key="StringToUpperConverter" />
    <converters:StringToLowerConverter x:Key="StringToLowerConverter" />
    <converters:StringFormatConverter x:Key="StringFormatConverter" />
    <converters:TextCaseConverter x:Key="TextCaseConverter" />
    <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter" />

    <!-- 主题转换器 -->
    <converters:ThemeToColorConverter x:Key="ThemeToColorConverter" />
    <converters:ThemeToBrushConverter x:Key="ThemeToBrushConverter" />

    <!-- ========================================== -->
    <!-- 常用转换器别名 -->
    <!-- ========================================== -->

    <!-- 可见性转换器别名 -->
    <converters:BoolToVisibilityConverter x:Key="BoolToVisibility" />
    <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityInvert" />
    <converters:InverseBoolConverter x:Key="InverseBool" />
    <converters:CollectionToVisibilityConverter x:Key="CollectionToVisibility" />
    <converters:IsNullOrEmptyConverter x:Key="NullToVisibility" />

    <!-- 字符串转换器别名 -->
    <converters:StringToUpperConverter x:Key="ToUpper" />
    <converters:StringToLowerConverter x:Key="ToLower" />
    <converters:StringFormatConverter x:Key="StringFormat" />

    <!-- 数值转换器别名 -->
    <converters:NumberToStringConverter x:Key="NumberToString" />
    <converters:PercentageConverter x:Key="ToPercentage" />

    <!-- 颜色转换器别名 -->
    <converters:ColorToBrushConverter x:Key="ColorToBrush" />
    <converters:HexToColorConverter x:Key="HexToColor" />

    <!-- ========================================== -->
    <!-- 预配置的转换器实例 -->
    <!-- ========================================== -->

    <!-- 货币格式转换器 -->
    <converters:NumberToStringConverter x:Key="CurrencyConverter" DefaultFormat="C2" />

    <!-- 百分比格式转换器 -->
    <converters:NumberToStringConverter x:Key="PercentageStringConverter" DefaultFormat="P1" />

    <!-- 整数格式转换器 -->
    <converters:NumberToStringConverter x:Key="IntegerConverter" DefaultFormat="N0" />

    <!-- 小数格式转换器 -->
    <converters:NumberToStringConverter x:Key="DecimalConverter" DefaultFormat="F2" />

    <!-- 标题格式转换器 -->
    <converters:StringToUpperConverter x:Key="TitleCaseConverter" DefaultMode="Words" />

    <!-- 驼峰格式转换器 -->
    <converters:StringToLowerConverter x:Key="CamelCaseConverter" DefaultMode="CamelCase" />

    <!-- 透明度转换器 -->
    <converters:BoolToOpacityConverter x:Key="FadeConverter" TrueOpacity="1.0" FalseOpacity="0.3" />

    <!-- 禁用状态转换器 -->
    <converters:BoolToOpacityConverter x:Key="DisabledConverter" TrueOpacity="1.0" FalseOpacity="0.5" />

    <!-- 主题背景转换器 -->
    <converters:ThemeToBrushConverter x:Key="ThemeBackgroundConverter" FreezeBrushes="True" />

    <!-- 主题文本转换器 -->
    <converters:ThemeToBrushConverter x:Key="ThemeTextConverter" FreezeBrushes="True" />

</ResourceDictionary>
