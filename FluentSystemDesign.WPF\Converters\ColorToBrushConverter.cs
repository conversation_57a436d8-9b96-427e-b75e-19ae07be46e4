using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;

namespace FluentSystemDesign.WPF.Converters
{
    /// <summary>
    /// 颜色到画刷转换器
    /// 将Color对象转换为SolidColorBrush，支持透明度调整
    /// </summary>
    /// <remarks>
    /// 支持的功能：
    /// - Color -> SolidColorBrush 转换
    /// - 通过参数调整透明度，格式："Alpha=0.5"
    /// - 支持冻结画刷以提高性能
    /// - 支持从十六进制字符串创建画刷
    /// </remarks>
    /// <example>
    /// 在XAML中使用：
    /// <code>
    /// &lt;Rectangle Fill="{Binding BackgroundColor, Converter={StaticResource ColorToBrushConverter}}"/&gt;
    /// &lt;Rectangle Fill="{Binding BackgroundColor, Converter={StaticResource ColorToBrushConverter}, ConverterParameter=Alpha=0.5}"/&gt;
    /// &lt;Border Background="{Binding HexColor, Converter={StaticResource ColorToBrushConverter}}"/&gt;
    /// </code>
    /// </example>
    [ValueConversion(typeof(Color), typeof(Brush))]
    [ValueConversion(typeof(string), typeof(Brush))]
    public class ColorToBrushConverter : IValueConverter
    {
        /// <summary>
        /// 是否冻结创建的画刷以提高性能
        /// </summary>
        public bool FreezeBrush { get; set; } = true;

        /// <summary>
        /// 将颜色转换为画刷
        /// </summary>
        /// <param name="value">Color对象或十六进制颜色字符串</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数：Alpha=透明度值</param>
        /// <param name="culture">文化信息</param>
        /// <returns>SolidColorBrush对象</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            Color color;

            // 解析输入值
            if (value is Color colorValue)
            {
                color = colorValue;
            }
            else if (value is string stringValue && !string.IsNullOrWhiteSpace(stringValue))
            {
                if (!TryParseColor(stringValue, out color))
                {
                    return DependencyProperty.UnsetValue;
                }
            }
            else
            {
                return DependencyProperty.UnsetValue;
            }

            // 解析参数
            var alpha = ParseAlphaParameter(parameter?.ToString());
            if (alpha.HasValue)
            {
                color = Color.FromArgb((byte)(alpha.Value * 255), color.R, color.G, color.B);
            }

            // 创建画刷
            var brush = new SolidColorBrush(color);

            // 冻结画刷以提高性能
            if (FreezeBrush && brush.CanFreeze)
            {
                brush.Freeze();
            }

            return brush;
        }

        /// <summary>
        /// 将画刷转换为颜色
        /// </summary>
        /// <param name="value">SolidColorBrush对象</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>Color对象或十六进制字符串</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not SolidColorBrush brush)
            {
                return DependencyProperty.UnsetValue;
            }

            var color = brush.Color;

            if (targetType == typeof(string))
            {
                return ColorToHex(color);
            }

            return color;
        }

        /// <summary>
        /// 尝试解析颜色字符串
        /// </summary>
        /// <param name="colorString">颜色字符串</param>
        /// <param name="color">解析出的颜色</param>
        /// <returns>是否解析成功</returns>
        private static bool TryParseColor(string colorString, out Color color)
        {
            color = Colors.Transparent;

            try
            {
                // 尝试使用ColorConverter解析
                var parsedColor = ColorConverter.ConvertFromString(colorString);
                if (parsedColor is Color colorValue)
                {
                    color = colorValue;
                    return true;
                }
            }
            catch
            {
                // 忽略异常，继续尝试其他方法
            }

            // 尝试解析十六进制格式
            if (colorString.StartsWith("#"))
            {
                return TryParseHexColor(colorString, out color);
            }

            // 尝试解析命名颜色
            try
            {
                var property = typeof(Colors).GetProperty(colorString);
                if (property != null && property.PropertyType == typeof(Color))
                {
                    color = (Color)property.GetValue(null);
                    return true;
                }
            }
            catch
            {
                // 忽略异常
            }

            return false;
        }

        /// <summary>
        /// 尝试解析十六进制颜色
        /// </summary>
        /// <param name="hex">十六进制颜色字符串</param>
        /// <param name="color">解析出的颜色</param>
        /// <returns>是否解析成功</returns>
        private static bool TryParseHexColor(string hex, out Color color)
        {
            color = Colors.Transparent;

            if (string.IsNullOrWhiteSpace(hex) || !hex.StartsWith("#"))
            {
                return false;
            }

            hex = hex.Substring(1); // 移除#

            try
            {
                switch (hex.Length)
                {
                    case 3: // RGB
                        color = Color.FromRgb(
                            System.Convert.ToByte(hex.Substring(0, 1) + hex.Substring(0, 1), 16),
                            System.Convert.ToByte(hex.Substring(1, 1) + hex.Substring(1, 1), 16),
                            System.Convert.ToByte(hex.Substring(2, 1) + hex.Substring(2, 1), 16));
                        return true;

                    case 6: // RRGGBB
                        color = Color.FromRgb(
                            System.Convert.ToByte(hex.Substring(0, 2), 16),
                            System.Convert.ToByte(hex.Substring(2, 2), 16),
                            System.Convert.ToByte(hex.Substring(4, 2), 16));
                        return true;

                    case 8: // AARRGGBB
                        color = Color.FromArgb(
                            System.Convert.ToByte(hex.Substring(0, 2), 16),
                            System.Convert.ToByte(hex.Substring(2, 2), 16),
                            System.Convert.ToByte(hex.Substring(4, 2), 16),
                            System.Convert.ToByte(hex.Substring(6, 2), 16));
                        return true;
                }
            }
            catch
            {
                // 忽略转换异常
            }

            return false;
        }

        /// <summary>
        /// 解析透明度参数
        /// </summary>
        /// <param name="parameter">参数字符串</param>
        /// <returns>透明度值（0-1）</returns>
        private static double? ParseAlphaParameter(string parameter)
        {
            if (string.IsNullOrWhiteSpace(parameter))
            {
                return null;
            }

            var lowerParam = parameter.ToLowerInvariant();
            if (lowerParam.StartsWith("alpha="))
            {
                var alphaString = parameter.Substring(6);
                if (double.TryParse(alphaString, NumberStyles.Float, CultureInfo.InvariantCulture, out double alpha))
                {
                    return Math.Max(0.0, Math.Min(1.0, alpha));
                }
            }

            return null;
        }

        /// <summary>
        /// 将颜色转换为十六进制字符串
        /// </summary>
        /// <param name="color">颜色</param>
        /// <returns>十六进制字符串</returns>
        private static string ColorToHex(Color color)
        {
            if (color.A == 255)
            {
                return $"#{color.R:X2}{color.G:X2}{color.B:X2}";
            }
            else
            {
                return $"#{color.A:X2}{color.R:X2}{color.G:X2}{color.B:X2}";
            }
        }
    }
}
