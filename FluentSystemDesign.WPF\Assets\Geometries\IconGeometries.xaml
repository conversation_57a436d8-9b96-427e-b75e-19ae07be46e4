<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 
    图标几何形状资源字典
    包含箭头、勾选、关闭、菜单、搜索等常用图标几何形状
    遵循Fluent Design System设计语言，支持矢量缩放
    -->

    <!-- 箭头图标 -->
    
    <!-- 向右箭头 -->
    <PathGeometry x:Key="ChevronRightGeometry">
        <PathFigure StartPoint="8,4">
            <LineSegment Point="16,12" />
            <LineSegment Point="8,20" />
        </PathFigure>
    </PathGeometry>

    <!-- 向左箭头 -->
    <PathGeometry x:Key="ChevronLeftGeometry">
        <PathFigure StartPoint="16,4">
            <LineSegment Point="8,12" />
            <LineSegment Point="16,20" />
        </PathFigure>
    </PathGeometry>

    <!-- 向上箭头 -->
    <PathGeometry x:Key="ChevronUpGeometry">
        <PathFigure StartPoint="4,16">
            <LineSegment Point="12,8" />
            <LineSegment Point="20,16" />
        </PathFigure>
    </PathGeometry>

    <!-- 向下箭头 -->
    <PathGeometry x:Key="ChevronDownGeometry">
        <PathFigure StartPoint="4,8">
            <LineSegment Point="12,16" />
            <LineSegment Point="20,8" />
        </PathFigure>
    </PathGeometry>

    <!-- 双向右箭头 -->
    <PathGeometry x:Key="DoubleChevronRightGeometry">
        <PathFigure StartPoint="6,4">
            <LineSegment Point="14,12" />
            <LineSegment Point="6,20" />
        </PathFigure>
        <PathFigure StartPoint="10,4">
            <LineSegment Point="18,12" />
            <LineSegment Point="10,20" />
        </PathFigure>
    </PathGeometry>

    <!-- 双向左箭头 -->
    <PathGeometry x:Key="DoubleChevronLeftGeometry">
        <PathFigure StartPoint="18,4">
            <LineSegment Point="10,12" />
            <LineSegment Point="18,20" />
        </PathFigure>
        <PathFigure StartPoint="14,4">
            <LineSegment Point="6,12" />
            <LineSegment Point="14,20" />
        </PathFigure>
    </PathGeometry>

    <!-- 勾选图标 -->
    <PathGeometry x:Key="CheckMarkGeometry">
        <PathFigure StartPoint="4,12">
            <LineSegment Point="9,17" />
            <LineSegment Point="20,6" />
        </PathFigure>
    </PathGeometry>

    <!-- 粗勾选图标 -->
    <PathGeometry x:Key="ThickCheckMarkGeometry">
        <PathFigure StartPoint="3,12" IsClosed="True">
            <LineSegment Point="9,18" />
            <LineSegment Point="21,6" />
            <LineSegment Point="21,8" />
            <LineSegment Point="9,20" />
            <LineSegment Point="3,14" />
        </PathFigure>
    </PathGeometry>

    <!-- 关闭图标 -->
    <PathGeometry x:Key="CloseGeometry">
        <PathFigure StartPoint="6,6">
            <LineSegment Point="18,18" />
        </PathFigure>
        <PathFigure StartPoint="18,6">
            <LineSegment Point="6,18" />
        </PathFigure>
    </PathGeometry>

    <!-- 加号图标 -->
    <PathGeometry x:Key="PlusGeometry">
        <PathFigure StartPoint="12,4">
            <LineSegment Point="12,20" />
        </PathFigure>
        <PathFigure StartPoint="4,12">
            <LineSegment Point="20,12" />
        </PathFigure>
    </PathGeometry>

    <!-- 减号图标 -->
    <PathGeometry x:Key="MinusGeometry">
        <PathFigure StartPoint="4,12">
            <LineSegment Point="20,12" />
        </PathFigure>
    </PathGeometry>

    <!-- 菜单图标（汉堡菜单） -->
    <PathGeometry x:Key="MenuGeometry">
        <PathFigure StartPoint="3,6">
            <LineSegment Point="21,6" />
        </PathFigure>
        <PathFigure StartPoint="3,12">
            <LineSegment Point="21,12" />
        </PathFigure>
        <PathFigure StartPoint="3,18">
            <LineSegment Point="21,18" />
        </PathFigure>
    </PathGeometry>

    <!-- 搜索图标 -->
    <PathGeometry x:Key="SearchGeometry">
        <PathFigure StartPoint="11,19">
            <ArcSegment Point="11,19" Size="8,8" SweepDirection="Clockwise" IsLargeArc="True" />
        </PathFigure>
        <PathFigure StartPoint="21,21">
            <LineSegment Point="16.65,16.65" />
        </PathFigure>
    </PathGeometry>

    <!-- 更详细的搜索图标 -->
    <PathGeometry x:Key="DetailedSearchGeometry">
        <PathFigure StartPoint="15.5,14">
            <ArcSegment Point="9.5,14" Size="6,6" SweepDirection="Clockwise" IsLargeArc="True" />
            <ArcSegment Point="15.5,14" Size="6,6" SweepDirection="Clockwise" IsLargeArc="True" />
        </PathFigure>
        <PathFigure StartPoint="18.35,18.35">
            <LineSegment Point="21,21" />
        </PathFigure>
    </PathGeometry>

    <!-- 设置图标（齿轮） -->
    <PathGeometry x:Key="SettingsGeometry">
        <PathFigure StartPoint="12,1" IsClosed="True">
            <LineSegment Point="15.09,2.09" />
            <LineSegment Point="16.91,0.27" />
            <LineSegment Point="19.73,3.09" />
            <LineSegment Point="17.91,4.91" />
            <LineSegment Point="23,12" />
            <LineSegment Point="17.91,19.09" />
            <LineSegment Point="19.73,20.91" />
            <LineSegment Point="16.91,23.73" />
            <LineSegment Point="15.09,21.91" />
            <LineSegment Point="12,23" />
            <LineSegment Point="8.91,21.91" />
            <LineSegment Point="7.09,23.73" />
            <LineSegment Point="4.27,20.91" />
            <LineSegment Point="6.09,19.09" />
            <LineSegment Point="1,12" />
            <LineSegment Point="6.09,4.91" />
            <LineSegment Point="4.27,3.09" />
            <LineSegment Point="7.09,0.27" />
            <LineSegment Point="8.91,2.09" />
        </PathFigure>
        <PathFigure StartPoint="12,8" IsClosed="True">
            <ArcSegment Point="12,8" Size="4,4" SweepDirection="Clockwise" IsLargeArc="True" />
        </PathFigure>
    </PathGeometry>

    <!-- 信息图标 -->
    <PathGeometry x:Key="InfoGeometry">
        <PathFigure StartPoint="12,2" IsClosed="True">
            <ArcSegment Point="12,2" Size="10,10" SweepDirection="Clockwise" IsLargeArc="True" />
        </PathFigure>
        <PathFigure StartPoint="11,10">
            <LineSegment Point="13,10" />
            <LineSegment Point="13,17" />
            <LineSegment Point="11,17" />
        </PathFigure>
        <PathFigure StartPoint="12,6" IsClosed="True">
            <ArcSegment Point="12,6" Size="1,1" SweepDirection="Clockwise" IsLargeArc="True" />
        </PathFigure>
    </PathGeometry>

    <!-- 警告图标 -->
    <PathGeometry x:Key="WarningGeometry">
        <PathFigure StartPoint="12,2" IsClosed="True">
            <LineSegment Point="22,20" />
            <LineSegment Point="2,20" />
        </PathFigure>
        <PathFigure StartPoint="11,8">
            <LineSegment Point="13,8" />
            <LineSegment Point="13,13" />
            <LineSegment Point="11,13" />
        </PathFigure>
        <PathFigure StartPoint="12,16" IsClosed="True">
            <ArcSegment Point="12,16" Size="1,1" SweepDirection="Clockwise" IsLargeArc="True" />
        </PathFigure>
    </PathGeometry>

    <!-- 错误图标 -->
    <PathGeometry x:Key="ErrorGeometry">
        <PathFigure StartPoint="12,2" IsClosed="True">
            <ArcSegment Point="12,2" Size="10,10" SweepDirection="Clockwise" IsLargeArc="True" />
        </PathFigure>
        <PathFigure StartPoint="8,8">
            <LineSegment Point="16,16" />
        </PathFigure>
        <PathFigure StartPoint="16,8">
            <LineSegment Point="8,16" />
        </PathFigure>
    </PathGeometry>

    <!-- 成功图标 -->
    <PathGeometry x:Key="SuccessGeometry">
        <PathFigure StartPoint="12,2" IsClosed="True">
            <ArcSegment Point="12,2" Size="10,10" SweepDirection="Clockwise" IsLargeArc="True" />
        </PathFigure>
        <PathFigure StartPoint="8,12">
            <LineSegment Point="11,15" />
            <LineSegment Point="16,10" />
        </PathFigure>
    </PathGeometry>

    <!-- 主页图标 -->
    <PathGeometry x:Key="HomeGeometry">
        <PathFigure StartPoint="3,12" IsClosed="True">
            <LineSegment Point="12,3" />
            <LineSegment Point="21,12" />
            <LineSegment Point="19,12" />
            <LineSegment Point="19,21" />
            <LineSegment Point="5,21" />
            <LineSegment Point="5,12" />
        </PathFigure>
        <PathFigure StartPoint="9,21" IsClosed="True">
            <LineSegment Point="15,21" />
            <LineSegment Point="15,15" />
            <LineSegment Point="9,15" />
        </PathFigure>
    </PathGeometry>

    <!-- 用户图标 -->
    <PathGeometry x:Key="UserGeometry">
        <PathFigure StartPoint="12,12" IsClosed="True">
            <ArcSegment Point="12,12" Size="4,4" SweepDirection="Clockwise" IsLargeArc="True" />
        </PathFigure>
        <PathFigure StartPoint="6,21">
            <ArcSegment Point="18,21" Size="6,6" SweepDirection="Clockwise" IsLargeArc="False" />
        </PathFigure>
    </PathGeometry>

    <!-- 文件图标 -->
    <PathGeometry x:Key="FileGeometry">
        <PathFigure StartPoint="6,2" IsClosed="True">
            <LineSegment Point="14,2" />
            <LineSegment Point="20,8" />
            <LineSegment Point="20,20" />
            <LineSegment Point="6,20" />
        </PathFigure>
        <PathFigure StartPoint="14,2">
            <LineSegment Point="14,8" />
            <LineSegment Point="20,8" />
        </PathFigure>
    </PathGeometry>

    <!-- 文件夹图标 -->
    <PathGeometry x:Key="FolderGeometry">
        <PathFigure StartPoint="4,6" IsClosed="True">
            <LineSegment Point="10,6" />
            <LineSegment Point="12,4" />
            <LineSegment Point="20,4" />
            <LineSegment Point="20,18" />
            <LineSegment Point="4,18" />
        </PathFigure>
    </PathGeometry>

</ResourceDictionary>
