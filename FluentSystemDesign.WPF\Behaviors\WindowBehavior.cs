using System;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Interop;
using Microsoft.Xaml.Behaviors;

namespace FluentSystemDesign.WPF.Behaviors
{
    /// <summary>
    /// 窗口行为，为Window提供增强功能
    /// </summary>
    public class WindowBehavior : Behavior<Window>
    {
        #region Win32 API

        [DllImport("user32.dll")]
        private static extern int GetWindowLong(IntPtr hWnd, int nIndex);

        [DllImport("user32.dll")]
        private static extern int SetWindowLong(IntPtr hWnd, int nIndex, int dwNewLong);

        [DllImport("user32.dll")]
        private static extern bool SetWindowPos(IntPtr hWnd, IntPtr hWndInsertAfter, int X, int Y, int cx, int cy, uint uFlags);

        private const int GWL_STYLE = -16;
        private const int WS_SYSMENU = 0x80000;
        private const int WS_MAXIMIZEBOX = 0x10000;
        private const int WS_MINIMIZEBOX = 0x20000;
        private const uint SWP_NOSIZE = 0x0001;
        private const uint SWP_NOMOVE = 0x0002;
        private const uint SWP_NOZORDER = 0x0004;
        private const uint SWP_FRAMECHANGED = 0x0020;

        #endregion

        #region 依赖属性

        /// <summary>
        /// 是否启用拖拽移动
        /// </summary>
        public static readonly DependencyProperty EnableDragMoveProperty =
            DependencyProperty.Register(nameof(EnableDragMove), typeof(bool), typeof(WindowBehavior),
                new PropertyMetadata(false));

        /// <summary>
        /// 是否启用双击最大化/还原
        /// </summary>
        public static readonly DependencyProperty EnableDoubleClickMaximizeProperty =
            DependencyProperty.Register(nameof(EnableDoubleClickMaximize), typeof(bool), typeof(WindowBehavior),
                new PropertyMetadata(false));

        /// <summary>
        /// 是否保存窗口位置和大小
        /// </summary>
        public static readonly DependencyProperty SaveWindowPlacementProperty =
            DependencyProperty.Register(nameof(SaveWindowPlacement), typeof(bool), typeof(WindowBehavior),
                new PropertyMetadata(false));

        /// <summary>
        /// 窗口位置保存键
        /// </summary>
        public static readonly DependencyProperty PlacementKeyProperty =
            DependencyProperty.Register(nameof(PlacementKey), typeof(string), typeof(WindowBehavior),
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 是否启用窗口淡入淡出效果
        /// </summary>
        public static readonly DependencyProperty EnableFadeEffectProperty =
            DependencyProperty.Register(nameof(EnableFadeEffect), typeof(bool), typeof(WindowBehavior),
                new PropertyMetadata(false));

        /// <summary>
        /// 淡入淡出持续时间
        /// </summary>
        public static readonly DependencyProperty FadeDurationProperty =
            DependencyProperty.Register(nameof(FadeDuration), typeof(TimeSpan), typeof(WindowBehavior),
                new PropertyMetadata(TimeSpan.FromMilliseconds(300)));

        /// <summary>
        /// 是否隐藏最小化按钮
        /// </summary>
        public static readonly DependencyProperty HideMinimizeButtonProperty =
            DependencyProperty.Register(nameof(HideMinimizeButton), typeof(bool), typeof(WindowBehavior),
                new PropertyMetadata(false, OnWindowStylePropertyChanged));

        /// <summary>
        /// 是否隐藏最大化按钮
        /// </summary>
        public static readonly DependencyProperty HideMaximizeButtonProperty =
            DependencyProperty.Register(nameof(HideMaximizeButton), typeof(bool), typeof(WindowBehavior),
                new PropertyMetadata(false, OnWindowStylePropertyChanged));

        /// <summary>
        /// 是否隐藏关闭按钮
        /// </summary>
        public static readonly DependencyProperty HideCloseButtonProperty =
            DependencyProperty.Register(nameof(HideCloseButton), typeof(bool), typeof(WindowBehavior),
                new PropertyMetadata(false, OnWindowStylePropertyChanged));

        /// <summary>
        /// 是否启用窗口阴影
        /// </summary>
        public static readonly DependencyProperty EnableDropShadowProperty =
            DependencyProperty.Register(nameof(EnableDropShadow), typeof(bool), typeof(WindowBehavior),
                new PropertyMetadata(true));

        /// <summary>
        /// 是否启用ESC键关闭窗口
        /// </summary>
        public static readonly DependencyProperty EnableEscapeKeyCloseProperty =
            DependencyProperty.Register(nameof(EnableEscapeKeyClose), typeof(bool), typeof(WindowBehavior),
                new PropertyMetadata(false));

        #endregion

        #region 属性

        /// <summary>
        /// 获取或设置是否启用拖拽移动
        /// </summary>
        public bool EnableDragMove
        {
            get => (bool)GetValue(EnableDragMoveProperty);
            set => SetValue(EnableDragMoveProperty, value);
        }

        /// <summary>
        /// 获取或设置是否启用双击最大化/还原
        /// </summary>
        public bool EnableDoubleClickMaximize
        {
            get => (bool)GetValue(EnableDoubleClickMaximizeProperty);
            set => SetValue(EnableDoubleClickMaximizeProperty, value);
        }

        /// <summary>
        /// 获取或设置是否保存窗口位置和大小
        /// </summary>
        public bool SaveWindowPlacement
        {
            get => (bool)GetValue(SaveWindowPlacementProperty);
            set => SetValue(SaveWindowPlacementProperty, value);
        }

        /// <summary>
        /// 获取或设置窗口位置保存键
        /// </summary>
        public string PlacementKey
        {
            get => (string)GetValue(PlacementKeyProperty);
            set => SetValue(PlacementKeyProperty, value);
        }

        /// <summary>
        /// 获取或设置是否启用窗口淡入淡出效果
        /// </summary>
        public bool EnableFadeEffect
        {
            get => (bool)GetValue(EnableFadeEffectProperty);
            set => SetValue(EnableFadeEffectProperty, value);
        }

        /// <summary>
        /// 获取或设置淡入淡出持续时间
        /// </summary>
        public TimeSpan FadeDuration
        {
            get => (TimeSpan)GetValue(FadeDurationProperty);
            set => SetValue(FadeDurationProperty, value);
        }

        /// <summary>
        /// 获取或设置是否隐藏最小化按钮
        /// </summary>
        public bool HideMinimizeButton
        {
            get => (bool)GetValue(HideMinimizeButtonProperty);
            set => SetValue(HideMinimizeButtonProperty, value);
        }

        /// <summary>
        /// 获取或设置是否隐藏最大化按钮
        /// </summary>
        public bool HideMaximizeButton
        {
            get => (bool)GetValue(HideMaximizeButtonProperty);
            set => SetValue(HideMaximizeButtonProperty, value);
        }

        /// <summary>
        /// 获取或设置是否隐藏关闭按钮
        /// </summary>
        public bool HideCloseButton
        {
            get => (bool)GetValue(HideCloseButtonProperty);
            set => SetValue(HideCloseButtonProperty, value);
        }

        /// <summary>
        /// 获取或设置是否启用窗口阴影
        /// </summary>
        public bool EnableDropShadow
        {
            get => (bool)GetValue(EnableDropShadowProperty);
            set => SetValue(EnableDropShadowProperty, value);
        }

        /// <summary>
        /// 获取或设置是否启用ESC键关闭窗口
        /// </summary>
        public bool EnableEscapeKeyClose
        {
            get => (bool)GetValue(EnableEscapeKeyCloseProperty);
            set => SetValue(EnableEscapeKeyCloseProperty, value);
        }

        #endregion

        #region 事件

        /// <summary>
        /// 窗口显示前事件
        /// </summary>
        public event EventHandler<WindowEventArgs> WindowShowing;

        /// <summary>
        /// 窗口关闭前事件
        /// </summary>
        public event EventHandler<WindowClosingEventArgs> WindowClosing;

        #endregion

        #region 重写方法

        /// <summary>
        /// 附加到关联对象时调用
        /// </summary>
        protected override void OnAttached()
        {
            base.OnAttached();
            
            if (AssociatedObject != null)
            {
                AssociatedObject.SourceInitialized += OnSourceInitialized;
                AssociatedObject.Loaded += OnLoaded;
                AssociatedObject.Closing += OnClosing;
                AssociatedObject.MouseLeftButtonDown += OnMouseLeftButtonDown;
                AssociatedObject.MouseDoubleClick += OnMouseDoubleClick;
                AssociatedObject.KeyDown += OnKeyDown;
                
                if (EnableFadeEffect)
                {
                    SetupFadeEffect();
                }
                
                if (SaveWindowPlacement)
                {
                    LoadWindowPlacement();
                }
            }
        }

        /// <summary>
        /// 从关联对象分离时调用
        /// </summary>
        protected override void OnDetaching()
        {
            if (AssociatedObject != null)
            {
                AssociatedObject.SourceInitialized -= OnSourceInitialized;
                AssociatedObject.Loaded -= OnLoaded;
                AssociatedObject.Closing -= OnClosing;
                AssociatedObject.MouseLeftButtonDown -= OnMouseLeftButtonDown;
                AssociatedObject.MouseDoubleClick -= OnMouseDoubleClick;
                AssociatedObject.KeyDown -= OnKeyDown;
            }
            
            base.OnDetaching();
        }

        #endregion

        #region 事件处理

        private void OnSourceInitialized(object sender, EventArgs e)
        {
            ApplyWindowStyle();
            
            if (EnableDropShadow)
            {
                ApplyDropShadow();
            }
        }

        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            WindowShowing?.Invoke(this, new WindowEventArgs(AssociatedObject));
        }

        private void OnClosing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            var eventArgs = new WindowClosingEventArgs(AssociatedObject);
            WindowClosing?.Invoke(this, eventArgs);
            
            if (eventArgs.Cancel)
            {
                e.Cancel = true;
                return;
            }
            
            if (SaveWindowPlacement)
            {
                SaveWindowPlacementToSettings();
            }
        }

        private void OnMouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (EnableDragMove && AssociatedObject.WindowState != WindowState.Maximized)
            {
                try
                {
                    AssociatedObject.DragMove();
                }
                catch
                {
                    // 忽略拖拽异常
                }
            }
        }

        private void OnMouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (EnableDoubleClickMaximize)
            {
                AssociatedObject.WindowState = AssociatedObject.WindowState == WindowState.Maximized
                    ? WindowState.Normal
                    : WindowState.Maximized;
            }
        }

        private void OnKeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            if (EnableEscapeKeyClose && e.Key == System.Windows.Input.Key.Escape)
            {
                AssociatedObject.Close();
            }
        }

        #endregion

        #region 私有方法

        private void ApplyWindowStyle()
        {
            var hwnd = new WindowInteropHelper(AssociatedObject).Handle;
            if (hwnd == IntPtr.Zero)
                return;

            var style = GetWindowLong(hwnd, GWL_STYLE);

            if (HideMinimizeButton)
                style &= ~WS_MINIMIZEBOX;

            if (HideMaximizeButton)
                style &= ~WS_MAXIMIZEBOX;

            if (HideCloseButton)
                style &= ~WS_SYSMENU;

            SetWindowLong(hwnd, GWL_STYLE, style);
            SetWindowPos(hwnd, IntPtr.Zero, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER | SWP_FRAMECHANGED);
        }

        private void ApplyDropShadow()
        {
            // 应用窗口阴影效果
            if (AssociatedObject.AllowsTransparency)
            {
                var dropShadowEffect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    Color = System.Windows.Media.Colors.Black,
                    Direction = 315,
                    ShadowDepth = 5,
                    Opacity = 0.3,
                    BlurRadius = 10
                };
                AssociatedObject.Effect = dropShadowEffect;
            }
        }

        private void SetupFadeEffect()
        {
            AssociatedObject.Opacity = 0;
            AssociatedObject.Loaded += (s, e) =>
            {
                var fadeInAnimation = new System.Windows.Media.Animation.DoubleAnimation
                {
                    From = 0,
                    To = 1,
                    Duration = FadeDuration
                };
                AssociatedObject.BeginAnimation(UIElement.OpacityProperty, fadeInAnimation);
            };
        }

        private void LoadWindowPlacement()
        {
            if (string.IsNullOrEmpty(PlacementKey) || AssociatedObject == null)
                return;

            try
            {
                // 使用注册表保存窗口位置信息
                using var key = Microsoft.Win32.Registry.CurrentUser.OpenSubKey($@"Software\FluentSystemDesign\WindowPlacement\{PlacementKey}");
                if (key != null)
                {
                    var left = key.GetValue("Left") as int?;
                    var top = key.GetValue("Top") as int?;
                    var width = key.GetValue("Width") as double?;
                    var height = key.GetValue("Height") as double?;
                    var windowState = key.GetValue("WindowState") as int?;

                    if (left.HasValue && top.HasValue)
                    {
                        AssociatedObject.Left = left.Value;
                        AssociatedObject.Top = top.Value;
                    }

                    if (width.HasValue && height.HasValue && width.Value > 0 && height.Value > 0)
                    {
                        AssociatedObject.Width = width.Value;
                        AssociatedObject.Height = height.Value;
                    }

                    if (windowState.HasValue && Enum.IsDefined(typeof(WindowState), windowState.Value))
                    {
                        AssociatedObject.WindowState = (WindowState)windowState.Value;
                    }
                }
            }
            catch
            {
                // 忽略加载错误
            }
        }

        private void SaveWindowPlacementToSettings()
        {
            if (string.IsNullOrEmpty(PlacementKey) || AssociatedObject == null)
                return;

            try
            {
                // 使用注册表保存窗口位置信息
                using var key = Microsoft.Win32.Registry.CurrentUser.CreateSubKey($@"Software\FluentSystemDesign\WindowPlacement\{PlacementKey}");
                if (key != null)
                {
                    key.SetValue("Left", (int)AssociatedObject.Left);
                    key.SetValue("Top", (int)AssociatedObject.Top);
                    key.SetValue("Width", AssociatedObject.Width);
                    key.SetValue("Height", AssociatedObject.Height);
                    key.SetValue("WindowState", (int)AssociatedObject.WindowState);
                }
            }
            catch
            {
                // 忽略保存错误
            }
        }

        #endregion

        #region 依赖属性回调

        private static void OnWindowStylePropertyChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is WindowBehavior behavior && behavior.AssociatedObject != null)
            {
                behavior.ApplyWindowStyle();
            }
        }

        #endregion
    }

    /// <summary>
    /// 窗口事件参数
    /// </summary>
    public class WindowEventArgs : EventArgs
    {
        /// <summary>
        /// 窗口对象
        /// </summary>
        public Window Window { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="window">窗口对象</param>
        public WindowEventArgs(Window window)
        {
            Window = window;
        }
    }

    /// <summary>
    /// 窗口关闭事件参数
    /// </summary>
    public class WindowClosingEventArgs : WindowEventArgs
    {
        /// <summary>
        /// 是否取消关闭
        /// </summary>
        public bool Cancel { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="window">窗口对象</param>
        public WindowClosingEventArgs(Window window) : base(window)
        {
        }
    }
}
