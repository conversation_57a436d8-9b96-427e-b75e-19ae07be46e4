<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ========================================== -->
    <!-- Fluent Design System Gradient Effects -->
    <!-- For modern visual hierarchy and depth -->
    <!-- ========================================== -->

    <!-- ========================================== -->
    <!-- Primary Gradients -->
    <!-- ========================================== -->

    <!-- Primary Linear Gradient: Top to bottom -->
    <LinearGradientBrush x:Key="PrimaryLinearGradient" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#2196F3" Offset="0"/>
        <GradientStop Color="#1976D2" Offset="1"/>
    </LinearGradientBrush>

    <!-- Primary Radial Gradient: Center outward -->
    <RadialGradientBrush x:Key="PrimaryRadialGradient" Center="0.5,0.5" RadiusX="0.8" RadiusY="0.8">
        <GradientStop Color="#42A5F5" Offset="0"/>
        <GradientStop Color="#1976D2" Offset="1"/>
    </RadialGradientBrush>

    <!-- Primary Diagonal Gradient: Top-left to bottom-right -->
    <LinearGradientBrush x:Key="PrimaryDiagonalGradient" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#64B5F6" Offset="0"/>
        <GradientStop Color="#1565C0" Offset="1"/>
    </LinearGradientBrush>

    <!-- ========================================== -->
    <!-- Secondary Gradients -->
    <!-- ========================================== -->

    <!-- Secondary Linear Gradient -->
    <LinearGradientBrush x:Key="SecondaryLinearGradient" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#9C27B0" Offset="0"/>
        <GradientStop Color="#7B1FA2" Offset="1"/>
    </LinearGradientBrush>

    <!-- Secondary Radial Gradient -->
    <RadialGradientBrush x:Key="SecondaryRadialGradient" Center="0.5,0.5" RadiusX="0.8" RadiusY="0.8">
        <GradientStop Color="#AB47BC" Offset="0"/>
        <GradientStop Color="#7B1FA2" Offset="1"/>
    </RadialGradientBrush>

    <!-- ========================================== -->
    <!-- Accent Gradients -->
    <!-- ========================================== -->

    <!-- Accent Linear Gradient -->
    <LinearGradientBrush x:Key="AccentLinearGradient" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#FF9800" Offset="0"/>
        <GradientStop Color="#F57C00" Offset="1"/>
    </LinearGradientBrush>

    <!-- Accent Radial Gradient -->
    <RadialGradientBrush x:Key="AccentRadialGradient" Center="0.5,0.5" RadiusX="0.8" RadiusY="0.8">
        <GradientStop Color="#FFA726" Offset="0"/>
        <GradientStop Color="#F57C00" Offset="1"/>
    </RadialGradientBrush>

    <!-- ========================================== -->
    <!-- Neutral Gradients -->
    <!-- ========================================== -->

    <!-- Light Neutral Gradient -->
    <LinearGradientBrush x:Key="LightNeutralGradient" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#FAFAFA" Offset="0"/>
        <GradientStop Color="#F5F5F5" Offset="1"/>
    </LinearGradientBrush>

    <!-- Medium Neutral Gradient -->
    <LinearGradientBrush x:Key="MediumNeutralGradient" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#E0E0E0" Offset="0"/>
        <GradientStop Color="#BDBDBD" Offset="1"/>
    </LinearGradientBrush>

    <!-- Dark Neutral Gradient -->
    <LinearGradientBrush x:Key="DarkNeutralGradient" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#424242" Offset="0"/>
        <GradientStop Color="#212121" Offset="1"/>
    </LinearGradientBrush>

    <!-- ========================================== -->
    <!-- Semantic Gradients -->
    <!-- ========================================== -->

    <!-- Success Gradient -->
    <LinearGradientBrush x:Key="SuccessGradient" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#4CAF50" Offset="0"/>
        <GradientStop Color="#388E3C" Offset="1"/>
    </LinearGradientBrush>

    <!-- Warning Gradient -->
    <LinearGradientBrush x:Key="WarningGradient" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#FF8F00" Offset="0"/>
        <GradientStop Color="#E65100" Offset="1"/>
    </LinearGradientBrush>

    <!-- Error Gradient -->
    <LinearGradientBrush x:Key="ErrorGradient" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#F44336" Offset="0"/>
        <GradientStop Color="#D32F2F" Offset="1"/>
    </LinearGradientBrush>

    <!-- Info Gradient -->
    <LinearGradientBrush x:Key="InfoGradient" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#00BCD4" Offset="0"/>
        <GradientStop Color="#0097A7" Offset="1"/>
    </LinearGradientBrush>

    <!-- ========================================== -->
    <!-- Special Effect Gradients -->
    <!-- ========================================== -->

    <!-- Glass Effect Gradient -->
    <LinearGradientBrush x:Key="GlassGradient" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#40FFFFFF" Offset="0"/>
        <GradientStop Color="#10FFFFFF" Offset="0.5"/>
        <GradientStop Color="#20FFFFFF" Offset="1"/>
    </LinearGradientBrush>

    <!-- Shimmer Effect Gradient -->
    <LinearGradientBrush x:Key="ShimmerGradient" StartPoint="0,0" EndPoint="1,0">
        <GradientStop Color="#00FFFFFF" Offset="0"/>
        <GradientStop Color="#40FFFFFF" Offset="0.5"/>
        <GradientStop Color="#00FFFFFF" Offset="1"/>
    </LinearGradientBrush>

    <!-- Overlay Gradient -->
    <LinearGradientBrush x:Key="OverlayGradient" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#00000000" Offset="0"/>
        <GradientStop Color="#80000000" Offset="1"/>
    </LinearGradientBrush>

    <!-- ========================================== -->
    <!-- Button Gradients -->
    <!-- ========================================== -->

    <!-- Button Normal Gradient -->
    <LinearGradientBrush x:Key="ButtonNormalGradient" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#2196F3" Offset="0"/>
        <GradientStop Color="#1976D2" Offset="1"/>
    </LinearGradientBrush>

    <!-- Button Hover Gradient -->
    <LinearGradientBrush x:Key="ButtonHoverGradient" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#42A5F5" Offset="0"/>
        <GradientStop Color="#2196F3" Offset="1"/>
    </LinearGradientBrush>

    <!-- Button Pressed Gradient -->
    <LinearGradientBrush x:Key="ButtonPressedGradient" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#1976D2" Offset="0"/>
        <GradientStop Color="#1565C0" Offset="1"/>
    </LinearGradientBrush>

    <!-- ========================================== -->
    <!-- Background Gradients -->
    <!-- ========================================== -->

    <!-- App Background Gradient (Light) -->
    <LinearGradientBrush x:Key="AppBackgroundLightGradient" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#FFFFFF" Offset="0"/>
        <GradientStop Color="#F8F9FA" Offset="1"/>
    </LinearGradientBrush>

    <!-- App Background Gradient (Dark) -->
    <LinearGradientBrush x:Key="AppBackgroundDarkGradient" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#1E1E1E" Offset="0"/>
        <GradientStop Color="#121212" Offset="1"/>
    </LinearGradientBrush>

    <!-- Card Background Gradient -->
    <LinearGradientBrush x:Key="CardBackgroundGradient" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#FFFFFF" Offset="0"/>
        <GradientStop Color="#FAFAFA" Offset="1"/>
    </LinearGradientBrush>

    <!-- ========================================== -->
    <!-- Acrylic Gradients -->
    <!-- ========================================== -->

    <!-- Light Acrylic Gradient -->
    <LinearGradientBrush x:Key="LightAcrylicGradient" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#F0FFFFFF" Offset="0"/>
        <GradientStop Color="#E0FFFFFF" Offset="1"/>
    </LinearGradientBrush>

    <!-- Dark Acrylic Gradient -->
    <LinearGradientBrush x:Key="DarkAcrylicGradient" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#F0000000" Offset="0"/>
        <GradientStop Color="#E0000000" Offset="1"/>
    </LinearGradientBrush>

</ResourceDictionary>
