<Page x:Class="FluentSystemDesign.Examples.Pages.Examples.ValueConvertersDemoPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:converters="clr-namespace:FluentSystemDesign.WPF.Converters;assembly=FluentSystemDesign.WPF"
      Title="值转换器演示">

    <Page.Resources>
        <!-- 转换器资源 -->
        <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter" />
        <converters:InverseBoolConverter x:Key="InverseBoolConverter" />
        <converters:BoolToOpacityConverter x:Key="BoolToOpacityConverter" />
        <converters:NumberToStringConverter x:Key="NumberToStringConverter" />
        <converters:PercentageConverter x:Key="PercentageConverter" />
        <converters:ThicknessConverter x:Key="ThicknessConverter" />
        <converters:ColorToBrushConverter x:Key="ColorToBrushConverter" />
        <converters:HexToColorConverter x:Key="HexToColorConverter" />
        <converters:ColorToContrastConverter x:Key="ColorToContrastConverter" />
        <converters:EnumToStringConverter x:Key="EnumToStringConverter" />
        <converters:EnumToBoolConverter x:Key="EnumToBoolConverter" />
        <converters:EnumToVisibilityConverter x:Key="EnumToVisibilityConverter" />
        <converters:CollectionToVisibilityConverter x:Key="CollectionToVisibilityConverter" />
        <converters:CountToVisibilityConverter x:Key="CountToVisibilityConverter" />
        <converters:IsNullOrEmptyConverter x:Key="IsNullOrEmptyConverter" />
        <converters:StringToUpperConverter x:Key="StringToUpperConverter" />
        <converters:StringToLowerConverter x:Key="StringToLowerConverter" />
        <converters:StringFormatConverter x:Key="StringFormatConverter" />
        <converters:ThemeToColorConverter x:Key="ThemeToColorConverter" />
        <converters:ThemeToBrushConverter x:Key="ThemeToBrushConverter" />
    </Page.Resources>

    <ScrollViewer>
        <StackPanel Margin="20">

            <!-- 页面标题 -->
            <TextBlock Text="值转换器演示" FontSize="32" FontWeight="Bold" Margin="0,0,0,20"/>

            <!-- 布尔转换器演示 -->
            <GroupBox Header="布尔转换器" Margin="0,0,0,20">
                <StackPanel Margin="10">
                    <CheckBox x:Name="BoolTestCheckBox" Content="测试布尔值" IsChecked="True" Margin="0,0,0,10"/>

                    <TextBlock Text="BoolToVisibilityConverter:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBlock Text="正常模式 - 当复选框选中时显示" Margin="0,0,0,5"
                               Visibility="{Binding IsChecked, ElementName=BoolTestCheckBox, Converter={StaticResource BoolToVisibilityConverter}}"/>
                    <TextBlock Text="反转模式 - 当复选框未选中时显示" Margin="0,0,0,10"
                               Visibility="{Binding IsChecked, ElementName=BoolTestCheckBox, Converter={StaticResource BoolToVisibilityConverter}, ConverterParameter=Invert}"/>

                    <TextBlock Text="InverseBoolConverter:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <Button Content="当复选框选中时禁用" Margin="0,0,0,10"
                            IsEnabled="{Binding IsChecked, ElementName=BoolTestCheckBox, Converter={StaticResource InverseBoolConverter}}"/>

                    <TextBlock Text="BoolToOpacityConverter:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <Rectangle Width="200" Height="30" Fill="Blue" Margin="0,0,0,10"
                               Opacity="{Binding IsChecked, ElementName=BoolTestCheckBox, Converter={StaticResource BoolToOpacityConverter}}"/>
                </StackPanel>
            </GroupBox>

            <!-- 数值转换器演示 -->
            <GroupBox Header="数值转换器" Margin="0,0,0,20">
                <StackPanel Margin="10">
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                        <TextBlock Text="数值:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <Slider x:Name="NumberSlider" Minimum="0" Maximum="1000" Value="123.45" Width="200" Margin="0,0,10,0"/>
                        <TextBlock Text="{Binding Value, ElementName=NumberSlider}" VerticalAlignment="Center"/>
                    </StackPanel>

                    <TextBlock Text="NumberToStringConverter (货币格式):" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBlock Text="{Binding Value, ElementName=NumberSlider, Converter={StaticResource NumberToStringConverter}, ConverterParameter=C2}" Margin="0,0,0,10"/>

                    <TextBlock Text="PercentageConverter:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBlock Text="{Binding Value, ElementName=NumberSlider, Converter={StaticResource PercentageConverter}, ConverterParameter=0-100}" Margin="0,0,0,10"/>
                </StackPanel>
            </GroupBox>

            <!-- 颜色转换器演示 -->
            <GroupBox Header="颜色转换器" Margin="0,0,0,20">
                <StackPanel Margin="10">
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                        <TextBlock Text="十六进制颜色:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox x:Name="HexColorTextBox" Text="#FF5722" Width="100"/>
                    </StackPanel>

                    <TextBlock Text="ColorToBrushConverter:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <Rectangle Width="200" Height="30" Margin="0,0,0,10"
                               Fill="{Binding Text, ElementName=HexColorTextBox, Converter={StaticResource ColorToBrushConverter}}"/>

                    <TextBlock Text="ColorToContrastConverter (自动对比色文本):" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <Border Width="200" Height="30" Margin="0,0,0,10"
                            Background="{Binding Text, ElementName=HexColorTextBox, Converter={StaticResource ColorToBrushConverter}}">
                        <TextBlock Text="自动对比色文本"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Foreground="{Binding Text, ElementName=HexColorTextBox, Converter={StaticResource ColorToContrastConverter}}"/>
                    </Border>
                </StackPanel>
            </GroupBox>

            <!-- 字符串转换器演示 -->
            <GroupBox Header="字符串转换器" Margin="0,0,0,20">
                <StackPanel Margin="10">
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                        <TextBlock Text="输入文本:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox x:Name="StringTestTextBox" Text="Hello World" Width="200"/>
                    </StackPanel>

                    <TextBlock Text="StringToUpperConverter:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBlock Text="{Binding Text, ElementName=StringTestTextBox, Converter={StaticResource StringToUpperConverter}}" Margin="0,0,0,5"/>
                    <TextBlock Text="{Binding Text, ElementName=StringTestTextBox, Converter={StaticResource StringToUpperConverter}, ConverterParameter=Words}" Margin="0,0,0,10"/>

                    <TextBlock Text="StringToLowerConverter:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBlock Text="{Binding Text, ElementName=StringTestTextBox, Converter={StaticResource StringToLowerConverter}}" Margin="0,0,0,10"/>

                    <TextBlock Text="StringFormatConverter:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBlock Text="{Binding Text, ElementName=StringTestTextBox, Converter={StaticResource StringFormatConverter}, ConverterParameter='格式化: {0}'}" Margin="0,0,0,10"/>
                </StackPanel>
            </GroupBox>

            <!-- 集合转换器演示 -->
            <GroupBox Header="集合转换器">
                <StackPanel Margin="10">
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                        <TextBlock Text="数量:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <Slider x:Name="CountSlider" Minimum="0" Maximum="10" Value="3" Width="200" Margin="0,0,10,0"/>
                        <TextBlock Text="{Binding Value, ElementName=CountSlider}" VerticalAlignment="Center"/>
                    </StackPanel>

                    <TextBlock Text="CountToVisibilityConverter:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBlock Text="大于0时显示此文本" Margin="0,0,0,5">
                        <TextBlock.Visibility>
                            <Binding Path="Value" ElementName="CountSlider" Converter="{StaticResource CountToVisibilityConverter}" ConverterParameter="&gt;0"/>
                        </TextBlock.Visibility>
                    </TextBlock>
                    <TextBlock Text="大于等于5时显示此文本" Margin="0,0,0,10">
                        <TextBlock.Visibility>
                            <Binding Path="Value" ElementName="CountSlider" Converter="{StaticResource CountToVisibilityConverter}" ConverterParameter="&gt;=5"/>
                        </TextBlock.Visibility>
                    </TextBlock>

                    <TextBlock Text="IsNullOrEmptyConverter:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBlock Text="当文本框有内容时显示"
                               Visibility="{Binding Text, ElementName=StringTestTextBox, Converter={StaticResource IsNullOrEmptyConverter}, ConverterParameter=Invert}"/>
                </StackPanel>
            </GroupBox>
            
        </StackPanel>
    </ScrollViewer>
</Page>
