using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Microsoft.Xaml.Behaviors;

namespace FluentSystemDesign.WPF.Behaviors
{
    /// <summary>
    /// 输入验证行为，为输入控件提供实时验证功能
    /// </summary>
    public class ValidationBehavior : Behavior<FrameworkElement>
    {
        #region 依赖属性

        /// <summary>
        /// 验证规则类型
        /// </summary>
        public static readonly DependencyProperty ValidationTypeProperty =
            DependencyProperty.Register(nameof(ValidationType), typeof(ValidationType), typeof(ValidationBehavior),
                new PropertyMetadata(ValidationType.None));

        /// <summary>
        /// 正则表达式模式
        /// </summary>
        public static readonly DependencyProperty RegexPatternProperty =
            DependencyProperty.Register(nameof(RegexPattern), typeof(string), typeof(ValidationBehavior),
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 最小长度
        /// </summary>
        public static readonly DependencyProperty MinLengthProperty =
            DependencyProperty.Register(nameof(MinLength), typeof(int), typeof(ValidationBehavior),
                new PropertyMetadata(0));

        /// <summary>
        /// 最大长度
        /// </summary>
        public static readonly DependencyProperty MaxLengthProperty =
            DependencyProperty.Register(nameof(MaxLength), typeof(int), typeof(ValidationBehavior),
                new PropertyMetadata(int.MaxValue));

        /// <summary>
        /// 是否必填
        /// </summary>
        public static readonly DependencyProperty IsRequiredProperty =
            DependencyProperty.Register(nameof(IsRequired), typeof(bool), typeof(ValidationBehavior),
                new PropertyMetadata(false));

        /// <summary>
        /// 错误消息
        /// </summary>
        public static readonly DependencyProperty ErrorMessageProperty =
            DependencyProperty.Register(nameof(ErrorMessage), typeof(string), typeof(ValidationBehavior),
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 是否实时验证
        /// </summary>
        public static readonly DependencyProperty ValidateOnTextChangedProperty =
            DependencyProperty.Register(nameof(ValidateOnTextChanged), typeof(bool), typeof(ValidationBehavior),
                new PropertyMetadata(true));

        /// <summary>
        /// 错误边框颜色
        /// </summary>
        public static readonly DependencyProperty ErrorBorderBrushProperty =
            DependencyProperty.Register(nameof(ErrorBorderBrush), typeof(Brush), typeof(ValidationBehavior),
                new PropertyMetadata(Brushes.Red));

        /// <summary>
        /// 正常边框颜色
        /// </summary>
        public static readonly DependencyProperty NormalBorderBrushProperty =
            DependencyProperty.Register(nameof(NormalBorderBrush), typeof(Brush), typeof(ValidationBehavior),
                new PropertyMetadata(null));

        #endregion

        #region 属性

        /// <summary>
        /// 获取或设置验证规则类型
        /// </summary>
        public ValidationType ValidationType
        {
            get => (ValidationType)GetValue(ValidationTypeProperty);
            set => SetValue(ValidationTypeProperty, value);
        }

        /// <summary>
        /// 获取或设置正则表达式模式
        /// </summary>
        public string RegexPattern
        {
            get => (string)GetValue(RegexPatternProperty);
            set => SetValue(RegexPatternProperty, value);
        }

        /// <summary>
        /// 获取或设置最小长度
        /// </summary>
        public int MinLength
        {
            get => (int)GetValue(MinLengthProperty);
            set => SetValue(MinLengthProperty, value);
        }

        /// <summary>
        /// 获取或设置最大长度
        /// </summary>
        public int MaxLength
        {
            get => (int)GetValue(MaxLengthProperty);
            set => SetValue(MaxLengthProperty, value);
        }

        /// <summary>
        /// 获取或设置是否必填
        /// </summary>
        public bool IsRequired
        {
            get => (bool)GetValue(IsRequiredProperty);
            set => SetValue(IsRequiredProperty, value);
        }

        /// <summary>
        /// 获取或设置错误消息
        /// </summary>
        public string ErrorMessage
        {
            get => (string)GetValue(ErrorMessageProperty);
            set => SetValue(ErrorMessageProperty, value);
        }

        /// <summary>
        /// 获取或设置是否实时验证
        /// </summary>
        public bool ValidateOnTextChanged
        {
            get => (bool)GetValue(ValidateOnTextChangedProperty);
            set => SetValue(ValidateOnTextChangedProperty, value);
        }

        /// <summary>
        /// 获取或设置错误边框颜色
        /// </summary>
        public Brush ErrorBorderBrush
        {
            get => (Brush)GetValue(ErrorBorderBrushProperty);
            set => SetValue(ErrorBorderBrushProperty, value);
        }

        /// <summary>
        /// 获取或设置正常边框颜色
        /// </summary>
        public Brush NormalBorderBrush
        {
            get => (Brush)GetValue(NormalBorderBrushProperty);
            set => SetValue(NormalBorderBrushProperty, value);
        }

        /// <summary>
        /// 获取当前验证状态
        /// </summary>
        public bool IsValid { get; private set; } = true;

        /// <summary>
        /// 获取当前错误信息
        /// </summary>
        public string CurrentError { get; private set; } = string.Empty;

        #endregion

        #region 事件

        /// <summary>
        /// 验证状态改变事件
        /// </summary>
        public event EventHandler<ValidationChangedEventArgs> ValidationChanged;

        #endregion

        #region 私有字段

        private Brush _originalBorderBrush;
        private readonly Dictionary<ValidationType, Regex> _regexCache = new Dictionary<ValidationType, Regex>();

        #endregion

        #region 重写方法

        /// <summary>
        /// 附加到关联对象时调用
        /// </summary>
        protected override void OnAttached()
        {
            base.OnAttached();
            
            if (AssociatedObject != null)
            {
                // 保存原始边框颜色
                if (AssociatedObject is Control control)
                {
                    _originalBorderBrush = NormalBorderBrush ?? control.BorderBrush;
                }

                // 绑定事件
                if (AssociatedObject is TextBox textBox)
                {
                    if (ValidateOnTextChanged)
                    {
                        textBox.TextChanged += OnTextChanged;
                    }
                    textBox.LostFocus += OnLostFocus;
                }
                else if (AssociatedObject is PasswordBox passwordBox)
                {
                    if (ValidateOnTextChanged)
                    {
                        passwordBox.PasswordChanged += OnPasswordChanged;
                    }
                    passwordBox.LostFocus += OnLostFocus;
                }

                // 初始化正则表达式缓存
                InitializeRegexCache();
            }
        }

        /// <summary>
        /// 从关联对象分离时调用
        /// </summary>
        protected override void OnDetaching()
        {
            if (AssociatedObject != null)
            {
                if (AssociatedObject is TextBox textBox)
                {
                    textBox.TextChanged -= OnTextChanged;
                    textBox.LostFocus -= OnLostFocus;
                }
                else if (AssociatedObject is PasswordBox passwordBox)
                {
                    passwordBox.PasswordChanged -= OnPasswordChanged;
                    passwordBox.LostFocus -= OnLostFocus;
                }
            }
            
            base.OnDetaching();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 手动验证
        /// </summary>
        /// <returns>验证是否通过</returns>
        public bool Validate()
        {
            var text = GetText();
            return ValidateText(text);
        }

        /// <summary>
        /// 清除验证状态
        /// </summary>
        public void ClearValidation()
        {
            IsValid = true;
            CurrentError = string.Empty;
            UpdateVisualState();
            ValidationChanged?.Invoke(this, new ValidationChangedEventArgs(true, string.Empty));
        }

        #endregion

        #region 事件处理

        private void OnTextChanged(object sender, TextChangedEventArgs e)
        {
            if (ValidateOnTextChanged)
            {
                var text = GetText();
                ValidateText(text);
            }
        }

        private void OnPasswordChanged(object sender, RoutedEventArgs e)
        {
            if (ValidateOnTextChanged)
            {
                var text = GetText();
                ValidateText(text);
            }
        }

        private void OnLostFocus(object sender, RoutedEventArgs e)
        {
            var text = GetText();
            ValidateText(text);
        }

        #endregion

        #region 私有方法

        private string GetText()
        {
            return AssociatedObject switch
            {
                TextBox textBox => textBox.Text ?? string.Empty,
                PasswordBox passwordBox => passwordBox.Password ?? string.Empty,
                _ => string.Empty
            };
        }

        private bool ValidateText(string text)
        {
            var errors = new List<string>();

            // 必填验证
            if (IsRequired && string.IsNullOrWhiteSpace(text))
            {
                errors.Add("此字段为必填项");
            }

            if (!string.IsNullOrEmpty(text))
            {
                // 长度验证
                if (text.Length < MinLength)
                {
                    errors.Add($"最少需要 {MinLength} 个字符");
                }

                if (text.Length > MaxLength)
                {
                    errors.Add($"最多允许 {MaxLength} 个字符");
                }

                // 类型验证
                if (ValidationType != ValidationType.None)
                {
                    if (!ValidateByType(text, ValidationType))
                    {
                        errors.Add(GetValidationErrorMessage(ValidationType));
                    }
                }

                // 自定义正则验证
                if (!string.IsNullOrEmpty(RegexPattern))
                {
                    if (!Regex.IsMatch(text, RegexPattern))
                    {
                        errors.Add(string.IsNullOrEmpty(ErrorMessage) ? "格式不正确" : ErrorMessage);
                    }
                }
            }

            var isValid = errors.Count == 0;
            var errorMessage = string.Join("; ", errors);

            // 更新状态
            IsValid = isValid;
            CurrentError = errorMessage;
            UpdateVisualState();

            // 触发事件
            ValidationChanged?.Invoke(this, new ValidationChangedEventArgs(isValid, errorMessage));

            return isValid;
        }

        private bool ValidateByType(string text, ValidationType type)
        {
            if (_regexCache.TryGetValue(type, out var regex))
            {
                return regex.IsMatch(text);
            }
            return true;
        }

        private void UpdateVisualState()
        {
            if (AssociatedObject is Control control)
            {
                control.BorderBrush = IsValid ? _originalBorderBrush : ErrorBorderBrush;
                
                // 设置ToolTip显示错误信息
                if (!IsValid && !string.IsNullOrEmpty(CurrentError))
                {
                    control.ToolTip = CurrentError;
                }
                else
                {
                    control.ToolTip = null;
                }
            }
        }

        private void InitializeRegexCache()
        {
            _regexCache[ValidationType.Email] = new Regex(@"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", RegexOptions.Compiled);
            _regexCache[ValidationType.Phone] = new Regex(@"^1[3-9]\d{9}$", RegexOptions.Compiled);
            _regexCache[ValidationType.Number] = new Regex(@"^\d+$", RegexOptions.Compiled);
            _regexCache[ValidationType.Decimal] = new Regex(@"^\d+(\.\d+)?$", RegexOptions.Compiled);
            _regexCache[ValidationType.Url] = new Regex(@"^https?://[^\s/$.?#].[^\s]*$", RegexOptions.Compiled | RegexOptions.IgnoreCase);
            _regexCache[ValidationType.Chinese] = new Regex(@"^[\u4e00-\u9fa5]+$", RegexOptions.Compiled);
            _regexCache[ValidationType.English] = new Regex(@"^[a-zA-Z]+$", RegexOptions.Compiled);
            _regexCache[ValidationType.AlphaNumeric] = new Regex(@"^[a-zA-Z0-9]+$", RegexOptions.Compiled);
        }

        private string GetValidationErrorMessage(ValidationType type)
        {
            return type switch
            {
                ValidationType.Email => "请输入有效的邮箱地址",
                ValidationType.Phone => "请输入有效的手机号码",
                ValidationType.Number => "请输入数字",
                ValidationType.Decimal => "请输入有效的数值",
                ValidationType.Url => "请输入有效的网址",
                ValidationType.Chinese => "请输入中文字符",
                ValidationType.English => "请输入英文字符",
                ValidationType.AlphaNumeric => "请输入字母或数字",
                _ => "格式不正确"
            };
        }

        #endregion
    }

    /// <summary>
    /// 验证类型枚举
    /// </summary>
    public enum ValidationType
    {
        /// <summary>
        /// 无验证
        /// </summary>
        None,
        
        /// <summary>
        /// 邮箱验证
        /// </summary>
        Email,
        
        /// <summary>
        /// 手机号验证
        /// </summary>
        Phone,
        
        /// <summary>
        /// 数字验证
        /// </summary>
        Number,
        
        /// <summary>
        /// 小数验证
        /// </summary>
        Decimal,
        
        /// <summary>
        /// 网址验证
        /// </summary>
        Url,
        
        /// <summary>
        /// 中文验证
        /// </summary>
        Chinese,
        
        /// <summary>
        /// 英文验证
        /// </summary>
        English,
        
        /// <summary>
        /// 字母数字验证
        /// </summary>
        AlphaNumeric
    }

    /// <summary>
    /// 验证状态改变事件参数
    /// </summary>
    public class ValidationChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 是否验证通过
        /// </summary>
        public bool IsValid { get; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="isValid">是否验证通过</param>
        /// <param name="errorMessage">错误消息</param>
        public ValidationChangedEventArgs(bool isValid, string errorMessage)
        {
            IsValid = isValid;
            ErrorMessage = errorMessage;
        }
    }
}
