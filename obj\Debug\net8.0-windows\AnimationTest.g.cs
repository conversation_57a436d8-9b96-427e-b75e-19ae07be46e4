﻿#pragma checksum "..\..\..\AnimationTest.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "EB28EB9113DA36FD47F0494B3D7CE054F8C82BCB"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using FluentSystemDesign.WPF.Behaviors;
using Microsoft.Xaml.Behaviors;
using Microsoft.Xaml.Behaviors.Core;
using Microsoft.Xaml.Behaviors.Input;
using Microsoft.Xaml.Behaviors.Layout;
using Microsoft.Xaml.Behaviors.Media;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;




/// <summary>
/// AnimationTest
/// </summary>
public partial class AnimationTest : System.Windows.Window, System.Windows.Markup.IComponentConnector {
    
    
    #line 15 "..\..\..\AnimationTest.xaml"
    [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
    internal System.Windows.Controls.Button SlideLeftBtn;
    
    #line default
    #line hidden
    
    
    #line 16 "..\..\..\AnimationTest.xaml"
    [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
    internal System.Windows.Controls.Button SlideRightBtn;
    
    #line default
    #line hidden
    
    
    #line 17 "..\..\..\AnimationTest.xaml"
    [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
    internal System.Windows.Controls.Button SlideTopBtn;
    
    #line default
    #line hidden
    
    
    #line 18 "..\..\..\AnimationTest.xaml"
    [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
    internal System.Windows.Controls.Button SlideBottomBtn;
    
    #line default
    #line hidden
    
    
    #line 19 "..\..\..\AnimationTest.xaml"
    [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
    internal System.Windows.Controls.Button ScaleInBtn;
    
    #line default
    #line hidden
    
    
    #line 20 "..\..\..\AnimationTest.xaml"
    [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
    internal System.Windows.Controls.Button FadeInBtn;
    
    #line default
    #line hidden
    
    
    #line 25 "..\..\..\AnimationTest.xaml"
    [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
    internal System.Windows.Controls.Border AnimationTarget;
    
    #line default
    #line hidden
    
    private bool _contentLoaded;
    
    /// <summary>
    /// InitializeComponent
    /// </summary>
    [System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
    public void InitializeComponent() {
        if (_contentLoaded) {
            return;
        }
        _contentLoaded = true;
        System.Uri resourceLocater = new System.Uri("/AnimationTest;component/animationtest.xaml", System.UriKind.Relative);
        
        #line 1 "..\..\..\AnimationTest.xaml"
        System.Windows.Application.LoadComponent(this, resourceLocater);
        
        #line default
        #line hidden
    }
    
    [System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
    [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
    [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
    [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
    void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
        this.SlideLeftBtn = ((System.Windows.Controls.Button)(target));
        
        #line 15 "..\..\..\AnimationTest.xaml"
        this.SlideLeftBtn.Click += new System.Windows.RoutedEventHandler(this.SlideLeftBtn_Click);
        
        #line default
        #line hidden
        return;
            case 2:
        this.SlideRightBtn = ((System.Windows.Controls.Button)(target));
        
        #line 16 "..\..\..\AnimationTest.xaml"
        this.SlideRightBtn.Click += new System.Windows.RoutedEventHandler(this.SlideRightBtn_Click);
        
        #line default
        #line hidden
        return;
            case 3:
        this.SlideTopBtn = ((System.Windows.Controls.Button)(target));
        
        #line 17 "..\..\..\AnimationTest.xaml"
        this.SlideTopBtn.Click += new System.Windows.RoutedEventHandler(this.SlideTopBtn_Click);
        
        #line default
        #line hidden
        return;
            case 4:
        this.SlideBottomBtn = ((System.Windows.Controls.Button)(target));
        
        #line 18 "..\..\..\AnimationTest.xaml"
        this.SlideBottomBtn.Click += new System.Windows.RoutedEventHandler(this.SlideBottomBtn_Click);
        
        #line default
        #line hidden
        return;
            case 5:
        this.ScaleInBtn = ((System.Windows.Controls.Button)(target));
        
        #line 19 "..\..\..\AnimationTest.xaml"
        this.ScaleInBtn.Click += new System.Windows.RoutedEventHandler(this.ScaleInBtn_Click);
        
        #line default
        #line hidden
        return;
            case 6:
        this.FadeInBtn = ((System.Windows.Controls.Button)(target));
        
        #line 20 "..\..\..\AnimationTest.xaml"
        this.FadeInBtn.Click += new System.Windows.RoutedEventHandler(this.FadeInBtn_Click);
        
        #line default
        #line hidden
        return;
            case 7:
        this.AnimationTarget = ((System.Windows.Controls.Border)(target));
        return;
            }
        this._contentLoaded = true;
    }
}

