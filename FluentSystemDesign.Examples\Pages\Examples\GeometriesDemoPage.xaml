<Page x:Class="FluentSystemDesign.Examples.Pages.Examples.GeometriesDemoPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      mc:Ignorable="d" 
      d:DesignHeight="800" d:DesignWidth="1200"
      Title="几何形状展示">

    <Page.Resources>
        <!-- 几何形状展示项样式 -->
        <Style x:Key="GeometryItemStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="MinWidth" Value="120"/>
            <Setter Property="MinHeight" Value="120"/>
            <Setter Property="Effect" Value="{StaticResource Elevation2}"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Effect" Value="{StaticResource Elevation4}"/>
                    <Setter Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 几何形状路径样式 -->
        <Style x:Key="GeometryPathStyle" TargetType="Path">
            <Setter Property="Fill" Value="{StaticResource AccentBrush}"/>
            <Setter Property="Stretch" Value="Uniform"/>
            <Setter Property="Width" Value="32"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- 分类标题样式 -->
        <Style x:Key="CategoryTitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="20"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
            <Setter Property="Margin" Value="0,24,0,16"/>
        </Style>

        <!-- 几何形状名称样式 -->
        <Style x:Key="GeometryNameStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Margin" Value="0,8,0,0"/>
        </Style>
    </Page.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 页面标题 -->
        <Border Grid.Row="0" 
                Background="{StaticResource SurfaceBrush}" 
                BorderBrush="{StaticResource BorderBrush}" 
                BorderThickness="0,0,0,1" 
                Padding="24,16">
            <StackPanel>
                <TextBlock Text="几何形状资源展示" 
                           FontSize="28" 
                           FontWeight="Bold" 
                           Foreground="{StaticResource TextPrimaryBrush}"/>
                <TextBlock Text="FluentSystemDesign WPF控件库提供的完整几何形状资源系统" 
                           FontSize="14" 
                           Foreground="{StaticResource TextSecondaryBrush}" 
                           Margin="0,4,0,0"/>
            </StackPanel>
        </Border>

        <!-- 几何形状展示区域 -->
        <ScrollViewer Grid.Row="1" 
                      VerticalScrollBarVisibility="Auto" 
                      HorizontalScrollBarVisibility="Disabled"
                      Padding="24">
            <StackPanel>

                <!-- 基础几何形状 -->
                <TextBlock Text="基础几何形状" Style="{StaticResource CategoryTitleStyle}"/>
                <WrapPanel>
                    <!-- 圆形 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource CircleGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="CircleGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 小圆形 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource SmallCircleGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="SmallCircleGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 大圆形 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource LargeCircleGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="LargeCircleGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 矩形 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource RectangleGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="RectangleGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 圆角矩形 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource RoundedRectangleGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="RoundedRectangleGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 正方形 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource SquareGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="SquareGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 向上三角形 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource TriangleUpGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="TriangleUpGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 向下三角形 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource TriangleDownGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="TriangleDownGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 向左三角形 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource TriangleLeftGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="TriangleLeftGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 向右三角形 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource TriangleRightGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="TriangleRightGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 等边三角形 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource EquilateralTriangleGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="EquilateralTriangleGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 五边形 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource PentagonGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="PentagonGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 六边形 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource HexagonGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="HexagonGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 八边形 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource OctagonGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="OctagonGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 星形 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource StarGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="StarGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 五角星 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource FivePointStarGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="FivePointStarGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 菱形 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource DiamondGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="DiamondGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 心形 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource HeartGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="HeartGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>
                </WrapPanel>

                <!-- 图标几何形状 -->
                <TextBlock Text="图标几何形状" Style="{StaticResource CategoryTitleStyle}"/>
                <WrapPanel>
                    <!-- 向右箭头 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource ChevronRightGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="ChevronRightGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 向左箭头 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource ChevronLeftGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="ChevronLeftGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 向上箭头 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource ChevronUpGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="ChevronUpGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 向下箭头 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource ChevronDownGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="ChevronDownGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 勾选 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource CheckMarkGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource SuccessBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="CheckMarkGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 关闭 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource CloseGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource ErrorBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="CloseGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 加号 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource PlusGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="PlusGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 减号 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource MinusGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="MinusGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 菜单 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource MenuGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="MenuGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 搜索 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource SearchGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="SearchGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 设置 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource SettingsGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="SettingsGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 信息 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource InfoGeometry}" Style="{StaticResource GeometryPathStyle}" Fill="{StaticResource InfoBrush}"/>
                            <TextBlock Text="InfoGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 警告 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource WarningGeometry}" Style="{StaticResource GeometryPathStyle}" Fill="{StaticResource WarningBrush}"/>
                            <TextBlock Text="WarningGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 错误 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource ErrorGeometry}" Style="{StaticResource GeometryPathStyle}" Fill="{StaticResource ErrorBrush}"/>
                            <TextBlock Text="ErrorGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 成功 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource SuccessGeometry}" Style="{StaticResource GeometryPathStyle}" Fill="{StaticResource SuccessBrush}"/>
                            <TextBlock Text="SuccessGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 主页 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource HomeGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="HomeGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 用户 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource UserGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="UserGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 文件 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource FileGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="FileGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 文件夹 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource FolderGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="FolderGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>
                </WrapPanel>

                <!-- 装饰几何形状 -->
                <TextBlock Text="装饰几何形状" Style="{StaticResource CategoryTitleStyle}"/>
                <WrapPanel>
                    <!-- 水平分隔线 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource HorizontalSeparatorGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource BorderBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="HorizontalSeparatorGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 垂直分隔线 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource VerticalSeparatorGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource BorderBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="VerticalSeparatorGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 圆形边框 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource CircleBorderGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="CircleBorderGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 圆角矩形边框 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource RoundedRectangleBorderGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="RoundedRectangleBorderGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 三个装饰点 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource ThreeDotsGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="ThreeDotsGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 垂直三个装饰点 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource VerticalThreeDotsGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="VerticalThreeDotsGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 波浪线 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource WaveLineGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="WaveLineGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 锯齿线 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource ZigzagLineGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="ZigzagLineGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 展开箭头 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource ExpandArrowGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="ExpandArrowGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 折叠箭头 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource CollapseArrowGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="CollapseArrowGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>
                </WrapPanel>

                <!-- 路径几何形状 -->
                <TextBlock Text="路径几何形状" Style="{StaticResource CategoryTitleStyle}"/>
                <WrapPanel>
                    <!-- 云朵 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource CloudGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="CloudGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 邮件 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource MailGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="MailGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 日历 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource CalendarGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="CalendarGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 时钟 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource ClockGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="ClockGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 位置 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource LocationGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="LocationGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 购物车 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource ShoppingCartGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="ShoppingCartGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 音乐 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource MusicGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="MusicGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 相机 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource CameraGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="CameraGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 视频 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource VideoGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="VideoGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 图片 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource ImageGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="ImageGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 下载 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource DownloadGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="DownloadGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 上传 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource UploadGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="UploadGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 分享 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource ShareGeometry}" Style="{StaticResource GeometryPathStyle}"/>
                            <TextBlock Text="ShareGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 链接 -->
                    <Border Style="{StaticResource GeometryItemStyle}">
                        <StackPanel>
                            <Path Data="{StaticResource LinkGeometry}" Style="{StaticResource GeometryPathStyle}" Stroke="{StaticResource AccentBrush}" StrokeThickness="2" Fill="Transparent"/>
                            <TextBlock Text="LinkGeometry" Style="{StaticResource GeometryNameStyle}"/>
                        </StackPanel>
                    </Border>
                </WrapPanel>

            </StackPanel>
        </ScrollViewer>
    </Grid>
</Page>
