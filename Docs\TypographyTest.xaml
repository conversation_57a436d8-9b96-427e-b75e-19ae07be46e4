<UserControl x:Class="FluentSystemDesign.Examples.TypographyTest"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <!-- 引用字体系统资源 -->
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FluentSystemDesign.WPF;component/Themes/Styles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <ScrollViewer>
        <StackPanel Margin="24">
            
            <!-- 页面标题 -->
            <TextBlock Style="{StaticResource H1TextStyle}" 
                       Text="FluentSystemDesign 字体系统测试"/>
            
            <TextBlock Style="{StaticResource Body1TextStyle}" 
                       Text="本页面展示了FluentSystemDesign WPF控件库的完整字体系统，包括各种字体层级、样式和用法示例。"/>

            <!-- Display 样式 -->
            <Border Background="{StaticResource SurfaceBrush}" 
                    Padding="16" 
                    CornerRadius="{StaticResource MediumCornerRadius}"
                    Margin="0,16,0,0">
                <StackPanel>
                    <TextBlock Style="{StaticResource H3TextStyle}" 
                               Text="Display 样式" 
                               Margin="0,0,0,8"/>
                    <TextBlock Style="{StaticResource DisplayTextStyle}" 
                               Text="FluentSystemDesign"/>
                    <TextBlock Style="{StaticResource CaptionTextStyle}" 
                               Text="用于品牌标题和重要展示文字 - 72px, Light"/>
                </StackPanel>
            </Border>

            <!-- 标题层级 -->
            <Border Background="{StaticResource SurfaceBrush}" 
                    Padding="16" 
                    CornerRadius="{StaticResource MediumCornerRadius}">
                <StackPanel>
                    <TextBlock Style="{StaticResource H3TextStyle}" 
                               Text="标题层级 (H1-H6)" 
                               Margin="0,0,0,16"/>
                    
                    <TextBlock Style="{StaticResource H1TextStyle}" Text="H1 主标题 - 40px Light"/>
                    <TextBlock Style="{StaticResource H2TextStyle}" Text="H2 次级标题 - 32px Light"/>
                    <TextBlock Style="{StaticResource H3TextStyle}" Text="H3 三级标题 - 28px Normal"/>
                    <TextBlock Style="{StaticResource H4TextStyle}" Text="H4 四级标题 - 24px Normal"/>
                    <TextBlock Style="{StaticResource H5TextStyle}" Text="H5 五级标题 - 20px Normal"/>
                    <TextBlock Style="{StaticResource H6TextStyle}" Text="H6 六级标题 - 18px Medium"/>
                </StackPanel>
            </Border>

            <!-- 正文层级 -->
            <Border Background="{StaticResource SurfaceBrush}" 
                    Padding="16" 
                    CornerRadius="{StaticResource MediumCornerRadius}">
                <StackPanel>
                    <TextBlock Style="{StaticResource H3TextStyle}" 
                               Text="正文层级" 
                               Margin="0,0,0,16"/>
                    
                    <TextBlock Style="{StaticResource Body1TextStyle}" 
                               Text="Body1 主要正文 - 16px Normal - 用于重要内容和主要描述文字，具有良好的可读性和视觉权重。"/>
                    
                    <TextBlock Style="{StaticResource Body2TextStyle}" 
                               Text="Body2 标准正文 - 14px Normal - 用于一般内容和说明文字，是最常用的正文样式。"
                               Margin="0,8,0,0"/>
                    
                    <TextBlock Style="{StaticResource Body3TextStyle}" 
                               Text="Body3 次要正文 - 12px Normal - 用于补充信息和次要描述文字。"
                               Margin="0,8,0,0"/>
                </StackPanel>
            </Border>

            <!-- 标签和说明 -->
            <Border Background="{StaticResource SurfaceBrush}" 
                    Padding="16" 
                    CornerRadius="{StaticResource MediumCornerRadius}">
                <StackPanel>
                    <TextBlock Style="{StaticResource H3TextStyle}" 
                               Text="标签和说明" 
                               Margin="0,0,0,16"/>
                    
                    <TextBlock Style="{StaticResource LabelTextStyle}" 
                               Text="Label 标签文字 - 14px Medium"/>
                    
                    <TextBlock Style="{StaticResource CaptionTextStyle}" 
                               Text="Caption 说明文字 - 12px Normal - 用于图片说明或补充信息"
                               Margin="0,8,0,0"/>
                    
                    <TextBlock Style="{StaticResource OverlineTextStyle}"
                               Text="OVERLINE 上标文字 - 10px Medium"
                               Margin="0,8,0,0"/>
                </StackPanel>
            </Border>

            <!-- 按钮字体 -->
            <Border Background="{StaticResource SurfaceBrush}" 
                    Padding="16" 
                    CornerRadius="{StaticResource MediumCornerRadius}">
                <StackPanel>
                    <TextBlock Style="{StaticResource H3TextStyle}" 
                               Text="按钮字体样式" 
                               Margin="0,0,0,16"/>
                    
                    <StackPanel Orientation="Horizontal">
                        <Border Background="{StaticResource PrimaryBrush}" 
                                Padding="16,8" 
                                CornerRadius="{StaticResource SmallCornerRadius}">
                            <TextBlock Style="{StaticResource ButtonLargeTextStyle}" 
                                       Text="大按钮 16px" 
                                       Foreground="{StaticResource TextOnPrimaryBrush}"/>
                        </Border>
                        
                        <Border Background="{StaticResource SecondaryBrush}" 
                                Padding="12,6" 
                                CornerRadius="{StaticResource SmallCornerRadius}">
                            <TextBlock Style="{StaticResource ButtonTextStyle}" 
                                       Text="标准按钮 14px" 
                                       Foreground="{StaticResource TextOnSecondaryBrush}"/>
                        </Border>
                        
                        <Border Background="{StaticResource AccentBrush}" 
                                Padding="8,4" 
                                CornerRadius="{StaticResource SmallCornerRadius}">
                            <TextBlock Style="{StaticResource ButtonSmallTextStyle}" 
                                       Text="小按钮 12px" 
                                       Foreground="{StaticResource TextOnAccentBrush}"/>
                        </Border>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- 状态字体 -->
            <Border Background="{StaticResource SurfaceBrush}" 
                    Padding="16" 
                    CornerRadius="{StaticResource MediumCornerRadius}">
                <StackPanel>
                    <TextBlock Style="{StaticResource H3TextStyle}" 
                               Text="状态字体样式" 
                               Margin="0,0,0,16"/>
                    
                    <TextBlock Style="{StaticResource SuccessTextStyle}" 
                               Text="✓ 成功状态文字"/>
                    
                    <TextBlock Style="{StaticResource WarningTextStyle}" 
                               Text="⚠ 警告状态文字"
                               Margin="0,4,0,0"/>
                    
                    <TextBlock Style="{StaticResource ErrorTextStyle}" 
                               Text="✗ 错误状态文字"
                               Margin="0,4,0,0"/>
                    
                    <TextBlock Style="{StaticResource InfoTextStyle}" 
                               Text="ℹ 信息状态文字"
                               Margin="0,4,0,0"/>
                </StackPanel>
            </Border>

            <!-- 特殊样式 -->
            <Border Background="{StaticResource SurfaceBrush}" 
                    Padding="16" 
                    CornerRadius="{StaticResource MediumCornerRadius}">
                <StackPanel>
                    <TextBlock Style="{StaticResource H3TextStyle}" 
                               Text="特殊字体样式" 
                               Margin="0,0,0,16"/>
                    
                    <TextBlock Style="{StaticResource LinkTextStyle}" 
                               Text="链接文字样式"/>
                    
                    <TextBlock Style="{StaticResource DisabledTextStyle}" 
                               Text="禁用文字样式"
                               Margin="0,8,0,0"/>
                    
                    <TextBlock Style="{StaticResource MonospaceTextStyle}" 
                               Text="var code = 'Monospace Font Style';"
                               Margin="0,8,0,0"/>
                    
                    <Border Background="{StaticResource PrimaryBrush}" 
                            Padding="8" 
                            CornerRadius="{StaticResource SmallCornerRadius}"
                            Margin="0,8,0,0">
                        <TextBlock Style="{StaticResource TooltipTextStyle}" 
                                   Text="工具提示文字样式 - 用于悬浮提示信息"/>
                    </Border>
                </StackPanel>
            </Border>

            <!-- 文本对齐 -->
            <Border Background="{StaticResource SurfaceBrush}"
                    Padding="16"
                    CornerRadius="{StaticResource MediumCornerRadius}">
                <StackPanel>
                    <TextBlock Style="{StaticResource H3TextStyle}"
                               Text="文本对齐样式"
                               Margin="0,0,0,16"/>

                    <TextBlock Style="{StaticResource Body2TextStyle}"
                               Text="左对齐文本 - 默认对齐方式"/>

                    <TextBlock Style="{StaticResource Body2TextStyle}"
                               Text="居中对齐文本"
                               TextAlignment="Center"
                               Margin="0,4,0,0"/>

                    <TextBlock Style="{StaticResource Body2TextStyle}"
                               Text="右对齐文本"
                               TextAlignment="Right"
                               Margin="0,4,0,0"/>
                </StackPanel>
            </Border>

            <!-- 文本转换示例 -->
            <Border Background="{StaticResource SurfaceBrush}"
                    Padding="16"
                    CornerRadius="{StaticResource MediumCornerRadius}">
                <StackPanel>
                    <TextBlock Style="{StaticResource H3TextStyle}"
                               Text="文本大小写转换"
                               Margin="0,0,0,16"/>

                    <TextBlock Style="{StaticResource Body2TextStyle}"
                               Text="原始文本: FluentSystemDesign Typography System"/>

                    <TextBlock Style="{StaticResource Body2TextStyle}"
                               Text="{Binding Source='FluentSystemDesign Typography System', Converter={StaticResource TextCaseConverter}, ConverterParameter=upper}"
                               Margin="0,4,0,0"/>

                    <TextBlock Style="{StaticResource Body2TextStyle}"
                               Text="{Binding Source='FluentSystemDesign Typography System', Converter={StaticResource TextCaseConverter}, ConverterParameter=lower}"
                               Margin="0,4,0,0"/>

                    <TextBlock Style="{StaticResource Body2TextStyle}"
                               Text="{Binding Source='FluentSystemDesign Typography System', Converter={StaticResource TextCaseConverter}, ConverterParameter=title}"
                               Margin="0,4,0,0"/>
                </StackPanel>
            </Border>

            <!-- 使用说明 -->
            <Border Background="{StaticResource InfoLightBrush}" 
                    BorderBrush="{StaticResource InfoBrush}"
                    BorderThickness="1"
                    Padding="16" 
                    CornerRadius="{StaticResource MediumCornerRadius}">
                <StackPanel>
                    <TextBlock Style="{StaticResource H4TextStyle}" 
                               Text="使用说明" 
                               Foreground="{StaticResource InfoTextBrush}"
                               Margin="0,0,0,8"/>
                    
                    <TextBlock Style="{StaticResource Body2TextStyle}" 
                               Foreground="{StaticResource InfoTextBrush}"
                               TextWrapping="Wrap">
                        <Run Text="所有字体样式都定义在 Typography.xaml 资源字典中，通过 StaticResource 引用使用。"/>
                        <LineBreak/>
                        <Run Text="字体系统支持深色和浅色主题的自动适配，文本颜色会根据当前主题自动调整。"/>
                        <LineBreak/>
                        <Run Text="详细的使用方法和API文档请参阅 Docs/Typography.md 文件。"/>
                    </TextBlock>
                </StackPanel>
            </Border>

        </StackPanel>
    </ScrollViewer>
</UserControl>
