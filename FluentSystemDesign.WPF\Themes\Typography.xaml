<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:converters="clr-namespace:FluentSystemDesign.WPF.Converters">

    <!-- ========================================== -->
    <!-- FluentSystemDesign Typography System -->
    <!-- 基于Microsoft Fluent Design System的字体规范 -->
    <!-- ========================================== -->

    <!-- ========================================== -->
    <!-- Converters -->
    <!-- ========================================== -->

    <!-- 文本大小写转换器 -->
    <converters:TextCaseConverter x:Key="TextCaseConverter"/>

    <!-- ========================================== -->
    <!-- Font Families -->
    <!-- ========================================== -->
    
    <!-- 主要字体族 - Segoe UI (Windows默认) -->
    <FontFamily x:Key="PrimaryFontFamily">Segoe UI</FontFamily>
    
    <!-- 备用字体族 -->
    <FontFamily x:Key="SecondaryFontFamily">Microsoft YaHei UI, SimSun</FontFamily>
    
    <!-- 等宽字体族 - 用于代码显示 -->
    <FontFamily x:Key="MonospaceFontFamily">Consolas, Courier New</FontFamily>
    
    <!-- 装饰字体族 - 用于特殊场合 -->
    <FontFamily x:Key="DisplayFontFamily">Segoe UI Light</FontFamily>

    <!-- ========================================== -->
    <!-- Font Sizes -->
    <!-- ========================================== -->
    
    <!-- 基础字体大小 -->
    <system:Double x:Key="BaseFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">14</system:Double>
    
    <!-- 标题字体大小 -->
    <system:Double x:Key="DisplayFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">72</system:Double>
    <system:Double x:Key="H1FontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">40</system:Double>
    <system:Double x:Key="H2FontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">32</system:Double>
    <system:Double x:Key="H3FontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">28</system:Double>
    <system:Double x:Key="H4FontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">24</system:Double>
    <system:Double x:Key="H5FontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">20</system:Double>
    <system:Double x:Key="H6FontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">18</system:Double>
    
    <!-- 正文字体大小 -->
    <system:Double x:Key="Body1FontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">16</system:Double>
    <system:Double x:Key="Body2FontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">14</system:Double>
    <system:Double x:Key="Body3FontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">12</system:Double>
    
    <!-- 标签和说明字体大小 -->
    <system:Double x:Key="CaptionFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">12</system:Double>
    <system:Double x:Key="LabelFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">14</system:Double>
    <system:Double x:Key="OverlineFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">10</system:Double>
    
    <!-- 按钮字体大小 -->
    <system:Double x:Key="ButtonFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">14</system:Double>
    <system:Double x:Key="ButtonSmallFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">12</system:Double>
    <system:Double x:Key="ButtonLargeFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">16</system:Double>

    <!-- ========================================== -->
    <!-- Font Weights -->
    <!-- ========================================== -->
    
    <FontWeight x:Key="ThinFontWeight">Thin</FontWeight>
    <FontWeight x:Key="ExtraLightFontWeight">ExtraLight</FontWeight>
    <FontWeight x:Key="LightFontWeight">Light</FontWeight>
    <FontWeight x:Key="NormalFontWeight">Normal</FontWeight>
    <FontWeight x:Key="MediumFontWeight">Medium</FontWeight>
    <FontWeight x:Key="SemiBoldFontWeight">SemiBold</FontWeight>
    <FontWeight x:Key="BoldFontWeight">Bold</FontWeight>
    <FontWeight x:Key="ExtraBoldFontWeight">ExtraBold</FontWeight>
    <FontWeight x:Key="BlackFontWeight">Black</FontWeight>

    <!-- ========================================== -->
    <!-- Line Heights -->
    <!-- ========================================== -->
    
    <!-- 标题行高 -->
    <system:Double x:Key="DisplayLineHeight" xmlns:system="clr-namespace:System;assembly=mscorlib">1.2</system:Double>
    <system:Double x:Key="H1LineHeight" xmlns:system="clr-namespace:System;assembly=mscorlib">1.2</system:Double>
    <system:Double x:Key="H2LineHeight" xmlns:system="clr-namespace:System;assembly=mscorlib">1.25</system:Double>
    <system:Double x:Key="H3LineHeight" xmlns:system="clr-namespace:System;assembly=mscorlib">1.3</system:Double>
    <system:Double x:Key="H4LineHeight" xmlns:system="clr-namespace:System;assembly=mscorlib">1.35</system:Double>
    <system:Double x:Key="H5LineHeight" xmlns:system="clr-namespace:System;assembly=mscorlib">1.4</system:Double>
    <system:Double x:Key="H6LineHeight" xmlns:system="clr-namespace:System;assembly=mscorlib">1.45</system:Double>
    
    <!-- 正文行高 -->
    <system:Double x:Key="Body1LineHeight" xmlns:system="clr-namespace:System;assembly=mscorlib">1.5</system:Double>
    <system:Double x:Key="Body2LineHeight" xmlns:system="clr-namespace:System;assembly=mscorlib">1.5</system:Double>
    <system:Double x:Key="Body3LineHeight" xmlns:system="clr-namespace:System;assembly=mscorlib">1.4</system:Double>
    
    <!-- 标签行高 -->
    <system:Double x:Key="CaptionLineHeight" xmlns:system="clr-namespace:System;assembly=mscorlib">1.4</system:Double>
    <system:Double x:Key="LabelLineHeight" xmlns:system="clr-namespace:System;assembly=mscorlib">1.4</system:Double>
    <system:Double x:Key="OverlineLineHeight" xmlns:system="clr-namespace:System;assembly=mscorlib">1.6</system:Double>

    <!-- ========================================== -->
    <!-- Letter Spacing -->
    <!-- ========================================== -->
    
    <!-- 字符间距（以em为单位转换为像素） -->
    <system:Double x:Key="DisplayLetterSpacing" xmlns:system="clr-namespace:System;assembly=mscorlib">-0.5</system:Double>
    <system:Double x:Key="H1LetterSpacing" xmlns:system="clr-namespace:System;assembly=mscorlib">-0.5</system:Double>
    <system:Double x:Key="H2LetterSpacing" xmlns:system="clr-namespace:System;assembly=mscorlib">0</system:Double>
    <system:Double x:Key="H3LetterSpacing" xmlns:system="clr-namespace:System;assembly=mscorlib">0</system:Double>
    <system:Double x:Key="H4LetterSpacing" xmlns:system="clr-namespace:System;assembly=mscorlib">0.25</system:Double>
    <system:Double x:Key="H5LetterSpacing" xmlns:system="clr-namespace:System;assembly=mscorlib">0</system:Double>
    <system:Double x:Key="H6LetterSpacing" xmlns:system="clr-namespace:System;assembly=mscorlib">0.15</system:Double>
    <system:Double x:Key="Body1LetterSpacing" xmlns:system="clr-namespace:System;assembly=mscorlib">0.5</system:Double>
    <system:Double x:Key="Body2LetterSpacing" xmlns:system="clr-namespace:System;assembly=mscorlib">0.25</system:Double>
    <system:Double x:Key="Body3LetterSpacing" xmlns:system="clr-namespace:System;assembly=mscorlib">0.4</system:Double>
    <system:Double x:Key="CaptionLetterSpacing" xmlns:system="clr-namespace:System;assembly=mscorlib">0.4</system:Double>
    <system:Double x:Key="LabelLetterSpacing" xmlns:system="clr-namespace:System;assembly=mscorlib">0.1</system:Double>
    <system:Double x:Key="OverlineLetterSpacing" xmlns:system="clr-namespace:System;assembly=mscorlib">1.5</system:Double>
    <system:Double x:Key="ButtonLetterSpacing" xmlns:system="clr-namespace:System;assembly=mscorlib">1.25</system:Double>

    <!-- ========================================== -->
    <!-- Typography Styles for TextBlock -->
    <!-- ========================================== -->
    
    <!-- Display Style -->
    <Style x:Key="DisplayTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource DisplayFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource DisplayFontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource LightFontWeight}"/>
        <Setter Property="LineHeight" Value="{StaticResource DisplayLineHeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <!-- Heading Styles -->
    <Style x:Key="H1TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource H1FontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource LightFontWeight}"/>
        <Setter Property="LineHeight" Value="{StaticResource H1LineHeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <Style x:Key="H2TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource H2FontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource LightFontWeight}"/>
        <Setter Property="LineHeight" Value="{StaticResource H2LineHeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <Style x:Key="H3TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource H3FontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource NormalFontWeight}"/>
        <Setter Property="LineHeight" Value="{StaticResource H3LineHeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <Style x:Key="H4TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource H4FontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource NormalFontWeight}"/>
        <Setter Property="LineHeight" Value="{StaticResource H4LineHeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <Style x:Key="H5TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource H5FontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource NormalFontWeight}"/>
        <Setter Property="LineHeight" Value="{StaticResource H5LineHeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <Style x:Key="H6TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource H6FontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource MediumFontWeight}"/>
        <Setter Property="LineHeight" Value="{StaticResource H6LineHeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <!-- Body Text Styles -->
    <Style x:Key="Body1TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Body1FontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource NormalFontWeight}"/>
        <Setter Property="LineHeight" Value="{StaticResource Body1LineHeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <Style x:Key="Body2TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Body2FontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource NormalFontWeight}"/>
        <Setter Property="LineHeight" Value="{StaticResource Body2LineHeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <Style x:Key="Body3TextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Body3FontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource NormalFontWeight}"/>
        <Setter Property="LineHeight" Value="{StaticResource Body3LineHeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <!-- Label and Caption Styles -->
    <Style x:Key="CaptionTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource CaptionFontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource NormalFontWeight}"/>
        <Setter Property="LineHeight" Value="{StaticResource CaptionLineHeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <Style x:Key="LabelTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource LabelFontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource MediumFontWeight}"/>
        <Setter Property="LineHeight" Value="{StaticResource LabelLineHeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <Style x:Key="OverlineTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource OverlineFontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource MediumFontWeight}"/>
        <Setter Property="LineHeight" Value="{StaticResource OverlineLineHeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextTertiaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <!-- Monospace Style -->
    <Style x:Key="MonospaceTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource MonospaceFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Body2FontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource NormalFontWeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="Background" Value="{StaticResource ControlBackgroundBrush}"/>
        <Setter Property="Padding" Value="4,2"/>
    </Style>

    <!-- ========================================== -->
    <!-- Button Typography Styles -->
    <!-- ========================================== -->

    <!-- Standard Button Text Style -->
    <Style x:Key="ButtonTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource ButtonFontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource MediumFontWeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="TextAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- Small Button Text Style -->
    <Style x:Key="ButtonSmallTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource ButtonSmallFontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource MediumFontWeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="TextAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- Large Button Text Style -->
    <Style x:Key="ButtonLargeTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource ButtonLargeFontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource MediumFontWeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="TextAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- ========================================== -->
    <!-- Input Control Typography Styles -->
    <!-- ========================================== -->

    <!-- TextBox Style -->
    <Style x:Key="TextBoxTextStyle" TargetType="TextBox">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Body2FontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource NormalFontWeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
    </Style>

    <!-- Placeholder Text Style -->
    <Style x:Key="PlaceholderTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Body2FontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource NormalFontWeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextTertiaryBrush}"/>
        <Setter Property="FontStyle" Value="Italic"/>
    </Style>

    <!-- ========================================== -->
    <!-- Navigation Typography Styles -->
    <!-- ========================================== -->

    <!-- Navigation Item Text Style -->
    <Style x:Key="NavigationItemTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Body2FontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource NormalFontWeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- Navigation Header Text Style -->
    <Style x:Key="NavigationHeaderTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource H6FontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource SemiBoldFontWeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- ========================================== -->
    <!-- Menu Typography Styles -->
    <!-- ========================================== -->

    <!-- Menu Item Text Style -->
    <Style x:Key="MenuItemTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Body2FontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource NormalFontWeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- Menu Header Text Style -->
    <Style x:Key="MenuHeaderTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource CaptionFontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource SemiBoldFontWeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- ========================================== -->
    <!-- Status and Notification Typography Styles -->
    <!-- ========================================== -->

    <!-- Success Text Style -->
    <Style x:Key="SuccessTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Body2FontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource NormalFontWeight}"/>
        <Setter Property="Foreground" Value="{StaticResource SuccessTextBrush}"/>
    </Style>

    <!-- Warning Text Style -->
    <Style x:Key="WarningTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Body2FontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource NormalFontWeight}"/>
        <Setter Property="Foreground" Value="{StaticResource WarningTextBrush}"/>
    </Style>

    <!-- Error Text Style -->
    <Style x:Key="ErrorTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Body2FontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource NormalFontWeight}"/>
        <Setter Property="Foreground" Value="{StaticResource ErrorTextBrush}"/>
    </Style>

    <!-- Info Text Style -->
    <Style x:Key="InfoTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Body2FontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource NormalFontWeight}"/>
        <Setter Property="Foreground" Value="{StaticResource InfoTextBrush}"/>
    </Style>

    <!-- ========================================== -->
    <!-- Specialized Typography Styles -->
    <!-- ========================================== -->

    <!-- Link Text Style -->
    <Style x:Key="LinkTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Body2FontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource NormalFontWeight}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="TextDecorations" Value="Underline"/>
        <Setter Property="Cursor" Value="Hand"/>
    </Style>

    <!-- Disabled Text Style -->
    <Style x:Key="DisabledTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource Body2FontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource NormalFontWeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextDisabledBrush}"/>
    </Style>

    <!-- Tooltip Text Style -->
    <Style x:Key="TooltipTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource CaptionFontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource NormalFontWeight}"/>
        <Setter Property="Foreground" Value="{StaticResource TextOnPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="MaxWidth" Value="300"/>
    </Style>

    <!-- ========================================== -->
    <!-- Typography Utilities -->
    <!-- ========================================== -->

    <!-- Text Alignment Styles -->
    <Style x:Key="TextLeftAlignStyle" TargetType="TextBlock">
        <Setter Property="TextAlignment" Value="Left"/>
    </Style>

    <Style x:Key="TextCenterAlignStyle" TargetType="TextBlock">
        <Setter Property="TextAlignment" Value="Center"/>
    </Style>

    <Style x:Key="TextRightAlignStyle" TargetType="TextBlock">
        <Setter Property="TextAlignment" Value="Right"/>
    </Style>

    <Style x:Key="TextJustifyAlignStyle" TargetType="TextBlock">
        <Setter Property="TextAlignment" Value="Justify"/>
    </Style>

    <!-- Text Transform Styles - Note: WPF TextBlock doesn't support TextTransform -->
    <!-- Use text converters or format the text in code-behind/view models instead -->

</ResourceDictionary>
