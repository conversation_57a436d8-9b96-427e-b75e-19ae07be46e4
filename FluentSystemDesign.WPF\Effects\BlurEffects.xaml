<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ========================================== -->
    <!-- Fluent Design System Blur Effects -->
    <!-- For background acrylic and focus effects -->
    <!-- ========================================== -->

    <!-- Subtle Blur: Light background blur -->
    <BlurEffect x:Key="SubtleBlur" 
                Radius="2" 
                KernelType="Gaussian"/>

    <!-- Light Blur: For slight background emphasis -->
    <BlurEffect x:Key="LightBlur" 
                Radius="4" 
                KernelType="Gaussian"/>

    <!-- Medium Blur: Standard background blur -->
    <BlurEffect x:Key="MediumBlur" 
                Radius="8" 
                KernelType="Gaussian"/>

    <!-- Strong Blur: Heavy background blur -->
    <BlurEffect x:Key="StrongBlur" 
                Radius="16" 
                KernelType="Gaussian"/>

    <!-- Heavy Blur: Maximum background blur -->
    <BlurEffect x:Key="HeavyBlur" 
                Radius="32" 
                KernelType="Gaussian"/>

    <!-- ========================================== -->
    <!-- Acrylic Blur Effects -->
    <!-- For Fluent Design acrylic materials -->
    <!-- ========================================== -->

    <!-- Acrylic Light: Light acrylic material -->
    <BlurEffect x:Key="AcrylicLight" 
                Radius="6" 
                KernelType="Gaussian"/>

    <!-- Acrylic Medium: Standard acrylic material -->
    <BlurEffect x:Key="AcrylicMedium" 
                Radius="12" 
                KernelType="Gaussian"/>

    <!-- Acrylic Strong: Heavy acrylic material -->
    <BlurEffect x:Key="AcrylicStrong" 
                Radius="20" 
                KernelType="Gaussian"/>

    <!-- ========================================== -->
    <!-- Focus Blur Effects -->
    <!-- For highlighting focused elements -->
    <!-- ========================================== -->

    <!-- Focus Background: Blur background when element is focused -->
    <BlurEffect x:Key="FocusBackground" 
                Radius="10" 
                KernelType="Gaussian"/>

    <!-- Modal Background: Blur for modal dialogs -->
    <BlurEffect x:Key="ModalBackground" 
                Radius="24" 
                KernelType="Gaussian"/>

    <!-- ========================================== -->
    <!-- Animation Blur Effects -->
    <!-- For transition animations -->
    <!-- ========================================== -->

    <!-- Transition Blur: For smooth transitions -->
    <BlurEffect x:Key="TransitionBlur" 
                Radius="6" 
                KernelType="Gaussian"/>

    <!-- Loading Blur: For loading states -->
    <BlurEffect x:Key="LoadingBlur" 
                Radius="8" 
                KernelType="Gaussian"/>

</ResourceDictionary>
