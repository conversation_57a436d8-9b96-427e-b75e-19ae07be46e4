using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;

namespace FluentSystemDesign.WPF.Converters
{
    /// <summary>
    /// 主题到画刷转换器
    /// 根据当前主题（深色/浅色）返回相应的画刷对象
    /// </summary>
    /// <remarks>
    /// 支持的主题类型：
    /// - "Light": 浅色主题
    /// - "Dark": 深色主题
    /// - "Auto": 自动检测系统主题
    /// 
    /// 参数格式：
    /// - "Light=#FFFFFF,Dark=#000000": 指定浅色和深色主题的颜色
    /// - "Primary": 使用主色调的浅色/深色变体
    /// - "Secondary": 使用次要色调的浅色/深色变体
    /// - "Accent": 使用强调色的浅色/深色变体
    /// - "Background": 使用背景色的浅色/深色变体
    /// - "Text": 使用文本色的浅色/深色变体
    /// - "ResourceKey": 直接使用资源键获取画刷
    /// 
    /// 特殊选项：
    /// - "Freeze": 冻结画刷以提高性能
    /// - "Opacity=0.5": 设置画刷透明度
    /// </remarks>
    /// <example>
    /// 在XAML中使用：
    /// <code>
    /// &lt;Rectangle Fill="{Binding CurrentTheme, Converter={StaticResource ThemeToBrushConverter}, ConverterParameter=Background}"/&gt;
    /// &lt;TextBlock Foreground="{Binding CurrentTheme, Converter={StaticResource ThemeToBrushConverter}, ConverterParameter=Text}"/&gt;
    /// &lt;Border Background="{Binding CurrentTheme, Converter={StaticResource ThemeToBrushConverter}, ConverterParameter=Primary,Opacity=0.1}"/&gt;
    /// </code>
    /// </example>
    [ValueConversion(typeof(string), typeof(Brush))]
    [ValueConversion(typeof(ApplicationTheme), typeof(Brush))]
    public class ThemeToBrushConverter : IValueConverter
    {
        /// <summary>
        /// 默认浅色主题画刷
        /// </summary>
        public Brush DefaultLightBrush { get; set; } = Brushes.White;

        /// <summary>
        /// 默认深色主题画刷
        /// </summary>
        public Brush DefaultDarkBrush { get; set; } = new SolidColorBrush(Color.FromRgb(30, 30, 30));

        /// <summary>
        /// 是否冻结创建的画刷
        /// </summary>
        public bool FreezeBrushes { get; set; } = true;

        /// <summary>
        /// 将主题值转换为画刷
        /// </summary>
        /// <param name="value">主题值（字符串或ApplicationTheme枚举）</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">画刷参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>对应主题的画刷</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var isDarkTheme = DetermineIsDarkTheme(value);
            var (lightBrush, darkBrush, opacity, shouldFreeze) = ParseBrushParameter(parameter?.ToString(), isDarkTheme);

            var resultBrush = isDarkTheme ? darkBrush : lightBrush;

            // 应用透明度
            if (opacity.HasValue && opacity.Value != 1.0)
            {
                if (resultBrush is SolidColorBrush solidBrush)
                {
                    var color = solidBrush.Color;
                    var newColor = Color.FromArgb((byte)(opacity.Value * 255), color.R, color.G, color.B);
                    resultBrush = new SolidColorBrush(newColor);
                }
                else
                {
                    // 对于其他类型的画刷，克隆并设置透明度
                    resultBrush = resultBrush.Clone();
                    resultBrush.Opacity = opacity.Value;
                }
            }

            // 冻结画刷以提高性能
            if (shouldFreeze && resultBrush.CanFreeze)
            {
                resultBrush.Freeze();
            }

            return resultBrush;
        }

        /// <summary>
        /// 反向转换（不支持）
        /// </summary>
        /// <param name="value">画刷值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>不支持的操作异常</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotSupportedException("ThemeToBrushConverter does not support ConvertBack operation.");
        }

        /// <summary>
        /// 确定是否为深色主题
        /// </summary>
        /// <param name="themeValue">主题值</param>
        /// <returns>是否为深色主题</returns>
        private static bool DetermineIsDarkTheme(object themeValue)
        {
            if (themeValue is ApplicationTheme appTheme)
            {
                return appTheme switch
                {
                    ApplicationTheme.Dark => true,
                    ApplicationTheme.Light => false,
                    ApplicationTheme.Auto => IsSystemDarkTheme(),
                    _ => false
                };
            }

            if (themeValue is string themeString)
            {
                return themeString.ToLowerInvariant() switch
                {
                    "dark" => true,
                    "light" => false,
                    "auto" => IsSystemDarkTheme(),
                    _ => false
                };
            }

            if (themeValue is bool isDark)
            {
                return isDark;
            }

            return false;
        }

        /// <summary>
        /// 检测系统是否使用深色主题
        /// </summary>
        /// <returns>系统是否使用深色主题</returns>
        private static bool IsSystemDarkTheme()
        {
            try
            {
                using var key = Microsoft.Win32.Registry.CurrentUser.OpenSubKey(@"Software\Microsoft\Windows\CurrentVersion\Themes\Personalize");
                var value = key?.GetValue("AppsUseLightTheme");
                if (value is int intValue)
                {
                    return intValue == 0;
                }
            }
            catch
            {
                // 忽略异常
            }

            return false;
        }

        /// <summary>
        /// 解析画刷参数
        /// </summary>
        /// <param name="parameter">参数字符串</param>
        /// <param name="isDarkTheme">是否为深色主题</param>
        /// <returns>解析结果</returns>
        private (Brush lightBrush, Brush darkBrush, double? opacity, bool shouldFreeze) ParseBrushParameter(string parameter, bool isDarkTheme)
        {
            var lightBrush = DefaultLightBrush;
            var darkBrush = DefaultDarkBrush;
            double? opacity = null;
            var shouldFreeze = FreezeBrushes;

            if (string.IsNullOrWhiteSpace(parameter))
            {
                return (lightBrush, darkBrush, opacity, shouldFreeze);
            }

            var lowerParam = parameter.ToLowerInvariant();

            // 解析透明度
            var opacityMatch = System.Text.RegularExpressions.Regex.Match(parameter, @"opacity\s*=\s*([\d.]+)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            if (opacityMatch.Success && double.TryParse(opacityMatch.Groups[1].Value, NumberStyles.Float, CultureInfo.InvariantCulture, out double opacityValue))
            {
                opacity = Math.Max(0.0, Math.Min(1.0, opacityValue));
            }

            // 检查冻结选项
            if (lowerParam.Contains("freeze"))
            {
                shouldFreeze = true;
            }
            else if (lowerParam.Contains("nofreeze"))
            {
                shouldFreeze = false;
            }

            // 处理预定义的画刷类型
            if (lowerParam.Contains("primary"))
            {
                lightBrush = GetThemeBrush("PrimaryBrush", isDarkTheme) ?? new SolidColorBrush(Colors.Blue);
                darkBrush = GetThemeBrush("PrimaryLightBrush", isDarkTheme) ?? new SolidColorBrush(Colors.LightBlue);
            }
            else if (lowerParam.Contains("secondary"))
            {
                lightBrush = GetThemeBrush("SecondaryBrush", isDarkTheme) ?? new SolidColorBrush(Colors.Purple);
                darkBrush = GetThemeBrush("SecondaryLightBrush", isDarkTheme) ?? new SolidColorBrush(Colors.Plum);
            }
            else if (lowerParam.Contains("accent"))
            {
                lightBrush = GetThemeBrush("AccentBrush", isDarkTheme) ?? new SolidColorBrush(Colors.Orange);
                darkBrush = GetThemeBrush("AccentLightBrush", isDarkTheme) ?? new SolidColorBrush(Colors.LightSalmon);
            }
            else if (lowerParam.Contains("background"))
            {
                lightBrush = GetThemeBrush("BackgroundBrush", isDarkTheme) ?? Brushes.White;
                darkBrush = GetThemeBrush("BackgroundBrush", isDarkTheme) ?? new SolidColorBrush(Color.FromRgb(33, 33, 33));
            }
            else if (lowerParam.Contains("text"))
            {
                lightBrush = GetThemeBrush("TextPrimaryBrush", isDarkTheme) ?? Brushes.Black;
                darkBrush = GetThemeBrush("TextPrimaryBrush", isDarkTheme) ?? Brushes.White;
            }
            else if (lowerParam.Contains("surface"))
            {
                lightBrush = GetThemeBrush("SurfaceBrush", isDarkTheme) ?? Brushes.White;
                darkBrush = GetThemeBrush("SurfaceBrush", isDarkTheme) ?? new SolidColorBrush(Color.FromRgb(48, 48, 48));
            }
            else if (parameter.Contains(",") && parameter.Contains("="))
            {
                // 解析自定义颜色格式："Light=#FFFFFF,Dark=#000000"
                var parts = parameter.Split(',');
                foreach (var part in parts)
                {
                    if (part.Contains("="))
                    {
                        var keyValue = part.Split('=');
                        if (keyValue.Length == 2)
                        {
                            var key = keyValue[0].Trim().ToLowerInvariant();
                            var value = keyValue[1].Trim();

                            if (TryParseColor(value, out Color parsedColor))
                            {
                                var brush = new SolidColorBrush(parsedColor);
                                switch (key)
                                {
                                    case "light":
                                        lightBrush = brush;
                                        break;
                                    case "dark":
                                        darkBrush = brush;
                                        break;
                                }
                            }
                        }
                    }
                }
            }
            else
            {
                // 尝试作为资源键查找
                var resourceBrush = GetThemeBrush(parameter, isDarkTheme);
                if (resourceBrush != null)
                {
                    lightBrush = resourceBrush;
                    darkBrush = resourceBrush;
                }
                else if (TryParseColor(parameter, out Color singleColor))
                {
                    // 尝试解析单个颜色值
                    var brush = new SolidColorBrush(singleColor);
                    lightBrush = brush;
                    darkBrush = brush;
                }
            }

            return (lightBrush, darkBrush, opacity, shouldFreeze);
        }

        /// <summary>
        /// 从应用程序资源中获取主题画刷
        /// </summary>
        /// <param name="resourceKey">资源键</param>
        /// <param name="isDarkTheme">是否为深色主题</param>
        /// <returns>主题画刷</returns>
        private static Brush GetThemeBrush(string resourceKey, bool isDarkTheme)
        {
            try
            {
                if (Application.Current?.Resources[resourceKey] is Brush brush)
                {
                    return brush;
                }

                // 尝试查找主题特定的资源键
                var themeSpecificKey = isDarkTheme ? $"Dark{resourceKey}" : $"Light{resourceKey}";
                if (Application.Current?.Resources[themeSpecificKey] is Brush themeBrush)
                {
                    return themeBrush;
                }
            }
            catch
            {
                // 忽略异常
            }

            return null;
        }

        /// <summary>
        /// 尝试解析颜色字符串
        /// </summary>
        /// <param name="colorString">颜色字符串</param>
        /// <param name="color">解析出的颜色</param>
        /// <returns>是否解析成功</returns>
        private static bool TryParseColor(string colorString, out Color color)
        {
            color = Colors.Transparent;

            try
            {
                var parsedColor = ColorConverter.ConvertFromString(colorString);
                if (parsedColor is Color colorValue)
                {
                    color = colorValue;
                    return true;
                }
            }
            catch
            {
                // 忽略异常
            }

            return false;
        }
    }
}
