using System;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Data;

namespace FluentSystemDesign.WPF.Converters
{
    /// <summary>
    /// 枚举到布尔值转换器
    /// 将枚举值与指定值比较，相等时返回true，否则返回false
    /// </summary>
    /// <remarks>
    /// 主要用于RadioButton和CheckBox的绑定场景。
    /// 参数可以是：
    /// - 单个枚举值名称
    /// - 多个枚举值名称（用逗号分隔），任一匹配即返回true
    /// - "Invert"前缀可以反转结果
    /// </remarks>
    /// <example>
    /// 在XAML中使用：
    /// <code>
    /// &lt;RadioButton IsChecked="{Binding Status, Converter={StaticResource EnumToBoolConverter}, ConverterParameter=Active}"/&gt;
    /// &lt;RadioButton IsChecked="{Binding Status, Converter={StaticResource EnumToBoolConverter}, ConverterParameter=Inactive,Disabled}"/&gt;
    /// &lt;CheckBox IsChecked="{Binding Status, Converter={StaticResource EnumToBoolConverter}, ConverterParameter=Invert:Active}"/&gt;
    /// </code>
    /// </example>
    [ValueConversion(typeof(Enum), typeof(bool))]
    public class EnumToBoolConverter : IValueConverter
    {
        /// <summary>
        /// 将枚举值转换为布尔值
        /// </summary>
        /// <param name="value">枚举值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">比较的枚举值名称</param>
        /// <param name="culture">文化信息</param>
        /// <returns>比较结果</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null || !value.GetType().IsEnum)
            {
                return DependencyProperty.UnsetValue;
            }

            if (parameter is not string parameterString || string.IsNullOrWhiteSpace(parameterString))
            {
                return false;
            }

            var enumValue = (Enum)value;
            var (isInverted, targetValues) = ParseParameter(parameterString);

            // 检查当前枚举值是否在目标值列表中
            var isMatch = targetValues.Any(targetValue => 
                string.Equals(enumValue.ToString(), targetValue, StringComparison.OrdinalIgnoreCase));

            return isInverted ? !isMatch : isMatch;
        }

        /// <summary>
        /// 将布尔值转换为枚举值
        /// </summary>
        /// <param name="value">布尔值</param>
        /// <param name="targetType">目标枚举类型</param>
        /// <param name="parameter">目标枚举值名称</param>
        /// <param name="culture">文化信息</param>
        /// <returns>枚举值</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not bool boolValue)
            {
                return DependencyProperty.UnsetValue;
            }

            if (!targetType.IsEnum)
            {
                return DependencyProperty.UnsetValue;
            }

            if (parameter is not string parameterString || string.IsNullOrWhiteSpace(parameterString))
            {
                return DependencyProperty.UnsetValue;
            }

            var (isInverted, targetValues) = ParseParameter(parameterString);

            // 确定应该返回的布尔值
            var shouldMatch = isInverted ? !boolValue : boolValue;

            if (!shouldMatch)
            {
                // 如果不应该匹配，返回Unset让绑定系统处理
                return Binding.DoNothing;
            }

            // 尝试解析第一个目标值
            var firstTargetValue = targetValues.FirstOrDefault();
            if (firstTargetValue != null && Enum.TryParse(targetType, firstTargetValue, true, out object result))
            {
                return result;
            }

            return DependencyProperty.UnsetValue;
        }

        /// <summary>
        /// 解析参数字符串
        /// </summary>
        /// <param name="parameter">参数字符串</param>
        /// <returns>是否反转和目标值列表</returns>
        private static (bool isInverted, string[] targetValues) ParseParameter(string parameter)
        {
            var isInverted = false;
            var parameterToProcess = parameter;

            // 检查是否有Invert前缀
            if (parameter.StartsWith("Invert:", StringComparison.OrdinalIgnoreCase))
            {
                isInverted = true;
                parameterToProcess = parameter.Substring(7); // 移除"Invert:"前缀
            }
            else if (parameter.StartsWith("!", StringComparison.OrdinalIgnoreCase))
            {
                isInverted = true;
                parameterToProcess = parameter.Substring(1); // 移除"!"前缀
            }

            // 分割多个值
            var targetValues = parameterToProcess
                .Split(new[] { ',', ';', '|' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(v => v.Trim())
                .Where(v => !string.IsNullOrEmpty(v))
                .ToArray();

            return (isInverted, targetValues);
        }
    }
}
