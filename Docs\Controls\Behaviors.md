# FluentSystemDesign WPF 行为类 (Behaviors)

FluentSystemDesign WPF控件库提供了一系列强大的行为类，用于增强UI元素的交互功能。这些行为类基于Microsoft.Xaml.Behaviors框架，提供了可重用的UI行为模式。

## 概述

行为类允许您在不修改控件代码的情况下，为现有控件添加新的功能。所有行为类都遵循MVVM模式，支持数据绑定和命令模式。

## 可用的行为类

### 1. DragBehavior - 拖拽行为

为UI元素提供拖拽功能，支持自定义拖拽数据和效果。

**主要特性：**
- 可配置的拖拽阈值
- 自定义拖拽数据
- 拖拽开始和完成事件
- 支持不同的拖拽效果

**使用示例：**
```xml
<ListBoxItem>
    <i:Interaction.Behaviors>
        <behaviors:DragBehavior 
            IsEnabled="True"
            DragData="{Binding}"
            DragEffects="Move"
            DragThreshold="5" />
    </i:Interaction.Behaviors>
    <TextBlock Text="{Binding Name}" />
</ListBoxItem>
```

### 2. AnimationBehavior - 动画行为

为UI元素提供常用的动画效果，如淡入淡出、滑动、缩放等。

**主要特性：**
- 多种预定义动画类型
- 可配置的触发器
- 自定义缓动函数
- 动画事件通知

**使用示例：**
```xml
<Border>
    <i:Interaction.Behaviors>
        <behaviors:AnimationBehavior 
            AnimationType="FadeIn"
            Duration="0:0:0.5"
            Trigger="Loaded"
            AutoStart="True" />
    </i:Interaction.Behaviors>
</Border>
```

### 3. FocusManagementBehavior - 焦点管理行为

提供高级的焦点管理功能，包括自动获取焦点、Tab导航控制等。

**主要特性：**
- 自动焦点获取
- 文本全选功能
- 自定义Tab导航
- 焦点事件通知

**使用示例：**
```xml
<TextBox>
    <i:Interaction.Behaviors>
        <behaviors:FocusManagementBehavior 
            FocusOnLoad="True"
            SelectAllOnFocus="True"
            TabIndex="1" />
    </i:Interaction.Behaviors>
</TextBox>
```

### 4. ValidationBehavior - 输入验证行为

为输入控件提供实时验证功能，支持多种验证规则。

**主要特性：**
- 多种预定义验证类型
- 自定义正则表达式验证
- 实时验证反馈
- 可视化错误提示

**使用示例：**
```xml
<TextBox>
    <i:Interaction.Behaviors>
        <behaviors:ValidationBehavior 
            ValidationType="Email"
            IsRequired="True"
            ValidateOnTextChanged="True"
            ErrorMessage="请输入有效的邮箱地址" />
    </i:Interaction.Behaviors>
</TextBox>
```

### 5. ScrollBehavior - 滚动行为

为ScrollViewer提供增强的滚动功能，包括平滑滚动、自动隐藏滚动条等。

**主要特性：**
- 平滑滚动动画
- 自动隐藏滚动条
- 边界反弹效果
- 滚动事件通知

**使用示例：**
```xml
<ScrollViewer>
    <i:Interaction.Behaviors>
        <behaviors:ScrollBehavior 
            IsSmoothScrollEnabled="True"
            AutoHideScrollBar="True"
            ScrollDuration="0:0:0.3" />
    </i:Interaction.Behaviors>
</ScrollViewer>
```

### 6. WindowBehavior - 窗口行为

为Window提供增强功能，如拖拽移动、双击最大化、窗口状态保存等。

**主要特性：**
- 拖拽移动窗口
- 双击标题栏最大化
- 窗口位置和大小保存
- 窗口按钮控制
- 淡入淡出效果

**使用示例：**
```xml
<Window>
    <i:Interaction.Behaviors>
        <behaviors:WindowBehavior 
            EnableDragMove="True"
            EnableDoubleClickMaximize="True"
            SaveWindowPlacement="True"
            PlacementKey="MainWindow" />
    </i:Interaction.Behaviors>
</Window>
```

### 7. WatermarkBehavior - 水印行为

为文本输入控件提供水印功能，显示提示文本。

**主要特性：**
- 自定义水印文本和样式
- 可配置的显示位置
- 焦点状态控制
- 透明度调节

**使用示例：**
```xml
<TextBox>
    <i:Interaction.Behaviors>
        <behaviors:WatermarkBehavior 
            WatermarkText="请输入用户名"
            WatermarkForeground="Gray"
            HideOnFocus="False" />
    </i:Interaction.Behaviors>
</TextBox>
```

## 扩展方法

为了简化行为的使用，我们提供了扩展方法：

```csharp
// 添加拖拽行为
myElement.AddDragBehavior(dragData, DragDropEffects.Move);

// 添加淡入动画
myElement.AddFadeInAnimation();

// 添加邮箱验证
myTextBox.AddEmailValidation(isRequired: true);

// 添加水印
myTextBox.AddWatermarkBehavior("请输入邮箱地址");

// 移除行为
myElement.RemoveBehavior<DragBehavior>();
```

## 命名空间引用

在XAML中使用行为类需要添加以下命名空间引用：

```xml
<Window x:Class="YourApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
        xmlns:behaviors="clr-namespace:FluentSystemDesign.WPF.Behaviors;assembly=FluentSystemDesign.WPF">
```

## 自定义行为

您也可以继承现有的行为类或创建新的行为类：

```csharp
public class CustomBehavior : Behavior<FrameworkElement>
{
    protected override void OnAttached()
    {
        base.OnAttached();
        // 附加逻辑
    }

    protected override void OnDetaching()
    {
        // 清理逻辑
        base.OnDetaching();
    }
}
```

## 性能考虑

- 行为类使用弱引用事件模式，避免内存泄漏
- 在不需要时及时移除行为
- 避免在大量元素上同时使用复杂的动画行为
- 合理配置验证行为的触发频率

## 兼容性

- 支持 .NET 8.0 及以上版本
- 兼容 WPF 应用程序
- 支持深色和浅色主题
- 遵循无障碍性标准

## 相关链接

- [返回主文档](../../README.md)
- [控件库概述](../GettingStarted.md)
- [示例项目](../../FluentSystemDesign.Examples/)
