using System;
using System.Globalization;
using System.Windows.Data;

namespace FluentSystemDesign.WPF.Converters
{
    /// <summary>
    /// 文本大小写转换器
    /// 用于在XAML中实现文本的大小写转换功能
    /// </summary>
    /// <remarks>
    /// 由于WPF的TextBlock不支持CSS的text-transform属性，
    /// 此转换器提供了在绑定时进行文本大小写转换的功能。
    /// </remarks>
    /// <example>
    /// 在XAML中使用：
    /// <code>
    /// &lt;TextBlock Text="{Binding SomeText, Converter={StaticResource TextCaseConverter}, ConverterParameter=upper}"/&gt;
    /// </code>
    /// </example>
    public class TextCaseConverter : IValueConverter
    {
        /// <summary>
        /// 将文本转换为指定的大小写格式
        /// </summary>
        /// <param name="value">要转换的文本值</param>
        /// <param name="targetType">目标类型（通常为string）</param>
        /// <param name="parameter">转换参数：upper（大写）、lower（小写）、title（标题格式）</param>
        /// <param name="culture">区域性信息</param>
        /// <returns>转换后的文本</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not string text)
                return value;

            if (parameter is not string caseType)
                return text;

            return caseType.ToLower() switch
            {
                "upper" => text.ToUpper(culture),
                "lower" => text.ToLower(culture),
                "title" => culture.TextInfo.ToTitleCase(text.ToLower(culture)),
                "capitalize" => CapitalizeFirstLetter(text, culture),
                _ => text
            };
        }

        /// <summary>
        /// 反向转换（不支持）
        /// </summary>
        /// <param name="value">值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">参数</param>
        /// <param name="culture">区域性信息</param>
        /// <returns>抛出NotImplementedException</returns>
        /// <exception cref="NotImplementedException">此转换器不支持反向转换</exception>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException("TextCaseConverter does not support ConvertBack operation.");
        }

        /// <summary>
        /// 将文本的首字母大写
        /// </summary>
        /// <param name="text">要处理的文本</param>
        /// <param name="culture">区域性信息</param>
        /// <returns>首字母大写的文本</returns>
        private static string CapitalizeFirstLetter(string text, CultureInfo culture)
        {
            if (string.IsNullOrEmpty(text))
                return text;

            if (text.Length == 1)
                return text.ToUpper(culture);

            return char.ToUpper(text[0], culture) + text.Substring(1);
        }
    }
}
