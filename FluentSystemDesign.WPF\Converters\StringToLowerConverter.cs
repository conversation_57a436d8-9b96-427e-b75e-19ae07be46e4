using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace FluentSystemDesign.WPF.Converters
{
    /// <summary>
    /// 字符串转小写转换器
    /// 将字符串转换为小写形式
    /// </summary>
    /// <remarks>
    /// 支持的参数：
    /// - "Invariant": 使用不变区域性进行转换
    /// - "Current": 使用当前区域性进行转换（默认）
    /// - "FirstOnly": 只将首字母转换为小写
    /// - "CamelCase": 转换为驼峰命名格式（首字母小写，其他单词首字母大写）
    /// </remarks>
    /// <example>
    /// 在XAML中使用：
    /// <code>
    /// &lt;TextBlock Text="{Binding UserName, Converter={StaticResource StringToLowerConverter}}"/&gt;
    /// &lt;TextBlock Text="{Binding PropertyName, Converter={StaticResource StringToLowerConverter}, ConverterParameter=CamelCase}"/&gt;
    /// &lt;TextBlock Text="{Binding Code, Converter={StaticResource StringToLowerConverter}, ConverterParameter=Invariant}"/&gt;
    /// </code>
    /// </example>
    [ValueConversion(typeof(string), typeof(string))]
    public class StringToLowerConverter : IValueConverter
    {
        /// <summary>
        /// 默认的转换模式
        /// </summary>
        public LowerCaseMode DefaultMode { get; set; } = LowerCaseMode.All;

        /// <summary>
        /// 默认是否使用不变区域性
        /// </summary>
        public bool UseInvariantCulture { get; set; } = false;

        /// <summary>
        /// 将字符串转换为小写
        /// </summary>
        /// <param name="value">字符串值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>小写字符串</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not string stringValue)
            {
                return value?.ToString() ?? string.Empty;
            }

            if (string.IsNullOrEmpty(stringValue))
            {
                return stringValue;
            }

            var (mode, useInvariant) = ParseParameter(parameter?.ToString());
            var cultureToUse = useInvariant ? CultureInfo.InvariantCulture : culture;

            return mode switch
            {
                LowerCaseMode.All => stringValue.ToLower(cultureToUse),
                LowerCaseMode.FirstOnly => ConvertFirstLetterToLower(stringValue, cultureToUse),
                LowerCaseMode.CamelCase => ConvertToCamelCase(stringValue, cultureToUse),
                _ => stringValue.ToLower(cultureToUse)
            };
        }

        /// <summary>
        /// 将小写字符串转换回原始形式（不支持完全恢复）
        /// </summary>
        /// <param name="value">小写字符串</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>转换后的字符串</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not string stringValue)
            {
                return DependencyProperty.UnsetValue;
            }

            if (string.IsNullOrEmpty(stringValue))
            {
                return stringValue;
            }

            var (mode, useInvariant) = ParseParameter(parameter?.ToString());
            var cultureToUse = useInvariant ? CultureInfo.InvariantCulture : culture;

            // 注意：ConvertBack无法完全恢复原始大小写，只能提供合理的转换
            return mode switch
            {
                LowerCaseMode.All => stringValue.ToUpper(cultureToUse),
                LowerCaseMode.FirstOnly => ConvertFirstLetterToUpper(stringValue, cultureToUse),
                LowerCaseMode.CamelCase => ConvertToPascalCase(stringValue, cultureToUse),
                _ => stringValue.ToUpper(cultureToUse)
            };
        }

        /// <summary>
        /// 将首字母转换为小写
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <param name="culture">文化信息</param>
        /// <returns>首字母小写的字符串</returns>
        private static string ConvertFirstLetterToLower(string input, CultureInfo culture)
        {
            if (string.IsNullOrEmpty(input))
            {
                return input;
            }

            if (input.Length == 1)
            {
                return input.ToLower(culture);
            }

            return char.ToLower(input[0], culture) + input.Substring(1);
        }

        /// <summary>
        /// 将首字母转换为大写
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <param name="culture">文化信息</param>
        /// <returns>首字母大写的字符串</returns>
        private static string ConvertFirstLetterToUpper(string input, CultureInfo culture)
        {
            if (string.IsNullOrEmpty(input))
            {
                return input;
            }

            if (input.Length == 1)
            {
                return input.ToUpper(culture);
            }

            return char.ToUpper(input[0], culture) + input.Substring(1);
        }

        /// <summary>
        /// 转换为驼峰命名格式
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <param name="culture">文化信息</param>
        /// <returns>驼峰格式的字符串</returns>
        private static string ConvertToCamelCase(string input, CultureInfo culture)
        {
            if (string.IsNullOrEmpty(input))
            {
                return input;
            }

            // 先转换为标题格式，然后将首字母转为小写
            var titleCase = culture.TextInfo.ToTitleCase(input.ToLower(culture));
            
            // 移除空格和特殊字符
            var result = System.Text.RegularExpressions.Regex.Replace(titleCase, @"[^\w]", "");
            
            // 将首字母转为小写
            return ConvertFirstLetterToLower(result, culture);
        }

        /// <summary>
        /// 转换为帕斯卡命名格式（首字母大写的驼峰格式）
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <param name="culture">文化信息</param>
        /// <returns>帕斯卡格式的字符串</returns>
        private static string ConvertToPascalCase(string input, CultureInfo culture)
        {
            if (string.IsNullOrEmpty(input))
            {
                return input;
            }

            // 先转换为标题格式
            var titleCase = culture.TextInfo.ToTitleCase(input.ToLower(culture));
            
            // 移除空格和特殊字符
            return System.Text.RegularExpressions.Regex.Replace(titleCase, @"[^\w]", "");
        }

        /// <summary>
        /// 解析参数字符串
        /// </summary>
        /// <param name="parameter">参数字符串</param>
        /// <returns>转换模式和区域性设置</returns>
        private (LowerCaseMode mode, bool useInvariant) ParseParameter(string parameter)
        {
            var mode = DefaultMode;
            var useInvariant = UseInvariantCulture;

            if (string.IsNullOrWhiteSpace(parameter))
            {
                return (mode, useInvariant);
            }

            var lowerParam = parameter.ToLowerInvariant();

            // 解析转换模式
            if (lowerParam.Contains("firstonly"))
            {
                mode = LowerCaseMode.FirstOnly;
            }
            else if (lowerParam.Contains("camelcase"))
            {
                mode = LowerCaseMode.CamelCase;
            }
            else if (lowerParam.Contains("all"))
            {
                mode = LowerCaseMode.All;
            }

            // 解析区域性设置
            if (lowerParam.Contains("invariant"))
            {
                useInvariant = true;
            }
            else if (lowerParam.Contains("current"))
            {
                useInvariant = false;
            }

            return (mode, useInvariant);
        }

        /// <summary>
        /// 小写转换模式
        /// </summary>
        public enum LowerCaseMode
        {
            /// <summary>
            /// 全部转换为小写
            /// </summary>
            All,

            /// <summary>
            /// 只将首字母转换为小写
            /// </summary>
            FirstOnly,

            /// <summary>
            /// 转换为驼峰命名格式
            /// </summary>
            CamelCase
        }
    }
}
