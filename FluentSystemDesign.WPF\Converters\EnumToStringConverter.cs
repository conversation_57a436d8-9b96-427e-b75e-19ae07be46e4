using System;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Windows;
using System.Windows.Data;

namespace FluentSystemDesign.WPF.Converters
{
    /// <summary>
    /// 枚举到字符串转换器
    /// 将枚举值转换为字符串，支持Description特性和本地化
    /// </summary>
    /// <remarks>
    /// 转换优先级：
    /// 1. Description特性的值
    /// 2. 枚举值的名称
    /// 支持参数：
    /// - "UseDescription": 强制使用Description特性
    /// - "UseName": 强制使用枚举名称
    /// - "ToUpper": 转换为大写
    /// - "ToLower": 转换为小写
    /// </remarks>
    /// <example>
    /// 在XAML中使用：
    /// <code>
    /// &lt;TextBlock Text="{Binding Status, Converter={StaticResource EnumToStringConverter}}"/&gt;
    /// &lt;TextBlock Text="{Binding Status, Converter={StaticResource EnumToStringConverter}, ConverterParameter=UseDescription}"/&gt;
    /// &lt;TextBlock Text="{Binding Status, Converter={StaticResource EnumToStringConverter}, ConverterParameter=ToUpper}"/&gt;
    /// </code>
    /// </example>
    [ValueConversion(typeof(Enum), typeof(string))]
    public class EnumToStringConverter : IValueConverter
    {
        /// <summary>
        /// 默认是否使用Description特性
        /// </summary>
        public bool UseDescriptionByDefault { get; set; } = true;

        /// <summary>
        /// 将枚举值转换为字符串
        /// </summary>
        /// <param name="value">枚举值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>字符串</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null || !value.GetType().IsEnum)
            {
                return DependencyProperty.UnsetValue;
            }

            var enumValue = (Enum)value;
            var parameterString = parameter?.ToString()?.ToLowerInvariant();
            
            // 解析参数
            var useDescription = UseDescriptionByDefault;
            var caseTransform = CaseTransform.None;

            if (!string.IsNullOrEmpty(parameterString))
            {
                if (parameterString.Contains("usedescription"))
                {
                    useDescription = true;
                }
                else if (parameterString.Contains("usename"))
                {
                    useDescription = false;
                }

                if (parameterString.Contains("toupper"))
                {
                    caseTransform = CaseTransform.Upper;
                }
                else if (parameterString.Contains("tolower"))
                {
                    caseTransform = CaseTransform.Lower;
                }
            }

            // 获取字符串值
            string result;
            if (useDescription)
            {
                result = GetEnumDescription(enumValue) ?? enumValue.ToString();
            }
            else
            {
                result = enumValue.ToString();
            }

            // 应用大小写转换
            result = ApplyCaseTransform(result, caseTransform, culture);

            return result;
        }

        /// <summary>
        /// 将字符串转换为枚举值
        /// </summary>
        /// <param name="value">字符串</param>
        /// <param name="targetType">目标枚举类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>枚举值</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not string stringValue || string.IsNullOrWhiteSpace(stringValue))
            {
                return DependencyProperty.UnsetValue;
            }

            if (!targetType.IsEnum)
            {
                return DependencyProperty.UnsetValue;
            }

            var parameterString = parameter?.ToString()?.ToLowerInvariant();
            var useDescription = UseDescriptionByDefault;

            if (!string.IsNullOrEmpty(parameterString))
            {
                if (parameterString.Contains("usedescription"))
                {
                    useDescription = true;
                }
                else if (parameterString.Contains("usename"))
                {
                    useDescription = false;
                }
            }

            // 首先尝试直接按名称解析
            if (Enum.TryParse(targetType, stringValue, true, out object directResult))
            {
                return directResult;
            }

            // 如果使用Description，尝试通过Description查找
            if (useDescription)
            {
                var enumValues = Enum.GetValues(targetType);
                foreach (var enumValue in enumValues)
                {
                    var description = GetEnumDescription((Enum)enumValue);
                    if (string.Equals(description, stringValue, StringComparison.OrdinalIgnoreCase))
                    {
                        return enumValue;
                    }
                }
            }

            return DependencyProperty.UnsetValue;
        }

        /// <summary>
        /// 获取枚举值的Description特性值
        /// </summary>
        /// <param name="enumValue">枚举值</param>
        /// <returns>Description特性值，如果没有则返回null</returns>
        private static string GetEnumDescription(Enum enumValue)
        {
            var fieldInfo = enumValue.GetType().GetField(enumValue.ToString());
            if (fieldInfo == null)
            {
                return null;
            }

            var descriptionAttribute = fieldInfo.GetCustomAttribute<DescriptionAttribute>();
            return descriptionAttribute?.Description;
        }

        /// <summary>
        /// 应用大小写转换
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <param name="transform">转换类型</param>
        /// <param name="culture">文化信息</param>
        /// <returns>转换后的字符串</returns>
        private static string ApplyCaseTransform(string input, CaseTransform transform, CultureInfo culture)
        {
            return transform switch
            {
                CaseTransform.Upper => input.ToUpper(culture),
                CaseTransform.Lower => input.ToLower(culture),
                _ => input
            };
        }

        /// <summary>
        /// 大小写转换类型
        /// </summary>
        private enum CaseTransform
        {
            None,
            Upper,
            Lower
        }
    }
}
