using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace FluentSystemDesign.WPF.Converters
{
    /// <summary>
    /// 转换器测试辅助类
    /// 提供转换器功能验证和测试的辅助方法
    /// </summary>
    public static class ConverterTestHelper
    {
        /// <summary>
        /// 测试转换器的基本功能
        /// </summary>
        /// <param name="converter">要测试的转换器</param>
        /// <param name="value">输入值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>转换结果</returns>
        public static object TestConvert(IValueConverter converter, object value, Type targetType, object parameter = null, CultureInfo culture = null)
        {
            if (converter == null)
                throw new ArgumentNullException(nameof(converter));

            culture ??= CultureInfo.CurrentCulture;

            try
            {
                return converter.Convert(value, targetType, parameter, culture);
            }
            catch (Exception ex)
            {
                return $"转换失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 测试转换器的反向转换功能
        /// </summary>
        /// <param name="converter">要测试的转换器</param>
        /// <param name="value">输入值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>反向转换结果</returns>
        public static object TestConvertBack(IValueConverter converter, object value, Type targetType, object parameter = null, CultureInfo culture = null)
        {
            if (converter == null)
                throw new ArgumentNullException(nameof(converter));

            culture ??= CultureInfo.CurrentCulture;

            try
            {
                return converter.ConvertBack(value, targetType, parameter, culture);
            }
            catch (NotSupportedException)
            {
                return "不支持反向转换";
            }
            catch (Exception ex)
            {
                return $"反向转换失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 验证转换器是否正确处理null值
        /// </summary>
        /// <param name="converter">要测试的转换器</param>
        /// <param name="targetType">目标类型</param>
        /// <returns>是否正确处理null值</returns>
        public static bool TestNullHandling(IValueConverter converter, Type targetType)
        {
            if (converter == null)
                throw new ArgumentNullException(nameof(converter));

            try
            {
                var result = converter.Convert(null, targetType, null, CultureInfo.CurrentCulture);
                return result != null; // 应该返回某种默认值而不是null
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证转换器是否正确处理无效输入
        /// </summary>
        /// <param name="converter">要测试的转换器</param>
        /// <param name="invalidValue">无效输入值</param>
        /// <param name="targetType">目标类型</param>
        /// <returns>是否正确处理无效输入</returns>
        public static bool TestInvalidInputHandling(IValueConverter converter, object invalidValue, Type targetType)
        {
            if (converter == null)
                throw new ArgumentNullException(nameof(converter));

            try
            {
                var result = converter.Convert(invalidValue, targetType, null, CultureInfo.CurrentCulture);
                return result == DependencyProperty.UnsetValue || result != null;
            }
            catch
            {
                return false; // 不应该抛出异常
            }
        }

        /// <summary>
        /// 获取转换器的基本信息
        /// </summary>
        /// <param name="converter">转换器实例</param>
        /// <returns>转换器信息</returns>
        public static string GetConverterInfo(IValueConverter converter)
        {
            if (converter == null)
                return "转换器为null";

            var type = converter.GetType();
            var attributes = type.GetCustomAttributes(typeof(ValueConversionAttribute), false);
            
            if (attributes.Length > 0)
            {
                var attr = (ValueConversionAttribute)attributes[0];
                return $"{type.Name}: {attr.SourceType?.Name} -> {attr.TargetType?.Name}";
            }

            return type.Name;
        }

        /// <summary>
        /// 运行转换器的完整测试套件
        /// </summary>
        /// <param name="converter">要测试的转换器</param>
        /// <param name="testValue">测试值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <returns>测试结果报告</returns>
        public static string RunFullTest(IValueConverter converter, object testValue, Type targetType, object parameter = null)
        {
            if (converter == null)
                return "转换器为null";

            var report = $"转换器测试报告: {GetConverterInfo(converter)}\n";
            report += new string('=', 50) + "\n";

            // 测试正常转换
            var convertResult = TestConvert(converter, testValue, targetType, parameter);
            report += $"正常转换: {testValue} -> {convertResult}\n";

            // 测试反向转换
            var convertBackResult = TestConvertBack(converter, convertResult, testValue?.GetType() ?? typeof(object), parameter);
            report += $"反向转换: {convertResult} -> {convertBackResult}\n";

            // 测试null处理
            var nullHandling = TestNullHandling(converter, targetType);
            report += $"Null处理: {(nullHandling ? "通过" : "失败")}\n";

            // 测试无效输入处理
            var invalidHandling = TestInvalidInputHandling(converter, "无效输入", targetType);
            report += $"无效输入处理: {(invalidHandling ? "通过" : "失败")}\n";

            return report;
        }
    }
}
