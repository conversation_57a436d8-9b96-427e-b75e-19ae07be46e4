using System;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Data;

namespace FluentSystemDesign.WPF.Converters
{
    /// <summary>
    /// 枚举到可见性转换器
    /// 将枚举值与指定值比较，匹配时显示，否则隐藏
    /// </summary>
    /// <remarks>
    /// 参数格式：
    /// - 单个枚举值名称：匹配时显示
    /// - 多个枚举值名称（用逗号分隔）：任一匹配即显示
    /// - "Invert:"前缀：反转逻辑
    /// - "Hidden"后缀：使用Hidden而不是Collapsed
    /// 例如："Active,Processing" 或 "Invert:Disabled,Hidden"
    /// </remarks>
    /// <example>
    /// 在XAML中使用：
    /// <code>
    /// &lt;TextBlock Visibility="{Binding Status, Converter={StaticResource EnumToVisibilityConverter}, ConverterParameter=Active}"/&gt;
    /// &lt;Button Visibility="{Binding Status, Converter={StaticResource EnumToVisibilityConverter}, ConverterParameter=Active,Processing}"/&gt;
    /// &lt;ProgressBar Visibility="{Binding Status, Converter={StaticResource EnumToVisibilityConverter}, ConverterParameter=Invert:Completed,Hidden}"/&gt;
    /// </code>
    /// </example>
    [ValueConversion(typeof(Enum), typeof(Visibility))]
    public class EnumToVisibilityConverter : IValueConverter
    {
        /// <summary>
        /// 默认的隐藏方式
        /// </summary>
        public Visibility DefaultHiddenVisibility { get; set; } = Visibility.Collapsed;

        /// <summary>
        /// 将枚举值转换为可见性
        /// </summary>
        /// <param name="value">枚举值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">比较的枚举值名称和选项</param>
        /// <param name="culture">文化信息</param>
        /// <returns>可见性值</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null || !value.GetType().IsEnum)
            {
                return DefaultHiddenVisibility;
            }

            if (parameter is not string parameterString || string.IsNullOrWhiteSpace(parameterString))
            {
                return DefaultHiddenVisibility;
            }

            var enumValue = (Enum)value;
            var (isInverted, targetValues, hiddenVisibility) = ParseParameter(parameterString);

            // 检查当前枚举值是否在目标值列表中
            var isMatch = targetValues.Any(targetValue => 
                string.Equals(enumValue.ToString(), targetValue, StringComparison.OrdinalIgnoreCase));

            // 应用反转逻辑
            var shouldBeVisible = isInverted ? !isMatch : isMatch;

            return shouldBeVisible ? Visibility.Visible : hiddenVisibility;
        }

        /// <summary>
        /// 将可见性转换为枚举值
        /// </summary>
        /// <param name="value">可见性值</param>
        /// <param name="targetType">目标枚举类型</param>
        /// <param name="parameter">目标枚举值名称</param>
        /// <param name="culture">文化信息</param>
        /// <returns>枚举值</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not Visibility visibility)
            {
                return DependencyProperty.UnsetValue;
            }

            if (!targetType.IsEnum)
            {
                return DependencyProperty.UnsetValue;
            }

            if (parameter is not string parameterString || string.IsNullOrWhiteSpace(parameterString))
            {
                return DependencyProperty.UnsetValue;
            }

            var (isInverted, targetValues, _) = ParseParameter(parameterString);

            // 确定当前可见性对应的匹配状态
            var isCurrentlyVisible = visibility == Visibility.Visible;
            var shouldMatch = isInverted ? !isCurrentlyVisible : isCurrentlyVisible;

            if (!shouldMatch)
            {
                // 如果不应该匹配，返回DoNothing让绑定系统处理
                return Binding.DoNothing;
            }

            // 尝试解析第一个目标值
            var firstTargetValue = targetValues.FirstOrDefault();
            if (firstTargetValue != null && Enum.TryParse(targetType, firstTargetValue, true, out object result))
            {
                return result;
            }

            return DependencyProperty.UnsetValue;
        }

        /// <summary>
        /// 解析参数字符串
        /// </summary>
        /// <param name="parameter">参数字符串</param>
        /// <returns>是否反转、目标值列表和隐藏方式</returns>
        private (bool isInverted, string[] targetValues, Visibility hiddenVisibility) ParseParameter(string parameter)
        {
            var isInverted = false;
            var hiddenVisibility = DefaultHiddenVisibility;
            var parameterToProcess = parameter;

            // 检查是否有Invert前缀
            if (parameter.StartsWith("Invert:", StringComparison.OrdinalIgnoreCase))
            {
                isInverted = true;
                parameterToProcess = parameter.Substring(7); // 移除"Invert:"前缀
            }
            else if (parameter.StartsWith("!", StringComparison.OrdinalIgnoreCase))
            {
                isInverted = true;
                parameterToProcess = parameter.Substring(1); // 移除"!"前缀
            }

            // 检查是否指定了Hidden选项
            if (parameterToProcess.EndsWith(",Hidden", StringComparison.OrdinalIgnoreCase) ||
                parameterToProcess.EndsWith(";Hidden", StringComparison.OrdinalIgnoreCase))
            {
                hiddenVisibility = Visibility.Hidden;
                parameterToProcess = parameterToProcess.Substring(0, parameterToProcess.Length - 7); // 移除",Hidden"或";Hidden"
            }
            else if (parameterToProcess.EndsWith(",Collapsed", StringComparison.OrdinalIgnoreCase) ||
                     parameterToProcess.EndsWith(";Collapsed", StringComparison.OrdinalIgnoreCase))
            {
                hiddenVisibility = Visibility.Collapsed;
                parameterToProcess = parameterToProcess.Substring(0, parameterToProcess.Length - 10); // 移除",Collapsed"或";Collapsed"
            }

            // 分割多个值
            var targetValues = parameterToProcess
                .Split(new[] { ',', ';', '|' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(v => v.Trim())
                .Where(v => !string.IsNullOrEmpty(v) && 
                           !string.Equals(v, "Hidden", StringComparison.OrdinalIgnoreCase) &&
                           !string.Equals(v, "Collapsed", StringComparison.OrdinalIgnoreCase))
                .ToArray();

            return (isInverted, targetValues, hiddenVisibility);
        }
    }
}
