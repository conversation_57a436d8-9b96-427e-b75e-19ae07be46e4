using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace FluentSystemDesign.WPF.Converters
{
    /// <summary>
    /// 布尔值到透明度转换器
    /// 将布尔值转换为透明度值，用于控制元素的可见程度
    /// </summary>
    /// <remarks>
    /// 默认行为：true -> 1.0（完全不透明）, false -> 0.0（完全透明）
    /// 可以通过参数自定义透明度值，格式："TrueValue,FalseValue"，例如："1.0,0.3"
    /// 使用参数"Invert"可以反转行为
    /// </remarks>
    /// <example>
    /// 在XAML中使用：
    /// <code>
    /// &lt;TextBlock Opacity="{Binding IsEnabled, Converter={StaticResource BoolToOpacityConverter}}"/&gt;
    /// &lt;TextBlock Opacity="{Binding IsDisabled, Converter={StaticResource BoolToOpacityConverter}, ConverterParameter=Invert}"/&gt;
    /// &lt;TextBlock Opacity="{Binding IsHighlighted, Converter={StaticResource BoolToOpacityConverter}, ConverterParameter=1.0,0.5}"/&gt;
    /// </code>
    /// </example>
    [ValueConversion(typeof(bool), typeof(double))]
    public class BoolToOpacityConverter : IValueConverter
    {
        /// <summary>
        /// 默认的true值对应的透明度
        /// </summary>
        public double TrueOpacity { get; set; } = 1.0;

        /// <summary>
        /// 默认的false值对应的透明度
        /// </summary>
        public double FalseOpacity { get; set; } = 0.0;

        /// <summary>
        /// 将布尔值转换为透明度
        /// </summary>
        /// <param name="value">布尔值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数：Invert（反转）或"TrueValue,FalseValue"格式的自定义值</param>
        /// <param name="culture">文化信息</param>
        /// <returns>透明度值</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not bool boolValue)
            {
                return DependencyProperty.UnsetValue;
            }

            var trueOpacity = TrueOpacity;
            var falseOpacity = FalseOpacity;
            var isInverted = false;

            // 解析参数
            if (parameter is string parameterString && !string.IsNullOrEmpty(parameterString))
            {
                var lowerParam = parameterString.ToLowerInvariant();
                
                if (lowerParam.Contains("invert"))
                {
                    isInverted = true;
                }
                else if (parameterString.Contains(","))
                {
                    // 解析自定义透明度值
                    var parts = parameterString.Split(',');
                    if (parts.Length == 2)
                    {
                        if (double.TryParse(parts[0].Trim(), NumberStyles.Float, culture, out double customTrueOpacity))
                        {
                            trueOpacity = Math.Max(0.0, Math.Min(1.0, customTrueOpacity));
                        }
                        if (double.TryParse(parts[1].Trim(), NumberStyles.Float, culture, out double customFalseOpacity))
                        {
                            falseOpacity = Math.Max(0.0, Math.Min(1.0, customFalseOpacity));
                        }
                    }
                }
                else if (double.TryParse(parameterString, NumberStyles.Float, culture, out double singleValue))
                {
                    // 如果只提供一个值，将其作为true值，false值保持为0
                    trueOpacity = Math.Max(0.0, Math.Min(1.0, singleValue));
                }
            }

            var result = isInverted ? !boolValue : boolValue;
            return result ? trueOpacity : falseOpacity;
        }

        /// <summary>
        /// 将透明度转换为布尔值
        /// </summary>
        /// <param name="value">透明度值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>布尔值</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not double opacity)
            {
                return DependencyProperty.UnsetValue;
            }

            var trueOpacity = TrueOpacity;
            var falseOpacity = FalseOpacity;
            var isInverted = false;

            // 解析参数（与Convert方法相同的逻辑）
            if (parameter is string parameterString && !string.IsNullOrEmpty(parameterString))
            {
                var lowerParam = parameterString.ToLowerInvariant();
                
                if (lowerParam.Contains("invert"))
                {
                    isInverted = true;
                }
                else if (parameterString.Contains(","))
                {
                    var parts = parameterString.Split(',');
                    if (parts.Length == 2)
                    {
                        if (double.TryParse(parts[0].Trim(), NumberStyles.Float, culture, out double customTrueOpacity))
                        {
                            trueOpacity = customTrueOpacity;
                        }
                        if (double.TryParse(parts[1].Trim(), NumberStyles.Float, culture, out double customFalseOpacity))
                        {
                            falseOpacity = customFalseOpacity;
                        }
                    }
                }
            }

            // 判断透明度更接近哪个值
            var distanceToTrue = Math.Abs(opacity - trueOpacity);
            var distanceToFalse = Math.Abs(opacity - falseOpacity);
            
            var result = distanceToTrue <= distanceToFalse;
            return isInverted ? !result : result;
        }
    }
}
