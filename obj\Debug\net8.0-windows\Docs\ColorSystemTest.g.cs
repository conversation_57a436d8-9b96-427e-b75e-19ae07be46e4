﻿#pragma checksum "..\..\..\..\Docs\ColorSystemTest.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "31359E357B7589DF30B5DA0CB153C2993D82BC0D"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using FluentSystemDesign.WPF.Controls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace FluentSystemDesign.Examples.Pages {
    
    
    /// <summary>
    /// ColorSystemTest
    /// </summary>
    public partial class ColorSystemTest : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 47 "..\..\..\..\Docs\ColorSystemTest.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal FluentSystemDesign.WPF.Controls.ColorPalette PrimaryPalette;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\..\Docs\ColorSystemTest.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal FluentSystemDesign.WPF.Controls.ColorPalette SecondaryPalette;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\Docs\ColorSystemTest.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal FluentSystemDesign.WPF.Controls.ColorPalette AccentPalette;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\Docs\ColorSystemTest.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal FluentSystemDesign.WPF.Controls.ColorPalette NeutralPalette;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AnimationTest;component/docs/colorsystemtest.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Docs\ColorSystemTest.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PrimaryPalette = ((FluentSystemDesign.WPF.Controls.ColorPalette)(target));
            return;
            case 2:
            this.SecondaryPalette = ((FluentSystemDesign.WPF.Controls.ColorPalette)(target));
            return;
            case 3:
            this.AccentPalette = ((FluentSystemDesign.WPF.Controls.ColorPalette)(target));
            return;
            case 4:
            this.NeutralPalette = ((FluentSystemDesign.WPF.Controls.ColorPalette)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

