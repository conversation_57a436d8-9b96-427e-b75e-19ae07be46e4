using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace FluentSystemDesign.WPF.Converters
{
    /// <summary>
    /// 厚度转换器
    /// 将数值转换为Thickness对象，支持多种输入格式
    /// </summary>
    /// <remarks>
    /// 支持的转换格式：
    /// - 单个数值 -> 四边相等的Thickness
    /// - "left,top,right,bottom" -> 指定四边的Thickness
    /// - "horizontal,vertical" -> 水平和垂直方向的Thickness
    /// - 参数可以指定应用方向："Left", "Top", "Right", "Bottom", "Horizontal", "Vertical", "All"
    /// </remarks>
    /// <example>
    /// 在XAML中使用：
    /// <code>
    /// &lt;Border BorderThickness="{Binding BorderSize, Converter={StaticResource ThicknessConverter}}"/&gt;
    /// &lt;Border Margin="{Binding Spacing, Converter={StaticResource ThicknessConverter}, ConverterParameter=Horizontal}"/&gt;
    /// &lt;Border Padding="{Binding Padding, Converter={StaticResource ThicknessConverter}, ConverterParameter=Left}"/&gt;
    /// </code>
    /// </example>
    [ValueConversion(typeof(double), typeof(Thickness))]
    [ValueConversion(typeof(string), typeof(Thickness))]
    public class ThicknessConverter : IValueConverter
    {
        /// <summary>
        /// 将数值或字符串转换为Thickness
        /// </summary>
        /// <param name="value">数值或字符串</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">应用方向参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>Thickness对象</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
            {
                return new Thickness(0);
            }

            var direction = parameter?.ToString()?.ToLowerInvariant() ?? "all";

            // 如果输入是字符串，尝试解析为多个值
            if (value is string stringValue && !string.IsNullOrWhiteSpace(stringValue))
            {
                return ParseThicknessFromString(stringValue, direction, culture);
            }

            // 如果输入是数值，转换为double
            if (TryConvertToDouble(value, out double numericValue))
            {
                return CreateThicknessFromValue(numericValue, direction);
            }

            return DependencyProperty.UnsetValue;
        }

        /// <summary>
        /// 将Thickness转换回数值或字符串
        /// </summary>
        /// <param name="value">Thickness对象</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>数值或字符串</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not Thickness thickness)
            {
                return DependencyProperty.UnsetValue;
            }

            var direction = parameter?.ToString()?.ToLowerInvariant() ?? "all";

            if (targetType == typeof(string))
            {
                return direction switch
                {
                    "left" => thickness.Left.ToString(culture),
                    "top" => thickness.Top.ToString(culture),
                    "right" => thickness.Right.ToString(culture),
                    "bottom" => thickness.Bottom.ToString(culture),
                    "horizontal" => $"{thickness.Left},{thickness.Right}",
                    "vertical" => $"{thickness.Top},{thickness.Bottom}",
                    _ => $"{thickness.Left},{thickness.Top},{thickness.Right},{thickness.Bottom}"
                };
            }

            // 返回数值类型
            var numericValue = direction switch
            {
                "left" => thickness.Left,
                "top" => thickness.Top,
                "right" => thickness.Right,
                "bottom" => thickness.Bottom,
                "horizontal" => thickness.Left, // 假设左右相等
                "vertical" => thickness.Top,    // 假设上下相等
                _ => thickness.Left // 假设四边相等
            };

            return ConvertToTargetType(numericValue, targetType);
        }

        /// <summary>
        /// 从字符串解析Thickness
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <param name="direction">应用方向</param>
        /// <param name="culture">文化信息</param>
        /// <returns>Thickness对象</returns>
        private static Thickness ParseThicknessFromString(string input, string direction, CultureInfo culture)
        {
            var parts = input.Split(new[] { ',', ' ', ';' }, StringSplitOptions.RemoveEmptyEntries);
            var values = new double[parts.Length];

            for (int i = 0; i < parts.Length; i++)
            {
                if (!double.TryParse(parts[i].Trim(), NumberStyles.Float, culture, out values[i]))
                {
                    return new Thickness(0);
                }
            }

            return parts.Length switch
            {
                1 => CreateThicknessFromValue(values[0], direction),
                2 => direction switch
                {
                    "horizontal" => new Thickness(values[0], 0, values[1], 0),
                    "vertical" => new Thickness(0, values[0], 0, values[1]),
                    _ => new Thickness(values[0], values[1], values[0], values[1]) // 水平,垂直
                },
                4 => new Thickness(values[0], values[1], values[2], values[3]), // 左,上,右,下
                _ => new Thickness(0)
            };
        }

        /// <summary>
        /// 根据单个数值和方向创建Thickness
        /// </summary>
        /// <param name="value">数值</param>
        /// <param name="direction">应用方向</param>
        /// <returns>Thickness对象</returns>
        private static Thickness CreateThicknessFromValue(double value, string direction)
        {
            return direction switch
            {
                "left" => new Thickness(value, 0, 0, 0),
                "top" => new Thickness(0, value, 0, 0),
                "right" => new Thickness(0, 0, value, 0),
                "bottom" => new Thickness(0, 0, 0, value),
                "horizontal" => new Thickness(value, 0, value, 0),
                "vertical" => new Thickness(0, value, 0, value),
                _ => new Thickness(value) // all
            };
        }

        /// <summary>
        /// 尝试将对象转换为double
        /// </summary>
        /// <param name="value">要转换的值</param>
        /// <param name="result">转换结果</param>
        /// <returns>是否转换成功</returns>
        private static bool TryConvertToDouble(object value, out double result)
        {
            result = 0;

            return value switch
            {
                double d => (result = d) == d,
                float f => (result = f) == f,
                decimal dec => (result = (double)dec) == (double)dec,
                int i => (result = i) == i,
                long l => (result = l) == l,
                short s => (result = s) == s,
                byte b => (result = b) == b,
                _ => false
            };
        }

        /// <summary>
        /// 将double值转换为目标类型
        /// </summary>
        /// <param name="value">double值</param>
        /// <param name="targetType">目标类型</param>
        /// <returns>转换后的值</returns>
        private static object ConvertToTargetType(double value, Type targetType)
        {
            try
            {
                return targetType switch
                {
                    Type t when t == typeof(double) || t == typeof(double?) => value,
                    Type t when t == typeof(float) || t == typeof(float?) => (float)value,
                    Type t when t == typeof(decimal) || t == typeof(decimal?) => (decimal)value,
                    Type t when t == typeof(int) || t == typeof(int?) => (int)Math.Round(value),
                    Type t when t == typeof(long) || t == typeof(long?) => (long)Math.Round(value),
                    _ => value
                };
            }
            catch (OverflowException)
            {
                return DependencyProperty.UnsetValue;
            }
        }
    }
}
