using System;
using System.Linq;
using System.Windows;
using System.Windows.Media;
using System.Windows.Media.Animation;
using Microsoft.Xaml.Behaviors;

namespace FluentSystemDesign.WPF.Behaviors
{
    /// <summary>
    /// 动画行为，为UI元素提供常用的动画效果
    /// </summary>
    public class AnimationBehavior : Behavior<FrameworkElement>
    {
        #region 依赖属性

        /// <summary>
        /// 动画类型
        /// </summary>
        public static readonly DependencyProperty AnimationTypeProperty =
            DependencyProperty.Register(nameof(AnimationType), typeof(AnimationType), typeof(AnimationBehavior),
                new PropertyMetadata(AnimationType.FadeIn));

        /// <summary>
        /// 动画持续时间
        /// </summary>
        public static readonly DependencyProperty DurationProperty =
            DependencyProperty.Register(nameof(Duration), typeof(TimeSpan), typeof(AnimationBehavior),
                new PropertyMetadata(TimeSpan.FromMilliseconds(300)));

        /// <summary>
        /// 动画延迟时间
        /// </summary>
        public static readonly DependencyProperty DelayProperty =
            DependencyProperty.Register(nameof(Delay), typeof(TimeSpan), typeof(AnimationBehavior),
                new PropertyMetadata(TimeSpan.Zero));

        /// <summary>
        /// 缓动函数
        /// </summary>
        public static readonly DependencyProperty EasingFunctionProperty =
            DependencyProperty.Register(nameof(EasingFunction), typeof(IEasingFunction), typeof(AnimationBehavior),
                new PropertyMetadata(new QuadraticEase { EasingMode = EasingMode.EaseOut }));

        /// <summary>
        /// 是否自动开始动画
        /// </summary>
        public static readonly DependencyProperty AutoStartProperty =
            DependencyProperty.Register(nameof(AutoStart), typeof(bool), typeof(AnimationBehavior),
                new PropertyMetadata(true));

        /// <summary>
        /// 触发器
        /// </summary>
        public static readonly DependencyProperty TriggerProperty =
            DependencyProperty.Register(nameof(Trigger), typeof(AnimationTrigger), typeof(AnimationBehavior),
                new PropertyMetadata(AnimationTrigger.Loaded));

        #endregion

        #region 属性

        /// <summary>
        /// 获取或设置动画类型
        /// </summary>
        public AnimationType AnimationType
        {
            get => (AnimationType)GetValue(AnimationTypeProperty);
            set => SetValue(AnimationTypeProperty, value);
        }

        /// <summary>
        /// 获取或设置动画持续时间
        /// </summary>
        public TimeSpan Duration
        {
            get => (TimeSpan)GetValue(DurationProperty);
            set => SetValue(DurationProperty, value);
        }

        /// <summary>
        /// 获取或设置动画延迟时间
        /// </summary>
        public TimeSpan Delay
        {
            get => (TimeSpan)GetValue(DelayProperty);
            set => SetValue(DelayProperty, value);
        }

        /// <summary>
        /// 获取或设置缓动函数
        /// </summary>
        public IEasingFunction EasingFunction
        {
            get => (IEasingFunction)GetValue(EasingFunctionProperty);
            set => SetValue(EasingFunctionProperty, value);
        }

        /// <summary>
        /// 获取或设置是否自动开始动画
        /// </summary>
        public bool AutoStart
        {
            get => (bool)GetValue(AutoStartProperty);
            set => SetValue(AutoStartProperty, value);
        }

        /// <summary>
        /// 获取或设置触发器
        /// </summary>
        public AnimationTrigger Trigger
        {
            get => (AnimationTrigger)GetValue(TriggerProperty);
            set => SetValue(TriggerProperty, value);
        }

        #endregion

        #region 事件

        /// <summary>
        /// 动画开始事件
        /// </summary>
        public event EventHandler AnimationStarted;

        /// <summary>
        /// 动画完成事件
        /// </summary>
        public event EventHandler AnimationCompleted;

        #endregion

        #region 私有字段

        private Storyboard _currentStoryboard;

        #endregion

        #region 重写方法

        /// <summary>
        /// 附加到关联对象时调用
        /// </summary>
        protected override void OnAttached()
        {
            base.OnAttached();
            
            if (AssociatedObject != null)
            {
                AssociatedObject.Loaded += OnLoaded;
                AssociatedObject.MouseEnter += OnMouseEnter;
                AssociatedObject.MouseLeave += OnMouseLeave;
                
                if (AutoStart && Trigger == AnimationTrigger.Loaded)
                {
                    AssociatedObject.Loaded += (s, e) => StartAnimation();
                }
            }
        }

        /// <summary>
        /// 从关联对象分离时调用
        /// </summary>
        protected override void OnDetaching()
        {
            if (AssociatedObject != null)
            {
                AssociatedObject.Loaded -= OnLoaded;
                AssociatedObject.MouseEnter -= OnMouseEnter;
                AssociatedObject.MouseLeave -= OnMouseLeave;
            }
            
            base.OnDetaching();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 开始动画
        /// </summary>
        public void StartAnimation()
        {
            if (AssociatedObject == null)
                return;

            // 停止当前动画
            StopAnimation();

            // 强制更新布局以确保获取正确的尺寸
            AssociatedObject.UpdateLayout();

            // 如果元素还没有完成布局，等待下一个渲染周期
            if (AssociatedObject.ActualWidth == 0 || AssociatedObject.ActualHeight == 0)
            {
                AssociatedObject.Dispatcher.BeginInvoke(System.Windows.Threading.DispatcherPriority.Render,
                    new Action(() => StartAnimation()));
                return;
            }

            var storyboard = CreateStoryboard();
            if (storyboard != null)
            {
                _currentStoryboard = storyboard;
                AnimationStarted?.Invoke(this, EventArgs.Empty);
                storyboard.Completed += (s, e) =>
                {
                    _currentStoryboard = null;
                    AnimationCompleted?.Invoke(this, EventArgs.Empty);
                };
                storyboard.Begin(AssociatedObject);
            }
        }

        /// <summary>
        /// 停止动画
        /// </summary>
        public void StopAnimation()
        {
            if (_currentStoryboard != null && AssociatedObject != null)
            {
                _currentStoryboard.Stop(AssociatedObject);
                _currentStoryboard = null;
            }
        }

        #endregion

        #region 事件处理

        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            if (AutoStart && Trigger == AnimationTrigger.Loaded)
            {
                StartAnimation();
            }
        }

        private void OnMouseEnter(object sender, System.Windows.Input.MouseEventArgs e)
        {
            if (Trigger == AnimationTrigger.MouseEnter)
            {
                StartAnimation();
            }
        }

        private void OnMouseLeave(object sender, System.Windows.Input.MouseEventArgs e)
        {
            if (Trigger == AnimationTrigger.MouseLeave)
            {
                StartAnimation();
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 创建故事板
        /// </summary>
        private Storyboard CreateStoryboard()
        {
            var storyboard = new Storyboard();
            
            switch (AnimationType)
            {
                case AnimationType.FadeIn:
                    CreateFadeInAnimation(storyboard);
                    break;
                case AnimationType.FadeOut:
                    CreateFadeOutAnimation(storyboard);
                    break;
                case AnimationType.SlideInFromLeft:
                    CreateSlideInFromLeftAnimation(storyboard);
                    break;
                case AnimationType.SlideInFromRight:
                    CreateSlideInFromRightAnimation(storyboard);
                    break;
                case AnimationType.SlideInFromTop:
                    CreateSlideInFromTopAnimation(storyboard);
                    break;
                case AnimationType.SlideInFromBottom:
                    CreateSlideInFromBottomAnimation(storyboard);
                    break;
                case AnimationType.ScaleIn:
                    CreateScaleInAnimation(storyboard);
                    break;
                case AnimationType.ScaleOut:
                    CreateScaleOutAnimation(storyboard);
                    break;
                default:
                    return null;
            }

            storyboard.BeginTime = Delay;
            return storyboard;
        }

        private void CreateFadeInAnimation(Storyboard storyboard)
        {
            var animation = new DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = Duration,
                EasingFunction = EasingFunction
            };
            
            Storyboard.SetTarget(animation, AssociatedObject);
            Storyboard.SetTargetProperty(animation, new PropertyPath(UIElement.OpacityProperty));
            storyboard.Children.Add(animation);
        }

        private void CreateFadeOutAnimation(Storyboard storyboard)
        {
            var animation = new DoubleAnimation
            {
                From = 1,
                To = 0,
                Duration = Duration,
                EasingFunction = EasingFunction
            };
            
            Storyboard.SetTarget(animation, AssociatedObject);
            Storyboard.SetTargetProperty(animation, new PropertyPath(UIElement.OpacityProperty));
            storyboard.Children.Add(animation);
        }

        private void CreateSlideInFromLeftAnimation(Storyboard storyboard)
        {
            // 获取元素的实际尺寸，如果为0则使用默认值
            var elementWidth = AssociatedObject.ActualWidth > 0 ? AssociatedObject.ActualWidth : 200;
            var slideDistance = 300; // 使用固定距离确保效果明显

            // 创建或获取TranslateTransform
            var translateTransform = AssociatedObject.RenderTransform as System.Windows.Media.TranslateTransform;
            if (translateTransform == null)
            {
                translateTransform = new System.Windows.Media.TranslateTransform();
                AssociatedObject.RenderTransform = translateTransform;
            }

            // 立即设置起始位置（在屏幕左侧外）
            translateTransform.X = -slideDistance;

            // 创建动画
            var animation = new DoubleAnimation
            {
                From = -slideDistance,
                To = 0,
                Duration = Duration,
                EasingFunction = EasingFunction
            };

            Storyboard.SetTarget(animation, translateTransform);
            Storyboard.SetTargetProperty(animation, new PropertyPath(System.Windows.Media.TranslateTransform.XProperty));
            storyboard.Children.Add(animation);
        }

        private void CreateSlideInFromRightAnimation(Storyboard storyboard)
        {
            // 使用固定距离确保效果明显
            var slideDistance = 300;

            // 创建或获取TranslateTransform
            var translateTransform = AssociatedObject.RenderTransform as System.Windows.Media.TranslateTransform;
            if (translateTransform == null)
            {
                translateTransform = new System.Windows.Media.TranslateTransform();
                AssociatedObject.RenderTransform = translateTransform;
            }

            // 立即设置起始位置（在屏幕右侧外）
            translateTransform.X = slideDistance;

            // 创建动画
            var animation = new DoubleAnimation
            {
                From = slideDistance,
                To = 0,
                Duration = Duration,
                EasingFunction = EasingFunction
            };

            Storyboard.SetTarget(animation, translateTransform);
            Storyboard.SetTargetProperty(animation, new PropertyPath(System.Windows.Media.TranslateTransform.XProperty));
            storyboard.Children.Add(animation);
        }

        private void CreateSlideInFromTopAnimation(Storyboard storyboard)
        {
            // 使用固定距离确保效果明显
            var slideDistance = 200;

            // 创建或获取TranslateTransform
            var translateTransform = AssociatedObject.RenderTransform as System.Windows.Media.TranslateTransform;
            if (translateTransform == null)
            {
                translateTransform = new System.Windows.Media.TranslateTransform();
                AssociatedObject.RenderTransform = translateTransform;
            }

            // 立即设置起始位置（在屏幕上方外）
            translateTransform.Y = -slideDistance;

            // 创建动画
            var animation = new DoubleAnimation
            {
                From = -slideDistance,
                To = 0,
                Duration = Duration,
                EasingFunction = EasingFunction
            };

            Storyboard.SetTarget(animation, translateTransform);
            Storyboard.SetTargetProperty(animation, new PropertyPath(System.Windows.Media.TranslateTransform.YProperty));
            storyboard.Children.Add(animation);
        }

        private void CreateSlideInFromBottomAnimation(Storyboard storyboard)
        {
            // 使用固定距离确保效果明显
            var slideDistance = 200;

            // 创建或获取TranslateTransform
            var translateTransform = AssociatedObject.RenderTransform as System.Windows.Media.TranslateTransform;
            if (translateTransform == null)
            {
                translateTransform = new System.Windows.Media.TranslateTransform();
                AssociatedObject.RenderTransform = translateTransform;
            }

            // 立即设置起始位置（在屏幕下方外）
            translateTransform.Y = slideDistance;

            // 创建动画
            var animation = new DoubleAnimation
            {
                From = slideDistance,
                To = 0,
                Duration = Duration,
                EasingFunction = EasingFunction
            };

            Storyboard.SetTarget(animation, translateTransform);
            Storyboard.SetTargetProperty(animation, new PropertyPath(System.Windows.Media.TranslateTransform.YProperty));
            storyboard.Children.Add(animation);
        }

        private void CreateScaleInAnimation(Storyboard storyboard)
        {
            // 设置变换原点为中心
            AssociatedObject.RenderTransformOrigin = new Point(0.5, 0.5);

            // 创建或获取ScaleTransform
            var scaleTransform = AssociatedObject.RenderTransform as System.Windows.Media.ScaleTransform;
            if (scaleTransform == null)
            {
                scaleTransform = new System.Windows.Media.ScaleTransform();
                AssociatedObject.RenderTransform = scaleTransform;
            }

            // 立即设置初始缩放值（很小但不为0）
            scaleTransform.ScaleX = 0.1;
            scaleTransform.ScaleY = 0.1;

            // 创建X轴缩放动画
            var scaleXAnimation = new DoubleAnimation
            {
                From = 0.1,
                To = 1.0,
                Duration = Duration,
                EasingFunction = EasingFunction
            };

            // 创建Y轴缩放动画
            var scaleYAnimation = new DoubleAnimation
            {
                From = 0.1,
                To = 1.0,
                Duration = Duration,
                EasingFunction = EasingFunction
            };

            Storyboard.SetTarget(scaleXAnimation, scaleTransform);
            Storyboard.SetTargetProperty(scaleXAnimation, new PropertyPath(System.Windows.Media.ScaleTransform.ScaleXProperty));

            Storyboard.SetTarget(scaleYAnimation, scaleTransform);
            Storyboard.SetTargetProperty(scaleYAnimation, new PropertyPath(System.Windows.Media.ScaleTransform.ScaleYProperty));

            storyboard.Children.Add(scaleXAnimation);
            storyboard.Children.Add(scaleYAnimation);
        }

        private void CreateScaleOutAnimation(Storyboard storyboard)
        {
            // 确保元素有合适的Transform
            var transformGroup = EnsureTransformGroup();
            var scaleTransform = GetOrCreateScaleTransform(transformGroup);

            // 设置变换原点为中心
            AssociatedObject.RenderTransformOrigin = new Point(0.5, 0.5);

            // 设置初始缩放值
            scaleTransform.ScaleX = 1;
            scaleTransform.ScaleY = 1;

            var scaleXAnimation = new DoubleAnimation
            {
                From = 1,
                To = 0,
                Duration = Duration,
                EasingFunction = EasingFunction
            };

            var scaleYAnimation = new DoubleAnimation
            {
                From = 1,
                To = 0,
                Duration = Duration,
                EasingFunction = EasingFunction
            };

            Storyboard.SetTarget(scaleXAnimation, scaleTransform);
            Storyboard.SetTargetProperty(scaleXAnimation, new PropertyPath(System.Windows.Media.ScaleTransform.ScaleXProperty));

            Storyboard.SetTarget(scaleYAnimation, scaleTransform);
            Storyboard.SetTargetProperty(scaleYAnimation, new PropertyPath(System.Windows.Media.ScaleTransform.ScaleYProperty));

            storyboard.Children.Add(scaleXAnimation);
            storyboard.Children.Add(scaleYAnimation);
        }

        #region Transform 管理辅助方法

        /// <summary>
        /// 确保元素有TransformGroup，如果没有则创建
        /// </summary>
        /// <returns>TransformGroup实例</returns>
        private TransformGroup EnsureTransformGroup()
        {
            if (AssociatedObject.RenderTransform is TransformGroup transformGroup)
            {
                return transformGroup;
            }

            // 创建新的TransformGroup
            transformGroup = new TransformGroup();

            // 如果已有其他Transform，保留它
            if (AssociatedObject.RenderTransform != null && AssociatedObject.RenderTransform != Transform.Identity)
            {
                transformGroup.Children.Add(AssociatedObject.RenderTransform);
            }

            AssociatedObject.RenderTransform = transformGroup;
            return transformGroup;
        }

        /// <summary>
        /// 获取或创建TranslateTransform
        /// </summary>
        /// <param name="transformGroup">Transform组</param>
        /// <returns>TranslateTransform实例</returns>
        private System.Windows.Media.TranslateTransform GetOrCreateTranslateTransform(TransformGroup transformGroup)
        {
            var translateTransform = transformGroup.Children.OfType<System.Windows.Media.TranslateTransform>().FirstOrDefault();
            if (translateTransform == null)
            {
                translateTransform = new System.Windows.Media.TranslateTransform();
                transformGroup.Children.Add(translateTransform);
            }
            return translateTransform;
        }

        /// <summary>
        /// 获取或创建ScaleTransform
        /// </summary>
        /// <param name="transformGroup">Transform组</param>
        /// <returns>ScaleTransform实例</returns>
        private System.Windows.Media.ScaleTransform GetOrCreateScaleTransform(TransformGroup transformGroup)
        {
            var scaleTransform = transformGroup.Children.OfType<System.Windows.Media.ScaleTransform>().FirstOrDefault();
            if (scaleTransform == null)
            {
                scaleTransform = new System.Windows.Media.ScaleTransform();
                transformGroup.Children.Add(scaleTransform);
            }
            return scaleTransform;
        }

        #endregion

        #endregion
    }

    /// <summary>
    /// 动画类型枚举
    /// </summary>
    public enum AnimationType
    {
        /// <summary>
        /// 淡入
        /// </summary>
        FadeIn,
        
        /// <summary>
        /// 淡出
        /// </summary>
        FadeOut,
        
        /// <summary>
        /// 从左侧滑入
        /// </summary>
        SlideInFromLeft,
        
        /// <summary>
        /// 从右侧滑入
        /// </summary>
        SlideInFromRight,
        
        /// <summary>
        /// 从顶部滑入
        /// </summary>
        SlideInFromTop,
        
        /// <summary>
        /// 从底部滑入
        /// </summary>
        SlideInFromBottom,
        
        /// <summary>
        /// 缩放进入
        /// </summary>
        ScaleIn,
        
        /// <summary>
        /// 缩放退出
        /// </summary>
        ScaleOut
    }

    /// <summary>
    /// 动画触发器枚举
    /// </summary>
    public enum AnimationTrigger
    {
        /// <summary>
        /// 加载时触发
        /// </summary>
        Loaded,
        
        /// <summary>
        /// 鼠标进入时触发
        /// </summary>
        MouseEnter,
        
        /// <summary>
        /// 鼠标离开时触发
        /// </summary>
        MouseLeave,
        
        /// <summary>
        /// 手动触发
        /// </summary>
        Manual
    }
}
