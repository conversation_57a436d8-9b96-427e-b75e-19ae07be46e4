# FluentSystemDesign 字体系统

FluentSystemDesign WPF控件库的字体系统基于Microsoft Fluent Design System设计语言，提供了完整的字体层级、样式和规范，确保应用程序具有一致性和可读性的文本显示效果。

## 目录

- [设计原则](#设计原则)
- [字体族](#字体族)
- [字体层级](#字体层级)
- [字体样式](#字体样式)
- [使用方法](#使用方法)
- [主题适配](#主题适配)
- [最佳实践](#最佳实践)

## 设计原则

### 1. 层级清晰
字体系统提供了明确的视觉层级，从Display到Caption，每个层级都有其特定的用途和视觉权重。

### 2. 可读性优先
所有字体大小、行高和字符间距都经过精心调整，确保在不同设备和分辨率下都具有良好的可读性。

### 3. 一致性
统一的字体族和样式规范确保整个应用程序的视觉一致性。

### 4. 可访问性
字体系统考虑了可访问性需求，提供了足够的对比度和合适的字体大小。

## 字体族

### 主要字体族
- **PrimaryFontFamily**: `Segoe UI` - Windows平台的默认字体，具有良好的可读性
- **SecondaryFontFamily**: `Microsoft YaHei UI, SimSun` - 中文字体支持
- **MonospaceFontFamily**: `Consolas, Courier New` - 等宽字体，用于代码显示
- **DisplayFontFamily**: `Segoe UI Light` - 装饰字体，用于大标题

## 字体层级

### Display 层级
用于最重要的标题和品牌展示：

```xml
<!-- Display 样式 -->
<TextBlock Style="{StaticResource DisplayTextStyle}" 
           Text="FluentSystemDesign" />
```

- **字体大小**: 72px
- **字重**: Light
- **行高**: 1.2
- **用途**: 品牌标题、欢迎页面主标题

### 标题层级 (H1-H6)

#### H1 - 主标题
```xml
<TextBlock Style="{StaticResource H1TextStyle}" 
           Text="主要页面标题" />
```
- **字体大小**: 40px
- **字重**: Light
- **行高**: 1.2

#### H2 - 次级标题
```xml
<TextBlock Style="{StaticResource H2TextStyle}" 
           Text="章节标题" />
```
- **字体大小**: 32px
- **字重**: Light
- **行高**: 1.25

#### H3 - 三级标题
```xml
<TextBlock Style="{StaticResource H3TextStyle}" 
           Text="子章节标题" />
```
- **字体大小**: 28px
- **字重**: Normal
- **行高**: 1.3

#### H4 - 四级标题
```xml
<TextBlock Style="{StaticResource H4TextStyle}" 
           Text="小节标题" />
```
- **字体大小**: 24px
- **字重**: Normal
- **行高**: 1.35

#### H5 - 五级标题
```xml
<TextBlock Style="{StaticResource H5TextStyle}" 
           Text="段落标题" />
```
- **字体大小**: 20px
- **字重**: Normal
- **行高**: 1.4

#### H6 - 六级标题
```xml
<TextBlock Style="{StaticResource H6TextStyle}" 
           Text="小标题" />
```
- **字体大小**: 18px
- **字重**: Medium
- **行高**: 1.45

### 正文层级

#### Body1 - 主要正文
```xml
<TextBlock Style="{StaticResource Body1TextStyle}" 
           Text="这是主要的正文内容，用于重要的文本信息。" />
```
- **字体大小**: 16px
- **字重**: Normal
- **行高**: 1.5
- **用途**: 重要内容、主要描述

#### Body2 - 标准正文
```xml
<TextBlock Style="{StaticResource Body2TextStyle}" 
           Text="这是标准的正文内容，用于一般的文本信息。" />
```
- **字体大小**: 14px
- **字重**: Normal
- **行高**: 1.5
- **用途**: 一般内容、说明文字

#### Body3 - 次要正文
```xml
<TextBlock Style="{StaticResource Body3TextStyle}" 
           Text="这是次要的正文内容，用于补充信息。" />
```
- **字体大小**: 12px
- **字重**: Normal
- **行高**: 1.4
- **用途**: 补充信息、次要描述

### 标签和说明层级

#### Caption - 说明文字
```xml
<TextBlock Style="{StaticResource CaptionTextStyle}" 
           Text="图片说明或补充信息" />
```
- **字体大小**: 12px
- **字重**: Normal
- **颜色**: 次要文本色

#### Label - 标签文字
```xml
<TextBlock Style="{StaticResource LabelTextStyle}" 
           Text="表单标签" />
```
- **字体大小**: 14px
- **字重**: Medium
- **用途**: 表单标签、控件标签

#### Overline - 上标文字
```xml
<TextBlock Style="{StaticResource OverlineTextStyle}"
           Text="CATEGORY" />
```
- **字体大小**: 10px
- **字重**: Medium
- **用途**: 分类标签、上标文字
- **注意**: 需要在代码中手动转换为大写

## 字体样式

### 按钮字体样式

#### 标准按钮
```xml
<Button Content="确定">
    <Button.Resources>
        <Style TargetType="TextBlock" BasedOn="{StaticResource ButtonTextStyle}"/>
    </Button.Resources>
</Button>
```

#### 小按钮
```xml
<Button Content="取消">
    <Button.Resources>
        <Style TargetType="TextBlock" BasedOn="{StaticResource ButtonSmallTextStyle}"/>
    </Button.Resources>
</Button>
```

#### 大按钮
```xml
<Button Content="开始">
    <Button.Resources>
        <Style TargetType="TextBlock" BasedOn="{StaticResource ButtonLargeTextStyle}"/>
    </Button.Resources>
</Button>
```

### 输入控件字体样式

#### 文本框
```xml
<TextBox Style="{StaticResource TextBoxTextStyle}" 
         Text="输入内容" />
```

#### 占位符文本
```xml
<TextBlock Style="{StaticResource PlaceholderTextStyle}" 
           Text="请输入内容..." />
```

### 导航字体样式

#### 导航项
```xml
<TextBlock Style="{StaticResource NavigationItemTextStyle}" 
           Text="首页" />
```

#### 导航标题
```xml
<TextBlock Style="{StaticResource NavigationHeaderTextStyle}" 
           Text="主导航" />
```

### 状态字体样式

#### 成功状态
```xml
<TextBlock Style="{StaticResource SuccessTextStyle}" 
           Text="操作成功" />
```

#### 警告状态
```xml
<TextBlock Style="{StaticResource WarningTextStyle}" 
           Text="请注意" />
```

#### 错误状态
```xml
<TextBlock Style="{StaticResource ErrorTextStyle}" 
           Text="操作失败" />
```

#### 信息状态
```xml
<TextBlock Style="{StaticResource InfoTextStyle}" 
           Text="提示信息" />
```

### 特殊字体样式

#### 链接文字
```xml
<TextBlock Style="{StaticResource LinkTextStyle}" 
           Text="点击这里" />
```

#### 禁用文字
```xml
<TextBlock Style="{StaticResource DisabledTextStyle}" 
           Text="不可用选项" />
```

#### 等宽字体
```xml
<TextBlock Style="{StaticResource MonospaceTextStyle}" 
           Text="var code = 'example';" />
```

#### 工具提示
```xml
<TextBlock Style="{StaticResource TooltipTextStyle}" 
           Text="这是一个工具提示" />
```

## 使用方法

### 1. 引用字体系统
确保在应用程序中正确引用了字体系统：

```xml
<Application.Resources>
    <ResourceDictionary>
        <ResourceDictionary.MergedDictionaries>
            <ResourceDictionary Source="pack://application:,,,/FluentSystemDesign.WPF;component/Themes/Styles.xaml"/>
        </ResourceDictionary.MergedDictionaries>
    </ResourceDictionary>
</Application.Resources>
```

### 2. 应用字体样式
直接使用预定义的样式：

```xml
<StackPanel>
    <TextBlock Style="{StaticResource H1TextStyle}" Text="页面标题"/>
    <TextBlock Style="{StaticResource Body1TextStyle}" Text="页面内容"/>
    <TextBlock Style="{StaticResource CaptionTextStyle}" Text="补充说明"/>
</StackPanel>
```

### 3. 自定义字体样式
基于现有样式创建自定义样式：

```xml
<Style x:Key="CustomTitleStyle" TargetType="TextBlock" BasedOn="{StaticResource H2TextStyle}">
    <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
    <Setter Property="Margin" Value="0,0,0,16"/>
</Style>
```

## 主题适配

字体系统完全支持深色和浅色主题的自动适配：

### 浅色主题
- 主要文本：深色 (#212121)
- 次要文本：中等深色 (#616161)
- 三级文本：浅深色 (#757575)
- 禁用文本：浅色 (#BDBDBD)

### 深色主题
- 主要文本：白色 (#FFFFFF)
- 次要文本：半透明白色 (#B3FFFFFF)
- 三级文本：低透明度白色 (#80FFFFFF)
- 禁用文本：很低透明度白色 (#4DFFFFFF)

主题切换时，所有文本颜色会自动适配，无需额外配置。

## 最佳实践

### 1. 选择合适的字体层级
- 使用H1-H6建立清晰的信息层级
- 避免跳跃式使用标题层级（如H1直接跳到H3）
- 正文内容优先使用Body2样式

### 2. 保持一致性
- 在同一应用中保持字体使用的一致性
- 相同功能的元素使用相同的字体样式
- 避免在一个页面中使用过多不同的字体样式

### 3. 考虑可读性
- 确保文本与背景有足够的对比度
- 避免在小字体上使用过细的字重
- 长文本内容使用合适的行高和段落间距

### 4. 响应式设计
- 在小屏幕设备上适当调整字体大小
- 考虑不同DPI设置下的字体显示效果
- 使用相对单位而非绝对像素值

### 5. 可访问性
- 确保最小字体大小不小于12px
- 为视觉障碍用户提供足够的文本对比度
- 支持系统字体大小设置

## 字体资源键参考

### 字体族资源键
```xml
<!-- 字体族 -->
{StaticResource PrimaryFontFamily}      <!-- Segoe UI -->
{StaticResource SecondaryFontFamily}    <!-- Microsoft YaHei UI, SimSun -->
{StaticResource MonospaceFontFamily}    <!-- Consolas, Courier New -->
{StaticResource DisplayFontFamily}      <!-- Segoe UI Light -->
```

### 字体大小资源键
```xml
<!-- 标题字体大小 -->
{StaticResource DisplayFontSize}        <!-- 72px -->
{StaticResource H1FontSize}             <!-- 40px -->
{StaticResource H2FontSize}             <!-- 32px -->
{StaticResource H3FontSize}             <!-- 28px -->
{StaticResource H4FontSize}             <!-- 24px -->
{StaticResource H5FontSize}             <!-- 20px -->
{StaticResource H6FontSize}             <!-- 18px -->

<!-- 正文字体大小 -->
{StaticResource Body1FontSize}          <!-- 16px -->
{StaticResource Body2FontSize}          <!-- 14px -->
{StaticResource Body3FontSize}          <!-- 12px -->

<!-- 标签字体大小 -->
{StaticResource CaptionFontSize}        <!-- 12px -->
{StaticResource LabelFontSize}          <!-- 14px -->
{StaticResource OverlineFontSize}       <!-- 10px -->

<!-- 按钮字体大小 -->
{StaticResource ButtonFontSize}         <!-- 14px -->
{StaticResource ButtonSmallFontSize}    <!-- 12px -->
{StaticResource ButtonLargeFontSize}    <!-- 16px -->
```

### 字体粗细资源键
```xml
{StaticResource ThinFontWeight}         <!-- Thin -->
{StaticResource ExtraLightFontWeight}   <!-- ExtraLight -->
{StaticResource LightFontWeight}        <!-- Light -->
{StaticResource NormalFontWeight}       <!-- Normal -->
{StaticResource MediumFontWeight}       <!-- Medium -->
{StaticResource SemiBoldFontWeight}     <!-- SemiBold -->
{StaticResource BoldFontWeight}         <!-- Bold -->
{StaticResource ExtraBoldFontWeight}    <!-- ExtraBold -->
{StaticResource BlackFontWeight}        <!-- Black -->
```

### 行高资源键
```xml
{StaticResource DisplayLineHeight}      <!-- 1.2 -->
{StaticResource H1LineHeight}           <!-- 1.2 -->
{StaticResource H2LineHeight}           <!-- 1.25 -->
{StaticResource H3LineHeight}           <!-- 1.3 -->
{StaticResource H4LineHeight}           <!-- 1.35 -->
{StaticResource H5LineHeight}           <!-- 1.4 -->
{StaticResource H6LineHeight}           <!-- 1.45 -->
{StaticResource Body1LineHeight}        <!-- 1.5 -->
{StaticResource Body2LineHeight}        <!-- 1.5 -->
{StaticResource Body3LineHeight}        <!-- 1.4 -->
{StaticResource CaptionLineHeight}      <!-- 1.4 -->
{StaticResource LabelLineHeight}        <!-- 1.4 -->
{StaticResource OverlineLineHeight}     <!-- 1.6 -->
```

## 高级用法

### 1. 动态字体大小
根据内容重要性动态调整字体大小：

```xml
<TextBlock x:Name="DynamicText">
    <TextBlock.Style>
        <Style TargetType="TextBlock" BasedOn="{StaticResource Body2TextStyle}">
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsImportant}" Value="True">
                    <Setter Property="FontSize" Value="{StaticResource Body1FontSize}"/>
                    <Setter Property="FontWeight" Value="{StaticResource MediumFontWeight}"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </TextBlock.Style>
</TextBlock>
```

### 2. 响应式字体
根据窗口大小调整字体：

```xml
<TextBlock Text="响应式标题">
    <TextBlock.Style>
        <Style TargetType="TextBlock" BasedOn="{StaticResource H2TextStyle}">
            <Style.Triggers>
                <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=Window}, Path=ActualWidth, Converter={StaticResource WidthToSizeConverter}}" Value="Small">
                    <Setter Property="FontSize" Value="{StaticResource H3FontSize}"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </TextBlock.Style>
</TextBlock>
```

### 3. 文本动画
为文本添加动画效果：

```xml
<TextBlock Style="{StaticResource H1TextStyle}" Text="动画标题">
    <TextBlock.Triggers>
        <EventTrigger RoutedEvent="Loaded">
            <BeginStoryboard>
                <Storyboard>
                    <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                   From="0" To="1" Duration="0:0:0.5"/>
                    <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                                   From="20" To="0" Duration="0:0:0.5"/>
                </Storyboard>
            </BeginStoryboard>
        </EventTrigger>
    </TextBlock.Triggers>
    <TextBlock.RenderTransform>
        <TranslateTransform/>
    </TextBlock.RenderTransform>
</TextBlock>
```

## 常见问题

### Q: 如何在代码中动态设置字体样式？
A: 可以通过FindResource方法获取样式资源：

```csharp
// 在代码中设置字体样式
var textBlock = new TextBlock();
textBlock.Style = (Style)FindResource("H1TextStyle");
textBlock.Text = "动态标题";
```

### Q: 如何创建自定义字体大小？
A: 建议基于现有的字体大小资源创建：

```xml
<system:Double x:Key="CustomFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">15</system:Double>

<Style x:Key="CustomTextStyle" TargetType="TextBlock" BasedOn="{StaticResource Body2TextStyle}">
    <Setter Property="FontSize" Value="{StaticResource CustomFontSize}"/>
</Style>
```

### Q: 如何确保字体在不同操作系统上的兼容性？
A: 字体系统已经包含了备用字体族，会自动回退到系统可用字体：

```xml
<!-- 自动回退机制 -->
<FontFamily x:Key="SafeFontFamily">Segoe UI, Microsoft YaHei UI, Arial, sans-serif</FontFamily>
```

### Q: 如何处理多语言字体显示？
A: 为不同语言设置合适的字体族：

```xml
<Style x:Key="MultiLanguageTextStyle" TargetType="TextBlock">
    <Setter Property="FontFamily" Value="{StaticResource SecondaryFontFamily}"/>
    <Style.Triggers>
        <DataTrigger Binding="{Binding Language}" Value="en">
            <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        </DataTrigger>
        <DataTrigger Binding="{Binding Language}" Value="zh">
            <Setter Property="FontFamily" Value="{StaticResource SecondaryFontFamily}"/>
        </DataTrigger>
    </Style.Triggers>
</Style>
```

### Q: 如何实现文本大小写转换？
A: 由于WPF TextBlock不支持CSS的text-transform属性，需要在代码中处理：

```csharp
// 在ViewModel或代码中转换
public string UppercaseText => OriginalText?.ToUpper();

// 或使用转换器
public class TextCaseConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is string text && parameter is string caseType)
        {
            return caseType.ToLower() switch
            {
                "upper" => text.ToUpper(),
                "lower" => text.ToLower(),
                "title" => CultureInfo.CurrentCulture.TextInfo.ToTitleCase(text.ToLower()),
                _ => text
            };
        }
        return value;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
```

## 性能优化

### 1. 字体缓存
系统会自动缓存字体资源，避免重复加载：

```xml
<!-- 字体资源会被自动缓存 -->
<TextBlock Style="{StaticResource Body2TextStyle}" Text="缓存的字体"/>
```

### 2. 虚拟化支持
在大量文本显示时，使用虚拟化容器：

```xml
<VirtualizingStackPanel>
    <ItemsControl.ItemsPanel>
        <ItemsPanelTemplate>
            <VirtualizingStackPanel/>
        </ItemsPanelTemplate>
    </ItemsControl.ItemsPanel>
</VirtualizingStackPanel>
```

### 3. 文本渲染优化
启用文本渲染优化选项：

```xml
<TextBlock Style="{StaticResource Body2TextStyle}"
           TextOptions.TextFormattingMode="Display"
           TextOptions.TextRenderingMode="ClearType"
           UseLayoutRounding="True"/>
```

## 版本历史

### v1.0.0
- 初始字体系统发布
- 包含完整的字体层级定义
- 支持深色和浅色主题
- 提供基础字体样式

### 未来计划
- 添加更多语言字体支持
- 增强响应式字体功能
- 提供字体大小自适应机制
- 添加更多装饰性字体样式

---

[返回主文档](../README.md)
