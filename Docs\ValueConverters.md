# FluentSystemDesign WPF 值转换器

FluentSystemDesign WPF控件库提供了一套完整的值转换器（Value Converters），用于在XAML绑定中进行数据类型转换和格式化。所有转换器都遵循WPF的IValueConverter接口，支持双向转换（当适用时），并提供丰富的参数化选项。

## 目录

- [布尔转换器](#布尔转换器)
- [数值转换器](#数值转换器)
- [颜色转换器](#颜色转换器)
- [枚举转换器](#枚举转换器)
- [集合转换器](#集合转换器)
- [字符串转换器](#字符串转换器)
- [主题转换器](#主题转换器)
- [使用指南](#使用指南)

## 布尔转换器

### BoolToVisibilityConverter

将布尔值转换为Visibility枚举值，支持反向转换。

**功能特性：**
- 默认行为：true → Visible, false → Collapsed
- 支持反转逻辑
- 支持Hidden模式

**参数选项：**
- `Invert`: 反转逻辑（true → Collapsed, false → Visible）
- `Hidden`: 使用Hidden而不是Collapsed

**XAML示例：**
```xml
<!-- 基本用法 -->
<TextBlock Visibility="{Binding IsVisible, Converter={StaticResource BoolToVisibilityConverter}}" />

<!-- 反转逻辑 -->
<TextBlock Visibility="{Binding IsHidden, Converter={StaticResource BoolToVisibilityConverter}, ConverterParameter=Invert}" />

<!-- 使用Hidden -->
<TextBlock Visibility="{Binding IsVisible, Converter={StaticResource BoolToVisibilityConverter}, ConverterParameter=Hidden}" />
```

### InverseBoolConverter

将布尔值进行反转：true → false, false → true。

**功能特性：**
- 简单的布尔值反转
- 支持双向转换
- 自动类型转换

**XAML示例：**
```xml
<!-- 当IsLoading为true时禁用按钮 -->
<Button IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBoolConverter}}" />

<!-- 当IsEmpty为false时显示内容 -->
<TextBlock Visibility="{Binding IsEmpty, Converter={StaticResource InverseBoolConverter}}" />
```

### BoolToOpacityConverter

将布尔值转换为透明度值，用于控制元素的可见程度。

**功能特性：**
- 默认：true → 1.0, false → 0.0
- 支持自定义透明度值
- 支持反转逻辑

**参数选项：**
- `Invert`: 反转逻辑
- `TrueValue,FalseValue`: 自定义透明度值（如："1.0,0.3"）
- 单个数值: 作为true值，false值为0

**XAML示例：**
```xml
<!-- 基本用法 -->
<TextBlock Opacity="{Binding IsEnabled, Converter={StaticResource BoolToOpacityConverter}}" />

<!-- 自定义透明度 -->
<TextBlock Opacity="{Binding IsHighlighted, Converter={StaticResource BoolToOpacityConverter}, ConverterParameter=1.0,0.5}" />

<!-- 反转逻辑 -->
<TextBlock Opacity="{Binding IsDisabled, Converter={StaticResource BoolToOpacityConverter}, ConverterParameter=Invert}" />
```

## 数值转换器

### NumberToStringConverter

将数值转换为格式化的字符串，支持标准和自定义格式。

**功能特性：**
- 支持所有数值类型
- 标准格式字符串（C、N、P、F等）
- 自定义格式字符串
- 支持双向转换

**参数选项：**
- `C` 或 `C2`: 货币格式
- `N` 或 `N2`: 数字格式（带千位分隔符）
- `P` 或 `P2`: 百分比格式
- `F` 或 `F2`: 固定点格式
- 自定义格式字符串

**XAML示例：**
```xml
<!-- 货币格式 -->
<TextBlock Text="{Binding Price, Converter={StaticResource NumberToStringConverter}, ConverterParameter=C2}" />

<!-- 百分比格式 -->
<TextBlock Text="{Binding Percentage, Converter={StaticResource NumberToStringConverter}, ConverterParameter=P1}" />

<!-- 整数格式 -->
<TextBlock Text="{Binding Count, Converter={StaticResource NumberToStringConverter}, ConverterParameter=N0}" />
```

### PercentageConverter

专门用于百分比转换的转换器。

**功能特性：**
- 0-1范围到百分比的转换
- 支持0-100范围输入
- 自定义小数位数
- 支持双向转换

**参数选项：**
- 数字: 小数位数（如："2"）
- `0-100`: 输入范围为0-100而不是0-1

**XAML示例：**
```xml
<!-- 基本百分比 -->
<TextBlock Text="{Binding Progress, Converter={StaticResource PercentageConverter}}" />

<!-- 保留1位小数 -->
<TextBlock Text="{Binding Progress, Converter={StaticResource PercentageConverter}, ConverterParameter=1}" />

<!-- 0-100范围输入 -->
<TextBlock Text="{Binding Score, Converter={StaticResource PercentageConverter}, ConverterParameter=0-100}" />
```

### ThicknessConverter

将数值转换为Thickness对象，支持多种输入格式。

**功能特性：**
- 单个数值到四边相等的Thickness
- 多值格式支持
- 方向性应用
- 支持双向转换

**参数选项：**
- `Left`, `Top`, `Right`, `Bottom`: 单边应用
- `Horizontal`, `Vertical`: 方向性应用
- `All`: 四边应用（默认）

**XAML示例：**
```xml
<!-- 四边相等 -->
<Border BorderThickness="{Binding BorderSize, Converter={StaticResource ThicknessConverter}}" />

<!-- 水平方向 -->
<Border Margin="{Binding Spacing, Converter={StaticResource ThicknessConverter}, ConverterParameter=Horizontal}" />

<!-- 左边距 -->
<Border Padding="{Binding Padding, Converter={StaticResource ThicknessConverter}, ConverterParameter=Left}" />
```

## 颜色转换器

### ColorToBrushConverter

将Color对象转换为SolidColorBrush，支持透明度调整。

**功能特性：**
- Color到SolidColorBrush转换
- 十六进制字符串支持
- 透明度调整
- 画刷冻结优化

**参数选项：**
- `Alpha=值`: 设置透明度（0-1）

**XAML示例：**
```xml
<!-- 基本转换 -->
<Rectangle Fill="{Binding BackgroundColor, Converter={StaticResource ColorToBrushConverter}}" />

<!-- 设置透明度 -->
<Rectangle Fill="{Binding BackgroundColor, Converter={StaticResource ColorToBrushConverter}, ConverterParameter=Alpha=0.5}" />

<!-- 十六进制字符串 -->
<Border Background="{Binding HexColor, Converter={StaticResource ColorToBrushConverter}}" />
```

### HexToColorConverter

将十六进制字符串转换为Color对象。

**功能特性：**
- 多种十六进制格式支持
- 带/不带#前缀
- 3、6、8位格式支持
- 支持双向转换

**支持格式：**
- `#RGB` (如: #F0A)
- `#RRGGBB` (如: #FF00AA)
- `#AARRGGBB` (如: #80FF00AA)
- 不带#前缀的格式

**XAML示例：**
```xml
<!-- 基本转换 -->
<Rectangle Fill="{Binding HexColor, Converter={StaticResource HexToColorConverter}}" />

<!-- 边框颜色 -->
<Border BorderBrush="{Binding BorderHex, Converter={StaticResource HexToColorConverter}}" />
```

### ColorToContrastConverter

根据输入颜色的亮度自动选择对比度最佳的前景色。

**功能特性：**
- 自动亮度计算
- 对比色选择
- 自定义对比色
- 支持多种输入类型

**参数选项：**
- `Light=#颜色,Dark=#颜色`: 自定义对比色

**XAML示例：**
```xml
<!-- 自动对比色 -->
<TextBlock Foreground="{Binding BackgroundColor, Converter={StaticResource ColorToContrastConverter}}" />

<!-- 自定义对比色 -->
<TextBlock Foreground="{Binding BackgroundColor, Converter={StaticResource ColorToContrastConverter}, ConverterParameter=Light=#FFFFFF,Dark=#000000}" />
```

## 枚举转换器

### EnumToStringConverter

将枚举值转换为字符串，支持Description特性。

**功能特性：**
- Description特性支持
- 本地化支持
- 大小写转换
- 支持双向转换

**参数选项：**
- `UseDescription`: 强制使用Description特性
- `UseName`: 强制使用枚举名称
- `ToUpper`: 转换为大写
- `ToLower`: 转换为小写

**XAML示例：**
```xml
<!-- 基本转换 -->
<TextBlock Text="{Binding Status, Converter={StaticResource EnumToStringConverter}}" />

<!-- 使用Description -->
<TextBlock Text="{Binding Status, Converter={StaticResource EnumToStringConverter}, ConverterParameter=UseDescription}" />

<!-- 转换为大写 -->
<TextBlock Text="{Binding Status, Converter={StaticResource EnumToStringConverter}, ConverterParameter=ToUpper}" />
```

### EnumToBoolConverter

将枚举值与指定值比较，相等时返回true。

**功能特性：**
- 单值或多值比较
- 反转逻辑支持
- RadioButton绑定优化
- 支持双向转换

**参数选项：**
- 枚举值名称: 单个比较值
- `值1,值2,值3`: 多个比较值
- `Invert:值`: 反转逻辑

**XAML示例：**
```xml
<!-- RadioButton绑定 -->
<RadioButton IsChecked="{Binding Status, Converter={StaticResource EnumToBoolConverter}, ConverterParameter=Active}" />

<!-- 多值比较 -->
<RadioButton IsChecked="{Binding Status, Converter={StaticResource EnumToBoolConverter}, ConverterParameter=Inactive,Disabled}" />

<!-- 反转逻辑 -->
<CheckBox IsChecked="{Binding Status, Converter={StaticResource EnumToBoolConverter}, ConverterParameter=Invert:Active}" />
```

### EnumToVisibilityConverter

将枚举值与指定值比较，匹配时显示。

**功能特性：**
- 可见性控制
- 多值匹配
- Hidden/Collapsed选择
- 反转逻辑

**参数选项：**
- 枚举值名称: 匹配时显示
- `值1,值2`: 多值匹配
- `Invert:值`: 反转逻辑
- `,Hidden`: 使用Hidden而不是Collapsed

**XAML示例：**
```xml
<!-- 基本可见性控制 -->
<TextBlock Visibility="{Binding Status, Converter={StaticResource EnumToVisibilityConverter}, ConverterParameter=Active}" />

<!-- 多值匹配 -->
<Button Visibility="{Binding Status, Converter={StaticResource EnumToVisibilityConverter}, ConverterParameter=Active,Processing}" />

<!-- 反转逻辑 -->
<ProgressBar Visibility="{Binding Status, Converter={StaticResource EnumToVisibilityConverter}, ConverterParameter=Invert:Completed,Hidden}" />
```

## 集合转换器

### CollectionToVisibilityConverter

根据集合是否为空来控制元素的可见性。

**功能特性：**
- 集合空值检查
- 元素数量阈值
- 反转逻辑支持
- Hidden/Collapsed选择

**参数选项：**
- `Invert`: 反转逻辑（空时显示，有元素时隐藏）
- `Hidden`: 使用Hidden而不是Collapsed
- `MinCount=N`: 最小元素数量阈值
- `MaxCount=N`: 最大元素数量阈值

**XAML示例：**
```xml
<!-- 有数据时显示 -->
<TextBlock Visibility="{Binding Items, Converter={StaticResource CollectionToVisibilityConverter}}" Text="有数据" />

<!-- 无数据时显示 -->
<TextBlock Visibility="{Binding Items, Converter={StaticResource CollectionToVisibilityConverter}, ConverterParameter=Invert}" Text="无数据" />

<!-- 最少5个元素时显示 -->
<Button Visibility="{Binding Items, Converter={StaticResource CollectionToVisibilityConverter}, ConverterParameter=MinCount=5}" />
```

### CountToVisibilityConverter

根据数值与阈值的比较结果来控制元素的可见性。

**功能特性：**
- 多种比较操作
- 范围比较支持
- 反转逻辑
- Hidden/Collapsed选择

**比较操作：**
- `>` 或 `GreaterThan`: 大于
- `>=` 或 `GreaterThanOrEqual`: 大于等于
- `<` 或 `LessThan`: 小于
- `<=` 或 `LessThanOrEqual`: 小于等于
- `=` 或 `Equal`: 等于
- `!=` 或 `NotEqual`: 不等于
- `Range:min,max`: 范围内

**XAML示例：**
```xml
<!-- 大于0时显示 -->
<TextBlock Visibility="{Binding ItemCount, Converter={StaticResource CountToVisibilityConverter}, ConverterParameter=>0}" />

<!-- 大于等于5时显示 -->
<Button Visibility="{Binding SelectedCount, Converter={StaticResource CountToVisibilityConverter}, ConverterParameter=>=5}" />

<!-- 在1-99范围内时显示 -->
<ProgressBar Visibility="{Binding Progress, Converter={StaticResource CountToVisibilityConverter}, ConverterParameter=Range:1,99}" />
```

### IsNullOrEmptyConverter

检查对象是否为null、空字符串或空集合。

**功能特性：**
- 多类型空值检查
- 字符串空白处理
- 反转逻辑支持
- 可见性转换

**检查规则：**
- null → true
- 空字符串 → true
- 空集合 → true
- 数值0 → true（可选）

**参数选项：**
- `Invert`: 反转结果
- `TrimString`: 对字符串进行Trim
- `IgnoreWhitespace`: 将空白字符串视为空

**XAML示例：**
```xml
<!-- 非空时启用按钮 -->
<Button IsEnabled="{Binding UserName, Converter={StaticResource IsNullOrEmptyConverter}, ConverterParameter=Invert}" />

<!-- 空时显示提示 -->
<TextBlock Visibility="{Binding Items, Converter={StaticResource IsNullOrEmptyConverter}}" Text="无数据" />

<!-- 忽略空白字符 -->
<TextBox BorderBrush="Red" Visibility="{Binding ErrorMessage, Converter={StaticResource IsNullOrEmptyConverter}, ConverterParameter=Invert,IgnoreWhitespace}" />
```

## 字符串转换器

### StringToUpperConverter

将字符串转换为大写形式。

**功能特性：**
- 全部大写转换
- 首字母大写
- 标题格式转换
- 区域性支持

**参数选项：**
- `All`: 全部转换为大写（默认）
- `FirstOnly`: 只将首字母转换为大写
- `Words`: 每个单词首字母大写（标题格式）
- `Invariant`: 使用不变区域性
- `Current`: 使用当前区域性

**XAML示例：**
```xml
<!-- 全部大写 -->
<TextBlock Text="{Binding UserName, Converter={StaticResource StringToUpperConverter}}" />

<!-- 标题格式 -->
<TextBlock Text="{Binding Title, Converter={StaticResource StringToUpperConverter}, ConverterParameter=Words}" />

<!-- 不变区域性 -->
<TextBlock Text="{Binding Code, Converter={StaticResource StringToUpperConverter}, ConverterParameter=Invariant}" />
```

### StringToLowerConverter

将字符串转换为小写形式。

**功能特性：**
- 全部小写转换
- 首字母小写
- 驼峰命名转换
- 区域性支持

**参数选项：**
- `All`: 全部转换为小写（默认）
- `FirstOnly`: 只将首字母转换为小写
- `CamelCase`: 转换为驼峰命名格式
- `Invariant`: 使用不变区域性
- `Current`: 使用当前区域性

**XAML示例：**
```xml
<!-- 全部小写 -->
<TextBlock Text="{Binding UserName, Converter={StaticResource StringToLowerConverter}}" />

<!-- 驼峰命名 -->
<TextBlock Text="{Binding PropertyName, Converter={StaticResource StringToLowerConverter}, ConverterParameter=CamelCase}" />

<!-- 不变区域性 -->
<TextBlock Text="{Binding Code, Converter={StaticResource StringToLowerConverter}, ConverterParameter=Invariant}" />
```

### StringFormatConverter

使用指定的格式字符串对输入值进行格式化。

**功能特性：**
- 复合格式字符串支持
- 空值处理
- 字符串修剪
- 支持双向转换

**参数选项：**
- 格式字符串: 如 `"价格: {0:C}"`
- `Null=文本`: null值替换文本
- `Empty=文本`: 空字符串替换文本
- `Trim`: 对字符串进行Trim

**XAML示例：**
```xml
<!-- 价格格式化 -->
<TextBlock Text="{Binding Price, Converter={StaticResource StringFormatConverter}, ConverterParameter='价格: {0:C}'}" />

<!-- 数量格式化 -->
<TextBlock Text="{Binding Count, Converter={StaticResource StringFormatConverter}, ConverterParameter='共 {0} 项'}" />

<!-- 空值处理 -->
<TextBlock Text="{Binding UserName, Converter={StaticResource StringFormatConverter}, ConverterParameter='用户: {0},Null=未登录'}" />
```

## 主题转换器

### ThemeToColorConverter

根据当前主题（深色/浅色）返回相应的颜色值。

**功能特性：**
- 主题自动检测
- 预定义颜色类型
- 自定义颜色映射
- 系统主题支持

**主题类型：**
- `Light`: 浅色主题
- `Dark`: 深色主题
- `Auto`: 自动检测系统主题

**参数选项：**
- `Light=#颜色,Dark=#颜色`: 自定义颜色映射
- `Primary`: 主色调变体
- `Secondary`: 次要色调变体
- `Accent`: 强调色变体
- `Background`: 背景色变体
- `Text`: 文本色变体

**XAML示例：**
```xml
<!-- 自定义颜色映射 -->
<Rectangle Fill="{Binding CurrentTheme, Converter={StaticResource ThemeToColorConverter}, ConverterParameter=Light=#FFFFFF,Dark=#1E1E1E}" />

<!-- 文本颜色 -->
<TextBlock Foreground="{Binding CurrentTheme, Converter={StaticResource ThemeToColorConverter}, ConverterParameter=Text}" />

<!-- 主色调 -->
<Border Background="{Binding CurrentTheme, Converter={StaticResource ThemeToColorConverter}, ConverterParameter=Primary}" />
```

### ThemeToBrushConverter

根据当前主题（深色/浅色）返回相应的画刷对象。

**功能特性：**
- 主题画刷映射
- 透明度控制
- 画刷冻结优化
- 资源键支持

**参数选项：**
- `Light=#颜色,Dark=#颜色`: 自定义画刷映射
- `Primary`, `Secondary`, `Accent`: 预定义画刷类型
- `Background`, `Text`, `Surface`: 语义画刷类型
- `Opacity=值`: 设置透明度
- `Freeze`: 冻结画刷

**XAML示例：**
```xml
<!-- 背景画刷 -->
<Rectangle Fill="{Binding CurrentTheme, Converter={StaticResource ThemeToBrushConverter}, ConverterParameter=Background}" />

<!-- 文本画刷 -->
<TextBlock Foreground="{Binding CurrentTheme, Converter={StaticResource ThemeToBrushConverter}, ConverterParameter=Text}" />

<!-- 半透明主色调 -->
<Border Background="{Binding CurrentTheme, Converter={StaticResource ThemeToBrushConverter}, ConverterParameter=Primary,Opacity=0.1}" />
```

## 使用指南

### 在资源字典中声明转换器

```xml
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:converters="clr-namespace:FluentSystemDesign.WPF.Converters">

    <!-- 布尔转换器 -->
    <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter" />
    <converters:InverseBoolConverter x:Key="InverseBoolConverter" />
    <converters:BoolToOpacityConverter x:Key="BoolToOpacityConverter" />

    <!-- 数值转换器 -->
    <converters:NumberToStringConverter x:Key="NumberToStringConverter" />
    <converters:PercentageConverter x:Key="PercentageConverter" />
    <converters:ThicknessConverter x:Key="ThicknessConverter" />

    <!-- 颜色转换器 -->
    <converters:ColorToBrushConverter x:Key="ColorToBrushConverter" />
    <converters:HexToColorConverter x:Key="HexToColorConverter" />
    <converters:ColorToContrastConverter x:Key="ColorToContrastConverter" />

    <!-- 枚举转换器 -->
    <converters:EnumToStringConverter x:Key="EnumToStringConverter" />
    <converters:EnumToBoolConverter x:Key="EnumToBoolConverter" />
    <converters:EnumToVisibilityConverter x:Key="EnumToVisibilityConverter" />

    <!-- 集合转换器 -->
    <converters:CollectionToVisibilityConverter x:Key="CollectionToVisibilityConverter" />
    <converters:CountToVisibilityConverter x:Key="CountToVisibilityConverter" />
    <converters:IsNullOrEmptyConverter x:Key="IsNullOrEmptyConverter" />

    <!-- 字符串转换器 -->
    <converters:StringToUpperConverter x:Key="StringToUpperConverter" />
    <converters:StringToLowerConverter x:Key="StringToLowerConverter" />
    <converters:StringFormatConverter x:Key="StringFormatConverter" />

    <!-- 主题转换器 -->
    <converters:ThemeToColorConverter x:Key="ThemeToColorConverter" />
    <converters:ThemeToBrushConverter x:Key="ThemeToBrushConverter" />

</ResourceDictionary>
```

### 性能优化建议

1. **使用静态资源**: 在ResourceDictionary中声明转换器实例，避免重复创建
2. **参数缓存**: 对于复杂的参数解析，转换器内部会进行缓存优化
3. **画刷冻结**: 颜色转换器会自动冻结创建的画刷以提高性能
4. **类型检查**: 转换器会进行输入类型验证，避免不必要的转换操作

### 错误处理

所有转换器都实现了完善的错误处理机制：

- **输入验证**: 检查输入值的类型和有效性
- **参数验证**: 验证转换参数的格式和范围
- **异常处理**: 捕获转换过程中的异常，返回合理的默认值
- **回退机制**: 提供默认值和回退逻辑

### 扩展和自定义

如果需要自定义转换器行为，可以：

1. **继承现有转换器**: 重写特定方法来修改行为
2. **设置属性**: 通过转换器的公共属性调整默认行为
3. **组合使用**: 结合多个转换器实现复杂的转换逻辑
4. **创建新转换器**: 参考现有转换器的实现模式

---

更多信息请参考 [FluentSystemDesign WPF 主文档](../README.md)。
