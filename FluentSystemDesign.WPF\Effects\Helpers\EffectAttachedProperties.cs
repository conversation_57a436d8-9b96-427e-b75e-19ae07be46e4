using System.Windows;
using System.Windows.Media;
using System.Windows.Media.Effects;

namespace FluentSystemDesign.WPF.Effects.Helpers
{
    /// <summary>
    /// 提供效果相关的附加属性，用于在XAML中方便地应用视觉效果
    /// </summary>
    public static class EffectAttachedProperties
    {
        #region Elevation 附加属性

        /// <summary>
        /// Elevation 附加属性
        /// </summary>
        public static readonly DependencyProperty ElevationProperty =
            DependencyProperty.RegisterAttached(
                "Elevation",
                typeof(int),
                typeof(EffectAttachedProperties),
                new PropertyMetadata(0, OnElevationChanged));

        /// <summary>
        /// 获取元素的高度级别
        /// </summary>
        /// <param name="obj">目标元素</param>
        /// <returns>高度级别</returns>
        public static int GetElevation(DependencyObject obj)
        {
            return (int)obj.GetValue(ElevationProperty);
        }

        /// <summary>
        /// 设置元素的高度级别
        /// </summary>
        /// <param name="obj">目标元素</param>
        /// <param name="value">高度级别</param>
        public static void SetElevation(DependencyObject obj, int value)
        {
            obj.SetValue(ElevationProperty, value);
        }

        private static void OnElevationChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is FrameworkElement element)
            {
                var elevation = (int)e.NewValue;
                var isDarkTheme = GetIsDarkTheme(element);
                
                if (elevation > 0)
                {
                    element.Effect = EffectHelper.CreateElevationShadow(elevation, isDarkTheme);
                }
                else
                {
                    element.Effect = null;
                }
            }
        }

        #endregion

        #region IsDarkTheme 附加属性

        /// <summary>
        /// IsDarkTheme 附加属性
        /// </summary>
        public static readonly DependencyProperty IsDarkThemeProperty =
            DependencyProperty.RegisterAttached(
                "IsDarkTheme",
                typeof(bool),
                typeof(EffectAttachedProperties),
                new PropertyMetadata(false, OnIsDarkThemeChanged));

        /// <summary>
        /// 获取是否为深色主题
        /// </summary>
        /// <param name="obj">目标元素</param>
        /// <returns>是否为深色主题</returns>
        public static bool GetIsDarkTheme(DependencyObject obj)
        {
            return (bool)obj.GetValue(IsDarkThemeProperty);
        }

        /// <summary>
        /// 设置是否为深色主题
        /// </summary>
        /// <param name="obj">目标元素</param>
        /// <param name="value">是否为深色主题</param>
        public static void SetIsDarkTheme(DependencyObject obj, bool value)
        {
            obj.SetValue(IsDarkThemeProperty, value);
        }

        private static void OnIsDarkThemeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is FrameworkElement element)
            {
                var elevation = GetElevation(element);
                if (elevation > 0)
                {
                    var isDarkTheme = (bool)e.NewValue;
                    element.Effect = EffectHelper.CreateElevationShadow(elevation, isDarkTheme);
                }
            }
        }

        #endregion

        #region GlowColor 附加属性

        /// <summary>
        /// GlowColor 附加属性
        /// </summary>
        public static readonly DependencyProperty GlowColorProperty =
            DependencyProperty.RegisterAttached(
                "GlowColor",
                typeof(Color?),
                typeof(EffectAttachedProperties),
                new PropertyMetadata(null, OnGlowColorChanged));

        /// <summary>
        /// 获取发光颜色
        /// </summary>
        /// <param name="obj">目标元素</param>
        /// <returns>发光颜色</returns>
        public static Color? GetGlowColor(DependencyObject obj)
        {
            return (Color?)obj.GetValue(GlowColorProperty);
        }

        /// <summary>
        /// 设置发光颜色
        /// </summary>
        /// <param name="obj">目标元素</param>
        /// <param name="value">发光颜色</param>
        public static void SetGlowColor(DependencyObject obj, Color? value)
        {
            obj.SetValue(GlowColorProperty, value);
        }

        private static void OnGlowColorChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is FrameworkElement element)
            {
                var color = (Color?)e.NewValue;
                var intensity = GetGlowIntensity(element);
                var radius = GetGlowRadius(element);

                if (color.HasValue)
                {
                    element.Effect = EffectHelper.CreateGlowEffect(color.Value, intensity, radius);
                }
                else
                {
                    element.Effect = null;
                }
            }
        }

        #endregion

        #region GlowIntensity 附加属性

        /// <summary>
        /// GlowIntensity 附加属性
        /// </summary>
        public static readonly DependencyProperty GlowIntensityProperty =
            DependencyProperty.RegisterAttached(
                "GlowIntensity",
                typeof(double),
                typeof(EffectAttachedProperties),
                new PropertyMetadata(0.5, OnGlowIntensityChanged));

        /// <summary>
        /// 获取发光强度
        /// </summary>
        /// <param name="obj">目标元素</param>
        /// <returns>发光强度</returns>
        public static double GetGlowIntensity(DependencyObject obj)
        {
            return (double)obj.GetValue(GlowIntensityProperty);
        }

        /// <summary>
        /// 设置发光强度
        /// </summary>
        /// <param name="obj">目标元素</param>
        /// <param name="value">发光强度</param>
        public static void SetGlowIntensity(DependencyObject obj, double value)
        {
            obj.SetValue(GlowIntensityProperty, value);
        }

        private static void OnGlowIntensityChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is FrameworkElement element)
            {
                var color = GetGlowColor(element);
                if (color.HasValue)
                {
                    var intensity = (double)e.NewValue;
                    var radius = GetGlowRadius(element);
                    element.Effect = EffectHelper.CreateGlowEffect(color.Value, intensity, radius);
                }
            }
        }

        #endregion

        #region GlowRadius 附加属性

        /// <summary>
        /// GlowRadius 附加属性
        /// </summary>
        public static readonly DependencyProperty GlowRadiusProperty =
            DependencyProperty.RegisterAttached(
                "GlowRadius",
                typeof(double),
                typeof(EffectAttachedProperties),
                new PropertyMetadata(10.0, OnGlowRadiusChanged));

        /// <summary>
        /// 获取发光半径
        /// </summary>
        /// <param name="obj">目标元素</param>
        /// <returns>发光半径</returns>
        public static double GetGlowRadius(DependencyObject obj)
        {
            return (double)obj.GetValue(GlowRadiusProperty);
        }

        /// <summary>
        /// 设置发光半径
        /// </summary>
        /// <param name="obj">目标元素</param>
        /// <param name="value">发光半径</param>
        public static void SetGlowRadius(DependencyObject obj, double value)
        {
            obj.SetValue(GlowRadiusProperty, value);
        }

        private static void OnGlowRadiusChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is FrameworkElement element)
            {
                var color = GetGlowColor(element);
                if (color.HasValue)
                {
                    var intensity = GetGlowIntensity(element);
                    var radius = (double)e.NewValue;
                    element.Effect = EffectHelper.CreateGlowEffect(color.Value, intensity, radius);
                }
            }
        }

        #endregion

        #region BlurRadius 附加属性

        /// <summary>
        /// BlurRadius 附加属性
        /// </summary>
        public static readonly DependencyProperty BlurRadiusProperty =
            DependencyProperty.RegisterAttached(
                "BlurRadius",
                typeof(double),
                typeof(EffectAttachedProperties),
                new PropertyMetadata(0.0, OnBlurRadiusChanged));

        /// <summary>
        /// 获取模糊半径
        /// </summary>
        /// <param name="obj">目标元素</param>
        /// <returns>模糊半径</returns>
        public static double GetBlurRadius(DependencyObject obj)
        {
            return (double)obj.GetValue(BlurRadiusProperty);
        }

        /// <summary>
        /// 设置模糊半径
        /// </summary>
        /// <param name="obj">目标元素</param>
        /// <param name="value">模糊半径</param>
        public static void SetBlurRadius(DependencyObject obj, double value)
        {
            obj.SetValue(BlurRadiusProperty, value);
        }

        private static void OnBlurRadiusChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is FrameworkElement element)
            {
                var radius = (double)e.NewValue;
                if (radius > 0)
                {
                    element.Effect = EffectHelper.CreateBlurEffect(radius);
                }
                else
                {
                    element.Effect = null;
                }
            }
        }

        #endregion
    }
}
