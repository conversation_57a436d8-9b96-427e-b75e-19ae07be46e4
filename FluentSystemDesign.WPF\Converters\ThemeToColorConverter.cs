using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;

namespace FluentSystemDesign.WPF.Converters
{
    /// <summary>
    /// 主题到颜色转换器
    /// 根据当前主题（深色/浅色）返回相应的颜色值
    /// </summary>
    /// <remarks>
    /// 支持的主题类型：
    /// - "Light": 浅色主题
    /// - "Dark": 深色主题
    /// - "Auto": 自动检测系统主题
    /// 
    /// 参数格式：
    /// - "Light=#FFFFFF,Dark=#000000": 指定浅色和深色主题的颜色
    /// - "Primary": 使用主色调的浅色/深色变体
    /// - "Secondary": 使用次要色调的浅色/深色变体
    /// - "Accent": 使用强调色的浅色/深色变体
    /// - "Background": 使用背景色的浅色/深色变体
    /// - "Text": 使用文本色的浅色/深色变体
    /// </remarks>
    /// <example>
    /// 在XAML中使用：
    /// <code>
    /// &lt;Rectangle Fill="{Binding CurrentTheme, Converter={StaticResource ThemeToColorConverter}, ConverterParameter=Light=#FFFFFF,Dark=#1E1E1E}"/&gt;
    /// &lt;TextBlock Foreground="{Binding CurrentTheme, Converter={StaticResource ThemeToColorConverter}, ConverterParameter=Text}"/&gt;
    /// &lt;Border Background="{Binding CurrentTheme, Converter={StaticResource ThemeToColorConverter}, ConverterParameter=Primary}"/&gt;
    /// </code>
    /// </example>
    [ValueConversion(typeof(string), typeof(Color))]
    [ValueConversion(typeof(ApplicationTheme), typeof(Color))]
    public class ThemeToColorConverter : IValueConverter
    {
        /// <summary>
        /// 默认浅色主题颜色
        /// </summary>
        public Color DefaultLightColor { get; set; } = Colors.White;

        /// <summary>
        /// 默认深色主题颜色
        /// </summary>
        public Color DefaultDarkColor { get; set; } = Color.FromRgb(30, 30, 30);

        /// <summary>
        /// 将主题值转换为颜色
        /// </summary>
        /// <param name="value">主题值（字符串或ApplicationTheme枚举）</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">颜色参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>对应主题的颜色</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var isDarkTheme = DetermineIsDarkTheme(value);
            var (lightColor, darkColor) = ParseColorParameter(parameter?.ToString());

            var resultColor = isDarkTheme ? darkColor : lightColor;

            // 如果目标类型是Brush，返回SolidColorBrush
            if (targetType == typeof(Brush) || targetType == typeof(SolidColorBrush))
            {
                return new SolidColorBrush(resultColor);
            }

            return resultColor;
        }

        /// <summary>
        /// 反向转换（不支持）
        /// </summary>
        /// <param name="value">颜色值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>不支持的操作异常</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotSupportedException("ThemeToColorConverter does not support ConvertBack operation.");
        }

        /// <summary>
        /// 确定是否为深色主题
        /// </summary>
        /// <param name="themeValue">主题值</param>
        /// <returns>是否为深色主题</returns>
        private static bool DetermineIsDarkTheme(object themeValue)
        {
            if (themeValue is ApplicationTheme appTheme)
            {
                return appTheme switch
                {
                    ApplicationTheme.Dark => true,
                    ApplicationTheme.Light => false,
                    ApplicationTheme.Auto => IsSystemDarkTheme(),
                    _ => false
                };
            }

            if (themeValue is string themeString)
            {
                return themeString.ToLowerInvariant() switch
                {
                    "dark" => true,
                    "light" => false,
                    "auto" => IsSystemDarkTheme(),
                    _ => false
                };
            }

            // 如果是布尔值，直接返回
            if (themeValue is bool isDark)
            {
                return isDark;
            }

            // 默认为浅色主题
            return false;
        }

        /// <summary>
        /// 检测系统是否使用深色主题
        /// </summary>
        /// <returns>系统是否使用深色主题</returns>
        private static bool IsSystemDarkTheme()
        {
            try
            {
                // 尝试从注册表读取系统主题设置
                using var key = Microsoft.Win32.Registry.CurrentUser.OpenSubKey(@"Software\Microsoft\Windows\CurrentVersion\Themes\Personalize");
                var value = key?.GetValue("AppsUseLightTheme");
                if (value is int intValue)
                {
                    return intValue == 0; // 0表示深色主题，1表示浅色主题
                }
            }
            catch
            {
                // 如果无法读取注册表，使用默认值
            }

            // 默认返回浅色主题
            return false;
        }

        /// <summary>
        /// 解析颜色参数
        /// </summary>
        /// <param name="parameter">参数字符串</param>
        /// <returns>浅色和深色主题的颜色</returns>
        private (Color lightColor, Color darkColor) ParseColorParameter(string parameter)
        {
            var lightColor = DefaultLightColor;
            var darkColor = DefaultDarkColor;

            if (string.IsNullOrWhiteSpace(parameter))
            {
                return (lightColor, darkColor);
            }

            var lowerParam = parameter.ToLowerInvariant();

            // 处理预定义的颜色类型
            if (lowerParam == "primary")
            {
                lightColor = GetThemeColor("Primary500", Colors.Blue);
                darkColor = GetThemeColor("Primary300", Colors.LightBlue);
            }
            else if (lowerParam == "secondary")
            {
                lightColor = GetThemeColor("Secondary500", Colors.Purple);
                darkColor = GetThemeColor("Secondary300", Colors.Plum);
            }
            else if (lowerParam == "accent")
            {
                lightColor = GetThemeColor("Accent500", Colors.Orange);
                darkColor = GetThemeColor("Accent300", Colors.LightSalmon);
            }
            else if (lowerParam == "background")
            {
                lightColor = GetThemeColor("Neutral50", Colors.White);
                darkColor = GetThemeColor("Neutral900", Color.FromRgb(33, 33, 33));
            }
            else if (lowerParam == "text")
            {
                lightColor = GetThemeColor("Neutral900", Colors.Black);
                darkColor = GetThemeColor("Neutral100", Colors.White);
            }
            else if (lowerParam == "surface")
            {
                lightColor = Colors.White;
                darkColor = Color.FromRgb(48, 48, 48);
            }
            else if (parameter.Contains(","))
            {
                // 解析自定义颜色格式："Light=#FFFFFF,Dark=#000000"
                var parts = parameter.Split(',');
                foreach (var part in parts)
                {
                    var keyValue = part.Split('=');
                    if (keyValue.Length == 2)
                    {
                        var key = keyValue[0].Trim().ToLowerInvariant();
                        var value = keyValue[1].Trim();

                        if (TryParseColor(value, out Color parsedColor))
                        {
                            switch (key)
                            {
                                case "light":
                                    lightColor = parsedColor;
                                    break;
                                case "dark":
                                    darkColor = parsedColor;
                                    break;
                            }
                        }
                    }
                }
            }
            else
            {
                // 尝试解析单个颜色值（同时用于浅色和深色主题）
                if (TryParseColor(parameter, out Color singleColor))
                {
                    lightColor = singleColor;
                    darkColor = singleColor;
                }
            }

            return (lightColor, darkColor);
        }

        /// <summary>
        /// 从应用程序资源中获取主题颜色
        /// </summary>
        /// <param name="resourceKey">资源键</param>
        /// <param name="fallbackColor">回退颜色</param>
        /// <returns>主题颜色</returns>
        private static Color GetThemeColor(string resourceKey, Color fallbackColor)
        {
            try
            {
                if (Application.Current?.Resources[resourceKey] is Color color)
                {
                    return color;
                }
            }
            catch
            {
                // 忽略异常，使用回退颜色
            }

            return fallbackColor;
        }

        /// <summary>
        /// 尝试解析颜色字符串
        /// </summary>
        /// <param name="colorString">颜色字符串</param>
        /// <param name="color">解析出的颜色</param>
        /// <returns>是否解析成功</returns>
        private static bool TryParseColor(string colorString, out Color color)
        {
            color = Colors.Transparent;

            try
            {
                var parsedColor = ColorConverter.ConvertFromString(colorString);
                if (parsedColor is Color colorValue)
                {
                    color = colorValue;
                    return true;
                }
            }
            catch
            {
                // 忽略异常
            }

            return false;
        }
    }

    /// <summary>
    /// 应用程序主题枚举
    /// </summary>
    public enum ApplicationTheme
    {
        /// <summary>
        /// 浅色主题
        /// </summary>
        Light,

        /// <summary>
        /// 深色主题
        /// </summary>
        Dark,

        /// <summary>
        /// 自动检测系统主题
        /// </summary>
        Auto
    }
}
