using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using Microsoft.Xaml.Behaviors;

namespace FluentSystemDesign.WPF.Behaviors
{
    /// <summary>
    /// 水印行为，为文本输入控件提供水印功能
    /// </summary>
    public class WatermarkBehavior : Behavior<Control>
    {
        #region 依赖属性

        /// <summary>
        /// 水印文本
        /// </summary>
        public static readonly DependencyProperty WatermarkTextProperty =
            DependencyProperty.Register(nameof(WatermarkText), typeof(string), typeof(WatermarkBehavior),
                new PropertyMetadata(string.Empty, OnWatermarkTextChanged));

        /// <summary>
        /// 水印字体大小
        /// </summary>
        public static readonly DependencyProperty WatermarkFontSizeProperty =
            DependencyProperty.Register(nameof(WatermarkFontSize), typeof(double), typeof(WatermarkBehavior),
                new PropertyMetadata(12.0, OnWatermarkStyleChanged));

        /// <summary>
        /// 水印字体样式
        /// </summary>
        public static readonly DependencyProperty WatermarkFontStyleProperty =
            DependencyProperty.Register(nameof(WatermarkFontStyle), typeof(FontStyle), typeof(WatermarkBehavior),
                new PropertyMetadata(FontStyles.Italic, OnWatermarkStyleChanged));

        /// <summary>
        /// 水印字体粗细
        /// </summary>
        public static readonly DependencyProperty WatermarkFontWeightProperty =
            DependencyProperty.Register(nameof(WatermarkFontWeight), typeof(FontWeight), typeof(WatermarkBehavior),
                new PropertyMetadata(FontWeights.Normal, OnWatermarkStyleChanged));

        /// <summary>
        /// 水印前景色
        /// </summary>
        public static readonly DependencyProperty WatermarkForegroundProperty =
            DependencyProperty.Register(nameof(WatermarkForeground), typeof(Brush), typeof(WatermarkBehavior),
                new PropertyMetadata(Brushes.Gray, OnWatermarkStyleChanged));

        /// <summary>
        /// 水印透明度
        /// </summary>
        public static readonly DependencyProperty WatermarkOpacityProperty =
            DependencyProperty.Register(nameof(WatermarkOpacity), typeof(double), typeof(WatermarkBehavior),
                new PropertyMetadata(0.6, OnWatermarkStyleChanged));

        /// <summary>
        /// 水印水平对齐方式
        /// </summary>
        public static readonly DependencyProperty WatermarkHorizontalAlignmentProperty =
            DependencyProperty.Register(nameof(WatermarkHorizontalAlignment), typeof(HorizontalAlignment), typeof(WatermarkBehavior),
                new PropertyMetadata(HorizontalAlignment.Left, OnWatermarkStyleChanged));

        /// <summary>
        /// 水印垂直对齐方式
        /// </summary>
        public static readonly DependencyProperty WatermarkVerticalAlignmentProperty =
            DependencyProperty.Register(nameof(WatermarkVerticalAlignment), typeof(VerticalAlignment), typeof(WatermarkBehavior),
                new PropertyMetadata(VerticalAlignment.Center, OnWatermarkStyleChanged));

        /// <summary>
        /// 水印边距
        /// </summary>
        public static readonly DependencyProperty WatermarkMarginProperty =
            DependencyProperty.Register(nameof(WatermarkMargin), typeof(Thickness), typeof(WatermarkBehavior),
                new PropertyMetadata(new Thickness(5, 0, 0, 0), OnWatermarkStyleChanged));

        /// <summary>
        /// 是否在获得焦点时隐藏水印
        /// </summary>
        public static readonly DependencyProperty HideOnFocusProperty =
            DependencyProperty.Register(nameof(HideOnFocus), typeof(bool), typeof(WatermarkBehavior),
                new PropertyMetadata(false));

        #endregion

        #region 属性

        /// <summary>
        /// 获取或设置水印文本
        /// </summary>
        public string WatermarkText
        {
            get => (string)GetValue(WatermarkTextProperty);
            set => SetValue(WatermarkTextProperty, value);
        }

        /// <summary>
        /// 获取或设置水印字体大小
        /// </summary>
        public double WatermarkFontSize
        {
            get => (double)GetValue(WatermarkFontSizeProperty);
            set => SetValue(WatermarkFontSizeProperty, value);
        }

        /// <summary>
        /// 获取或设置水印字体样式
        /// </summary>
        public FontStyle WatermarkFontStyle
        {
            get => (FontStyle)GetValue(WatermarkFontStyleProperty);
            set => SetValue(WatermarkFontStyleProperty, value);
        }

        /// <summary>
        /// 获取或设置水印字体粗细
        /// </summary>
        public FontWeight WatermarkFontWeight
        {
            get => (FontWeight)GetValue(WatermarkFontWeightProperty);
            set => SetValue(WatermarkFontWeightProperty, value);
        }

        /// <summary>
        /// 获取或设置水印前景色
        /// </summary>
        public Brush WatermarkForeground
        {
            get => (Brush)GetValue(WatermarkForegroundProperty);
            set => SetValue(WatermarkForegroundProperty, value);
        }

        /// <summary>
        /// 获取或设置水印透明度
        /// </summary>
        public double WatermarkOpacity
        {
            get => (double)GetValue(WatermarkOpacityProperty);
            set => SetValue(WatermarkOpacityProperty, value);
        }

        /// <summary>
        /// 获取或设置水印水平对齐方式
        /// </summary>
        public HorizontalAlignment WatermarkHorizontalAlignment
        {
            get => (HorizontalAlignment)GetValue(WatermarkHorizontalAlignmentProperty);
            set => SetValue(WatermarkHorizontalAlignmentProperty, value);
        }

        /// <summary>
        /// 获取或设置水印垂直对齐方式
        /// </summary>
        public VerticalAlignment WatermarkVerticalAlignment
        {
            get => (VerticalAlignment)GetValue(WatermarkVerticalAlignmentProperty);
            set => SetValue(WatermarkVerticalAlignmentProperty, value);
        }

        /// <summary>
        /// 获取或设置水印边距
        /// </summary>
        public Thickness WatermarkMargin
        {
            get => (Thickness)GetValue(WatermarkMarginProperty);
            set => SetValue(WatermarkMarginProperty, value);
        }

        /// <summary>
        /// 获取或设置是否在获得焦点时隐藏水印
        /// </summary>
        public bool HideOnFocus
        {
            get => (bool)GetValue(HideOnFocusProperty);
            set => SetValue(HideOnFocusProperty, value);
        }

        #endregion

        #region 私有字段

        private AdornerLayer _adornerLayer;
        private WatermarkAdorner _watermarkAdorner;

        #endregion

        #region 重写方法

        /// <summary>
        /// 附加到关联对象时调用
        /// </summary>
        protected override void OnAttached()
        {
            base.OnAttached();
            
            if (AssociatedObject != null)
            {
                AssociatedObject.Loaded += OnLoaded;
                AssociatedObject.GotFocus += OnGotFocus;
                AssociatedObject.LostFocus += OnLostFocus;
                
                if (AssociatedObject is TextBox textBox)
                {
                    textBox.TextChanged += OnTextChanged;
                }
                else if (AssociatedObject is PasswordBox passwordBox)
                {
                    passwordBox.PasswordChanged += OnPasswordChanged;
                }
            }
        }

        /// <summary>
        /// 从关联对象分离时调用
        /// </summary>
        protected override void OnDetaching()
        {
            if (AssociatedObject != null)
            {
                AssociatedObject.Loaded -= OnLoaded;
                AssociatedObject.GotFocus -= OnGotFocus;
                AssociatedObject.LostFocus -= OnLostFocus;
                
                if (AssociatedObject is TextBox textBox)
                {
                    textBox.TextChanged -= OnTextChanged;
                }
                else if (AssociatedObject is PasswordBox passwordBox)
                {
                    passwordBox.PasswordChanged -= OnPasswordChanged;
                }
            }
            
            RemoveWatermark();
            
            base.OnDetaching();
        }

        #endregion

        #region 事件处理

        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            UpdateWatermarkVisibility();
        }

        private void OnGotFocus(object sender, RoutedEventArgs e)
        {
            if (HideOnFocus)
            {
                RemoveWatermark();
            }
        }

        private void OnLostFocus(object sender, RoutedEventArgs e)
        {
            UpdateWatermarkVisibility();
        }

        private void OnTextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateWatermarkVisibility();
        }

        private void OnPasswordChanged(object sender, RoutedEventArgs e)
        {
            UpdateWatermarkVisibility();
        }

        #endregion

        #region 私有方法

        private void UpdateWatermarkVisibility()
        {
            if (AssociatedObject == null || string.IsNullOrEmpty(WatermarkText))
            {
                RemoveWatermark();
                return;
            }

            var hasText = GetHasText();
            var hasFocus = AssociatedObject.IsFocused && HideOnFocus;

            if (!hasText && !hasFocus)
            {
                ShowWatermark();
            }
            else
            {
                RemoveWatermark();
            }
        }

        private bool GetHasText()
        {
            return AssociatedObject switch
            {
                TextBox textBox => !string.IsNullOrEmpty(textBox.Text),
                PasswordBox passwordBox => !string.IsNullOrEmpty(passwordBox.Password),
                _ => false
            };
        }

        private void ShowWatermark()
        {
            if (_watermarkAdorner != null)
                return;

            _adornerLayer = AdornerLayer.GetAdornerLayer(AssociatedObject);
            if (_adornerLayer != null)
            {
                _watermarkAdorner = new WatermarkAdorner(AssociatedObject, this);
                _adornerLayer.Add(_watermarkAdorner);
            }
        }

        private void RemoveWatermark()
        {
            if (_watermarkAdorner != null && _adornerLayer != null)
            {
                _adornerLayer.Remove(_watermarkAdorner);
                _watermarkAdorner = null;
            }
        }

        private void UpdateWatermarkStyle()
        {
            _watermarkAdorner?.InvalidateVisual();
        }

        #endregion

        #region 依赖属性回调

        private static void OnWatermarkTextChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is WatermarkBehavior behavior)
            {
                behavior.UpdateWatermarkVisibility();
            }
        }

        private static void OnWatermarkStyleChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is WatermarkBehavior behavior)
            {
                behavior.UpdateWatermarkStyle();
            }
        }

        #endregion
    }

    /// <summary>
    /// 水印装饰器
    /// </summary>
    internal class WatermarkAdorner : Adorner
    {
        private readonly WatermarkBehavior _behavior;
        private readonly Control _control;

        public WatermarkAdorner(UIElement adornedElement, WatermarkBehavior behavior) : base(adornedElement)
        {
            _behavior = behavior ?? throw new ArgumentNullException(nameof(behavior));
            _control = adornedElement as Control;
            IsHitTestVisible = false;
        }

        protected override void OnRender(DrawingContext drawingContext)
        {
            if (string.IsNullOrEmpty(_behavior.WatermarkText))
                return;

            var fontFamily = _control?.FontFamily ?? SystemFonts.MessageFontFamily;

            var formattedText = new FormattedText(
                _behavior.WatermarkText,
                System.Globalization.CultureInfo.CurrentCulture,
                FlowDirection.LeftToRight,
                new Typeface(fontFamily, _behavior.WatermarkFontStyle,
                           _behavior.WatermarkFontWeight, FontStretches.Normal),
                _behavior.WatermarkFontSize,
                _behavior.WatermarkForeground,
                VisualTreeHelper.GetDpi(this).PixelsPerDip);

            var renderSize = AdornedElement.RenderSize;
            var margin = _behavior.WatermarkMargin;

            var x = _behavior.WatermarkHorizontalAlignment switch
            {
                HorizontalAlignment.Left => margin.Left,
                HorizontalAlignment.Center => (renderSize.Width - formattedText.Width) / 2,
                HorizontalAlignment.Right => renderSize.Width - formattedText.Width - margin.Right,
                _ => margin.Left
            };

            var y = _behavior.WatermarkVerticalAlignment switch
            {
                VerticalAlignment.Top => margin.Top,
                VerticalAlignment.Center => (renderSize.Height - formattedText.Height) / 2,
                VerticalAlignment.Bottom => renderSize.Height - formattedText.Height - margin.Bottom,
                _ => (renderSize.Height - formattedText.Height) / 2
            };

            var opacity = _behavior.WatermarkOpacity;
            if (opacity < 1.0)
            {
                drawingContext.PushOpacity(opacity);
            }

            drawingContext.DrawText(formattedText, new Point(x, y));

            if (opacity < 1.0)
            {
                drawingContext.Pop();
            }
        }
    }
}
