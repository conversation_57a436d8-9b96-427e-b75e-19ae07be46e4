using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace FluentSystemDesign.WPF.Converters
{
    /// <summary>
    /// 反向布尔值转换器
    /// 将布尔值进行反转：true -> false, false -> true
    /// </summary>
    /// <remarks>
    /// 这个转换器常用于需要反向逻辑的场景，比如：
    /// - 当某个条件为true时隐藏控件
    /// - 当某个条件为false时启用控件
    /// </remarks>
    /// <example>
    /// 在XAML中使用：
    /// <code>
    /// &lt;Button IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBoolConverter}}"/&gt;
    /// &lt;TextBlock Visibility="{Binding IsEmpty, Converter={StaticResource InverseBoolConverter}}"/&gt;
    /// </code>
    /// </example>
    [ValueConversion(typeof(bool), typeof(bool))]
    public class InverseBoolConverter : IValueConverter
    {
        /// <summary>
        /// 将布尔值进行反转
        /// </summary>
        /// <param name="value">布尔值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数（未使用）</param>
        /// <param name="culture">文化信息</param>
        /// <returns>反转后的布尔值</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }

            // 如果值不是布尔类型，尝试转换
            if (value != null)
            {
                if (bool.TryParse(value.ToString(), out bool parsedValue))
                {
                    return !parsedValue;
                }
            }

            return DependencyProperty.UnsetValue;
        }

        /// <summary>
        /// 将反转后的布尔值转换回原始值
        /// </summary>
        /// <param name="value">反转后的布尔值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数（未使用）</param>
        /// <param name="culture">文化信息</param>
        /// <returns>原始布尔值</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // ConvertBack的逻辑与Convert相同，因为反转操作是对称的
            return Convert(value, targetType, parameter, culture);
        }
    }
}
