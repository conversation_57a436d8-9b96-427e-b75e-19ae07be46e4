<Window x:Class="FluentSystemDesign.Examples.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="FluentSystemDesign Examples"
        Height="800"
        Width="1200"
        WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <!-- 导航按钮样式 -->
        <Style x:Key="NavigationButtonStyle" TargetType="Button">
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="Margin" Value="5,2"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="5"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E3F2FD"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#BBDEFB"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 顶部菜单栏 -->
        <Border Grid.Row="0" Background="#F5F5F5" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
            <Grid Height="50">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Logo和标题 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <TextBlock Text="🎨" FontSize="24" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="FluentSystemDesign" FontSize="18" FontWeight="Bold" VerticalAlignment="Center"/>
                </StackPanel>

                <!-- 搜索框 -->
                <TextBox Grid.Column="1"
                         x:Name="SearchBox"
                         Width="300"
                         Height="32"
                         VerticalAlignment="Center"
                         HorizontalAlignment="Center"
                         TextChanged="SearchBox_TextChanged" />

                <!-- 右侧按钮 -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <Button Content="🌙" Width="40" Height="32" Margin="5,0" Click="ThemeToggle_Click" ToolTip="切换主题"/>
                    <Button Content="⚙️" Width="40" Height="32" Margin="5,0" Click="Settings_Click" ToolTip="设置"/>
                    <Button Content="❓" Width="40" Height="32" Margin="5,0" Click="About_Click" ToolTip="关于"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧导航 -->
            <Border Grid.Column="0" Background="#FAFAFA" BorderBrush="#E0E0E0" BorderThickness="0,0,1,0">
                <ScrollViewer>
                    <StackPanel Margin="10">
                        <!-- 首页 -->
                        <Button x:Name="HomeButton"
                                Content="🏠 首页"
                                Style="{StaticResource NavigationButtonStyle}"
                                Click="NavigateToHome_Click"/>

                        <!-- 控件分类 -->
                        <TextBlock Text="控件示例" FontWeight="Bold" Margin="10,20,10,10" FontSize="14"/>

                        <Button x:Name="BehaviorsButton"
                                Content="📋 行为类 (Behaviors)"
                                Style="{StaticResource NavigationButtonStyle}"
                                Click="NavigateToBehaviors_Click"/>

                        <Button x:Name="ValueConvertersButton"
                                Content="🔄 值转换器"
                                Style="{StaticResource NavigationButtonStyle}"
                                Click="NavigateToValueConverters_Click"/>

                        <Button x:Name="BasicControlsButton"
                                Content="🔘 基础控件"
                                Style="{StaticResource NavigationButtonStyle}"
                                Click="NavigateToBasicControls_Click"/>

                        <Button x:Name="InputControlsButton"
                                Content="📝 输入控件"
                                Style="{StaticResource NavigationButtonStyle}"
                                Click="NavigateToInputControls_Click"/>

                        <Button x:Name="NavigationControlsButton"
                                Content="🧭 导航控件"
                                Style="{StaticResource NavigationButtonStyle}"
                                Click="NavigateToNavigationControls_Click"/>

                        <Button x:Name="LayoutControlsButton"
                                Content="📐 布局控件"
                                Style="{StaticResource NavigationButtonStyle}"
                                Click="NavigateToLayoutControls_Click"/>

                        <!-- 主题和样式 -->
                        <TextBlock Text="主题和样式" FontWeight="Bold" Margin="10,20,10,10" FontSize="14"/>

                        <Button x:Name="ColorsButton"
                                Content="🎨 色彩系统"
                                Style="{StaticResource NavigationButtonStyle}"
                                Click="NavigateToColors_Click"/>

                        <Button x:Name="TypographyButton"
                                Content="📖 字体排版"
                                Style="{StaticResource NavigationButtonStyle}"
                                Click="NavigateToTypography_Click"/>

                        <Button x:Name="EffectsButton"
                                Content="✨ 视觉效果"
                                Style="{StaticResource NavigationButtonStyle}"
                                Click="NavigateToEffects_Click"/>

                        <Button x:Name="GeometriesButton"
                                Content="📐 几何形状"
                                Style="{StaticResource NavigationButtonStyle}"
                                Click="NavigateToGeometries_Click"/>

                        <!-- 文档和资源 -->
                        <TextBlock Text="文档和资源" FontWeight="Bold" Margin="10,20,10,10" FontSize="14"/>

                        <Button x:Name="GettingStartedButton"
                                Content="🚀 快速开始"
                                Style="{StaticResource NavigationButtonStyle}"
                                Click="NavigateToGettingStarted_Click"/>

                        <Button x:Name="DocumentationButton"
                                Content="📚 完整文档"
                                Style="{StaticResource NavigationButtonStyle}"
                                Click="NavigateToDocumentation_Click"/>
                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!-- 右侧内容展示区域 -->
            <Border Grid.Column="1" Background="White">
                <Frame x:Name="ContentFrame" NavigationUIVisibility="Hidden"/>
            </Border>
        </Grid>

        <!-- 底部状态栏 -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
            <Grid Height="30">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                           x:Name="StatusText"
                           Text="就绪"
                           VerticalAlignment="Center"
                           Margin="20,0"/>

                <TextBlock Grid.Column="1"
                           Text="版本 1.0.0"
                           VerticalAlignment="Center"
                           Margin="20,0"
                           FontSize="12"
                           Foreground="Gray"/>
            </Grid>
        </Border>
    </Grid>
</Window>
