{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"FluentSystemDesign.Examples/1.0.0": {"dependencies": {"FluentSystemDesign.WPF": "1.0.0"}, "runtime": {"FluentSystemDesign.Examples.dll": {}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.77": {"runtime": {"lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.77.27830"}}}, "FluentSystemDesign.WPF/1.0.0": {"dependencies": {"Microsoft.Xaml.Behaviors.Wpf": "1.1.77"}, "runtime": {"FluentSystemDesign.WPF.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"FluentSystemDesign.Examples/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Xaml.Behaviors.Wpf/1.1.77": {"type": "package", "serviceable": true, "sha512": "sha512-MCu674ZETgU18EbxfwIlRpUPJ02YbZenLsMCXTkpeA7KUBpXfFaOUDlEO+7UWu5AFnUoydg+aQENJkuaZPheMQ==", "path": "microsoft.xaml.behaviors.wpf/1.1.77", "hashPath": "microsoft.xaml.behaviors.wpf.1.1.77.nupkg.sha512"}, "FluentSystemDesign.WPF/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}