<Page x:Class="FluentSystemDesign.Examples.Pages.HomePage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="首页">

    <ScrollViewer>
        <StackPanel Margin="40">
            <!-- 欢迎标题 -->
            <StackPanel Margin="0,0,0,40">
                
                <TextBlock Text="欢迎使用 FluentSystemDesign" 
                           FontSize="36" 
                           FontWeight="Bold" 
                           HorizontalAlignment="Center"
                           Margin="0,0,0,10"/>
                
                <TextBlock Text="基于 Fluent Design System 的现代化 WPF 控件库" 
                           FontSize="18" 
                           HorizontalAlignment="Center"
                           Foreground="Gray"/>
            </StackPanel>

            <!-- 特性介绍 -->
            <Grid Margin="0,0,0,40">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 现代化设计 -->
                <Border Grid.Column="0"
                        Background="#F8F9FA"
                        CornerRadius="10"
                        Padding="20"
                        Margin="10">
                    
                    <StackPanel>
                        <TextBlock Text="🎨" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                        <TextBlock Text="现代化设计" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="遵循 Fluent Design System 设计语言，提供现代化、一致性的用户界面体验。" 
                                   TextWrapping="Wrap" 
                                   HorizontalAlignment="Center"
                                   TextAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- 丰富的控件 -->
                <Border Grid.Column="1"
                        Background="#F8F9FA"
                        CornerRadius="10"
                        Padding="20"
                        Margin="10">
                    
                    <StackPanel>
                        <TextBlock Text="🧩" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                        <TextBlock Text="丰富的控件" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="提供基础控件、输入控件、导航控件、布局控件等多种类型的控件组件。" 
                                   TextWrapping="Wrap" 
                                   HorizontalAlignment="Center"
                                   TextAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- 强大的行为 -->
                <Border Grid.Column="2"
                        Background="#F8F9FA"
                        CornerRadius="10"
                        Padding="20"
                        Margin="10">
                    
                    <StackPanel>
                        <TextBlock Text="⚡" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                        <TextBlock Text="强大的行为" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="内置动画、拖拽、验证、焦点管理等多种行为类，增强用户交互体验。" 
                                   TextWrapping="Wrap" 
                                   HorizontalAlignment="Center"
                                   TextAlignment="Center"/>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- 快速开始 -->
            <Border Background="#E3F2FD"
                    CornerRadius="10"
                    Padding="30"
                    Margin="0,0,0,40">
                
                <StackPanel>
                    <TextBlock Text="🚀 快速开始" 
                               FontSize="24" 
                               FontWeight="Bold" 
                               HorizontalAlignment="Center"
                               Margin="0,0,0,20"/>
                    
                    <TextBlock Text="通过左侧导航菜单浏览不同类型的控件和功能示例。" 
                               FontSize="16" 
                               HorizontalAlignment="Center"
                               Margin="0,0,0,20"/>
                    
                    <WrapPanel HorizontalAlignment="Center">
                        <Button Content="📋 查看行为类示例" 
                                Padding="15,10" 
                                Margin="10,5"
                                Background="#2196F3"
                                Foreground="White"
                                BorderThickness="0"
                                Cursor="Hand"
                                Click="ViewBehaviors_Click">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}" 
                                                        CornerRadius="5"
                                                        Padding="{TemplateBinding Padding}">
                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#1976D2"/>
                                                    </Trigger>
                                                    <Trigger Property="IsPressed" Value="True">
                                                        <Setter Property="Background" Value="#1565C0"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                        </Button>
                        
                        <Button Content="🎨 查看色彩系统" 
                                Padding="15,10" 
                                Margin="10,5"
                                Background="#4CAF50"
                                Foreground="White"
                                BorderThickness="0"
                                Cursor="Hand"
                                Click="ViewColors_Click">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}" 
                                                        CornerRadius="5"
                                                        Padding="{TemplateBinding Padding}">
                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#388E3C"/>
                                                    </Trigger>
                                                    <Trigger Property="IsPressed" Value="True">
                                                        <Setter Property="Background" Value="#2E7D32"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                        </Button>
                        
                        <Button Content="✨ 查看视觉效果"
                                Padding="15,10"
                                Margin="10,5"
                                Background="#9C27B0"
                                Foreground="White"
                                BorderThickness="0"
                                Cursor="Hand"
                                Click="ViewEffects_Click">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="5"
                                                        Padding="{TemplateBinding Padding}">
                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#7B1FA2"/>
                                                    </Trigger>
                                                    <Trigger Property="IsPressed" Value="True">
                                                        <Setter Property="Background" Value="#6A1B9A"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                        </Button>

                        <Button Content="📚 查看文档"
                                Padding="15,10"
                                Margin="10,5"
                                Background="#FF9800"
                                Foreground="White"
                                BorderThickness="0"
                                Cursor="Hand"
                                Click="ViewDocumentation_Click">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}" 
                                                        CornerRadius="5"
                                                        Padding="{TemplateBinding Padding}">
                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#F57C00"/>
                                                    </Trigger>
                                                    <Trigger Property="IsPressed" Value="True">
                                                        <Setter Property="Background" Value="#EF6C00"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                        </Button>
                    </WrapPanel>
                </StackPanel>
            </Border>

            <!-- 最新更新 -->
            <Border Background="#FFF3E0" 
                    CornerRadius="10" 
                    Padding="20" 
                    Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="📢 最新更新" 
                               FontSize="20" 
                               FontWeight="Bold" 
                               Margin="0,0,0,15"/>
                    
                    <StackPanel>
                        <TextBlock Text="• 新增行为类系统，包括动画、拖拽、验证、焦点管理等7种行为类" Margin="0,5"/>
                        <TextBlock Text="• 完善示例展示应用，提供完整的导航和演示功能" Margin="0,5"/>
                        <TextBlock Text="• 优化控件样式和主题系统，支持深色/浅色主题切换" Margin="0,5"/>
                        <TextBlock Text="• 增加完整的文档和使用指南" Margin="0,5"/>
                    </StackPanel>
                </StackPanel>
            </Border>
        </StackPanel>
    </ScrollViewer>
</Page>
