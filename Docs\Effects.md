# FluentSystemDesign WPF 视觉效果系统

FluentSystemDesign WPF控件库提供了一套完整的视觉效果系统，符合Fluent Design System设计规范，包括阴影、模糊、发光和渐变效果。

## 目录

- [阴影效果系统](#阴影效果系统)
- [模糊效果](#模糊效果)
- [发光效果](#发光效果)
- [渐变效果](#渐变效果)
- [效果辅助类](#效果辅助类)
- [附加属性](#附加属性)
- [组合效果样式](#组合效果样式)
- [最佳实践](#最佳实践)

## 阴影效果系统

### Elevation 高度系统

基于Material Design的Elevation概念，提供0-24级的阴影效果：

```xml
<!-- 基础阴影效果 -->
<Border Effect="{StaticResource Elevation1}">  <!-- 轻微阴影 -->
<Border Effect="{StaticResource Elevation4}">  <!-- 标准阴影 -->
<Border Effect="{StaticResource Elevation8}">  <!-- 明显阴影 -->
<Border Effect="{StaticResource Elevation12}"> <!-- 对话框阴影 -->
<Border Effect="{StaticResource Elevation24}"> <!-- 最深阴影 -->
```

### 深色主题阴影

深色主题下使用更强的阴影效果：

```xml
<!-- 深色主题阴影 -->
<Border Effect="{StaticResource DarkElevation4}">
<Border Effect="{StaticResource DarkElevation8}">
```

### 专用阴影效果

针对特定控件类型的阴影：

```xml
<!-- 卡片阴影 -->
<Border Effect="{StaticResource CardShadow}">

<!-- 按钮阴影 -->
<Button Effect="{StaticResource ButtonShadow}">

<!-- 菜单阴影 -->
<ContextMenu Effect="{StaticResource MenuShadow}">

<!-- 对话框阴影 -->
<Window Effect="{StaticResource DialogShadow}">
```

## 模糊效果

### 基础模糊效果

```xml
<!-- 不同强度的模糊效果 -->
<Border Effect="{StaticResource SubtleBlur}">   <!-- 轻微模糊 -->
<Border Effect="{StaticResource LightBlur}">    <!-- 轻度模糊 -->
<Border Effect="{StaticResource MediumBlur}">   <!-- 中度模糊 -->
<Border Effect="{StaticResource StrongBlur}">   <!-- 强度模糊 -->
<Border Effect="{StaticResource HeavyBlur}">    <!-- 重度模糊 -->
```

### 亚克力模糊效果

用于Fluent Design的亚克力材质：

```xml
<!-- 亚克力模糊效果 -->
<Border Effect="{StaticResource AcrylicLight}">
<Border Effect="{StaticResource AcrylicMedium}">
<Border Effect="{StaticResource AcrylicStrong}">
```

### 特殊用途模糊

```xml
<!-- 焦点背景模糊 -->
<Border Effect="{StaticResource FocusBackground}">

<!-- 模态对话框背景模糊 -->
<Border Effect="{StaticResource ModalBackground}">
```

## 发光效果

### 基础发光效果

```xml
<!-- 不同强度的发光 -->
<Border Effect="{StaticResource SubtleGlow}">
<Border Effect="{StaticResource LightGlow}">
<Border Effect="{StaticResource MediumGlow}">
<Border Effect="{StaticResource StrongGlow}">
```

### 主题色发光

```xml
<!-- 主题色发光效果 -->
<Border Effect="{StaticResource PrimaryGlow}">
<Border Effect="{StaticResource SecondaryGlow}">
<Border Effect="{StaticResource AccentGlow}">
```

### 语义发光效果

```xml
<!-- 语义状态发光 -->
<Border Effect="{StaticResource SuccessGlow}">
<Border Effect="{StaticResource WarningGlow}">
<Border Effect="{StaticResource ErrorGlow}">
<Border Effect="{StaticResource InfoGlow}">
```

### 交互发光效果

```xml
<!-- 交互状态发光 -->
<Border Effect="{StaticResource FocusGlow}">
<Border Effect="{StaticResource HoverGlow}">
<Border Effect="{StaticResource ActiveGlow}">
<Border Effect="{StaticResource SelectionGlow}">
```

## 渐变效果

### 主题渐变

```xml
<!-- 主题色渐变 -->
<Border Background="{StaticResource PrimaryLinearGradient}">
<Border Background="{StaticResource SecondaryLinearGradient}">
<Border Background="{StaticResource AccentLinearGradient}">
```

### 径向渐变

```xml
<!-- 径向渐变效果 -->
<Border Background="{StaticResource PrimaryRadialGradient}">
<Border Background="{StaticResource SecondaryRadialGradient}">
```

### 语义渐变

```xml
<!-- 语义状态渐变 -->
<Border Background="{StaticResource SuccessGradient}">
<Border Background="{StaticResource WarningGradient}">
<Border Background="{StaticResource ErrorGradient}">
<Border Background="{StaticResource InfoGradient}">
```

### 特殊效果渐变

```xml
<!-- 玻璃效果渐变 -->
<Border Background="{StaticResource GlassGradient}">

<!-- 闪光效果渐变 -->
<Border Background="{StaticResource ShimmerGradient}">

<!-- 覆盖层渐变 -->
<Border Background="{StaticResource OverlayGradient}">
```

## 效果辅助类

### EffectHelper 静态方法

```csharp
using FluentSystemDesign.WPF.Effects.Helpers;

// 创建高度阴影
var shadow = EffectHelper.CreateElevationShadow(4, isDarkTheme: false);

// 创建自定义阴影
var customShadow = EffectHelper.CreateCustomShadow(
    depth: 2, 
    blurRadius: 8, 
    opacity: 0.3, 
    color: Colors.Blue);

// 创建发光效果
var glow = EffectHelper.CreateGlowEffect(Colors.Blue, intensity: 0.5, radius: 10);

// 创建主题发光
var themeGlow = EffectHelper.CreateThemeGlow(ThemeColorType.Primary);

// 创建模糊效果
var blur = EffectHelper.CreateBlurEffect(BlurIntensity.Medium);

// 创建线性渐变
var gradient = EffectHelper.CreateLinearGradient(Colors.Blue, Colors.Purple, angle: 45);
```

## 附加属性

### Elevation 附加属性

```xml
<!-- 使用附加属性设置高度 -->
<Border effects:EffectAttachedProperties.Elevation="4">
<Border effects:EffectAttachedProperties.Elevation="8" 
        effects:EffectAttachedProperties.IsDarkTheme="True">
```

### 发光附加属性

```xml
<!-- 使用附加属性设置发光 -->
<Border effects:EffectAttachedProperties.GlowColor="Blue"
        effects:EffectAttachedProperties.GlowIntensity="0.5"
        effects:EffectAttachedProperties.GlowRadius="10">
```

### 模糊附加属性

```xml
<!-- 使用附加属性设置模糊 -->
<Border effects:EffectAttachedProperties.BlurRadius="8">
```

## 组合效果样式

### 预定义组合样式

```xml
<!-- 卡片效果样式 -->
<Border Style="{StaticResource CardEffectStyle}">

<!-- 按钮效果样式 -->
<Button Style="{StaticResource ButtonEffectStyle}">

<!-- 对话框效果样式 -->
<Window Style="{StaticResource DialogEffectStyle}">

<!-- 浮动按钮效果样式 -->
<Button Style="{StaticResource FloatingButtonEffectStyle}">
```

### 交互状态样式

```xml
<!-- 焦点效果样式 -->
<TextBox Style="{StaticResource FocusEffectStyle}">

<!-- 悬停效果样式 -->
<Button Style="{StaticResource HoverEffectStyle}">

<!-- 选中效果样式 -->
<ListBoxItem Style="{StaticResource SelectionEffectStyle}">
```

### 语义状态样式

```xml
<!-- 成功状态效果 -->
<Border Style="{StaticResource SuccessEffectStyle}">

<!-- 警告状态效果 -->
<Border Style="{StaticResource WarningEffectStyle}">

<!-- 错误状态效果 -->
<Border Style="{StaticResource ErrorEffectStyle}">
```

## 最佳实践

### 1. 阴影使用指南

- **Elevation 0-2**: 用于平面元素和轻微突出
- **Elevation 3-4**: 用于按钮和卡片
- **Elevation 6-8**: 用于浮动元素和导航
- **Elevation 12-16**: 用于对话框和模态窗口
- **Elevation 24**: 用于最高层级的元素

### 2. 性能优化

```xml
<!-- 避免过度使用效果 -->
<!-- 好的做法 -->
<Border Effect="{StaticResource Elevation2}">

<!-- 避免的做法 -->
<Border>
    <Border.Effect>
        <DropShadowEffect BlurRadius="50" ShadowDepth="20"/>
    </Border.Effect>
</Border>
```

### 3. 主题适配

```xml
<!-- 使用主题感知的效果 -->
<Border Effect="{StaticResource ThemeElevation4}">
```

### 4. 动画过渡

```xml
<!-- 为效果变化添加动画 -->
<Style.Triggers>
    <Trigger Property="IsMouseOver" Value="True">
        <Trigger.EnterActions>
            <BeginStoryboard>
                <Storyboard>
                    <ObjectAnimationUsingKeyFrames Storyboard.TargetProperty="Effect">
                        <DiscreteObjectKeyFrame KeyTime="0:0:0.2" 
                                              Value="{StaticResource ButtonHoverShadow}"/>
                    </ObjectAnimationUsingKeyFrames>
                </Storyboard>
            </BeginStoryboard>
        </Trigger.EnterActions>
    </Trigger>
</Style.Triggers>
```

### 5. 可访问性考虑

- 确保效果不影响文本可读性
- 提供禁用效果的选项
- 考虑高对比度模式下的效果显示

### 6. DPI 适配

所有效果都会自动适配不同的DPI设置，无需额外配置。

## 相关文档

- [色彩系统文档](ColorSystem.md)
- [主题系统文档](README_ColorSystem.md)
- [控件库入门指南](GettingStarted.md)

---

*更多信息请参考 [FluentSystemDesign WPF 控件库文档](../README.md)*
