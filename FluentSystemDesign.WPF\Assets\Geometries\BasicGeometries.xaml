<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 
    基础几何形状资源字典
    包含圆形、矩形、三角形、多边形等基本几何形状
    所有几何形状都是矢量格式，支持无损缩放
    -->

    <!-- 圆形几何形状 -->
    <EllipseGeometry x:Key="CircleGeometry" 
                     Center="12,12" 
                     RadiusX="12" 
                     RadiusY="12" />

    <!-- 小圆形 -->
    <EllipseGeometry x:Key="SmallCircleGeometry" 
                     Center="8,8" 
                     RadiusX="8" 
                     RadiusY="8" />

    <!-- 大圆形 -->
    <EllipseGeometry x:Key="LargeCircleGeometry" 
                     Center="16,16" 
                     RadiusX="16" 
                     RadiusY="16" />

    <!-- 矩形几何形状 -->
    <RectangleGeometry x:Key="RectangleGeometry" 
                       Rect="0,0,24,24" />

    <!-- 圆角矩形 -->
    <RectangleGeometry x:Key="RoundedRectangleGeometry" 
                       Rect="0,0,24,24" 
                       RadiusX="4" 
                       RadiusY="4" />

    <!-- 小圆角矩形 -->
    <RectangleGeometry x:Key="SmallRoundedRectangleGeometry" 
                       Rect="0,0,16,16" 
                       RadiusX="2" 
                       RadiusY="2" />

    <!-- 大圆角矩形 -->
    <RectangleGeometry x:Key="LargeRoundedRectangleGeometry" 
                       Rect="0,0,32,32" 
                       RadiusX="6" 
                       RadiusY="6" />

    <!-- 正方形 -->
    <RectangleGeometry x:Key="SquareGeometry" 
                       Rect="0,0,24,24" />

    <!-- 三角形几何形状 -->
    
    <!-- 向上三角形 -->
    <PathGeometry x:Key="TriangleUpGeometry">
        <PathFigure StartPoint="12,4" IsClosed="True">
            <LineSegment Point="20,20" />
            <LineSegment Point="4,20" />
        </PathFigure>
    </PathGeometry>

    <!-- 向下三角形 -->
    <PathGeometry x:Key="TriangleDownGeometry">
        <PathFigure StartPoint="12,20" IsClosed="True">
            <LineSegment Point="4,4" />
            <LineSegment Point="20,4" />
        </PathFigure>
    </PathGeometry>

    <!-- 向左三角形 -->
    <PathGeometry x:Key="TriangleLeftGeometry">
        <PathFigure StartPoint="4,12" IsClosed="True">
            <LineSegment Point="20,4" />
            <LineSegment Point="20,20" />
        </PathFigure>
    </PathGeometry>

    <!-- 向右三角形 -->
    <PathGeometry x:Key="TriangleRightGeometry">
        <PathFigure StartPoint="20,12" IsClosed="True">
            <LineSegment Point="4,20" />
            <LineSegment Point="4,4" />
        </PathFigure>
    </PathGeometry>

    <!-- 等边三角形 -->
    <PathGeometry x:Key="EquilateralTriangleGeometry">
        <PathFigure StartPoint="12,2" IsClosed="True">
            <LineSegment Point="22,18" />
            <LineSegment Point="2,18" />
        </PathFigure>
    </PathGeometry>

    <!-- 多边形几何形状 -->
    
    <!-- 五边形 -->
    <PathGeometry x:Key="PentagonGeometry">
        <PathFigure StartPoint="12,2" IsClosed="True">
            <LineSegment Point="22,8" />
            <LineSegment Point="18,20" />
            <LineSegment Point="6,20" />
            <LineSegment Point="2,8" />
        </PathFigure>
    </PathGeometry>

    <!-- 六边形 -->
    <PathGeometry x:Key="HexagonGeometry">
        <PathFigure StartPoint="12,2" IsClosed="True">
            <LineSegment Point="20,6" />
            <LineSegment Point="20,18" />
            <LineSegment Point="12,22" />
            <LineSegment Point="4,18" />
            <LineSegment Point="4,6" />
        </PathFigure>
    </PathGeometry>

    <!-- 八边形 -->
    <PathGeometry x:Key="OctagonGeometry">
        <PathFigure StartPoint="8,2" IsClosed="True">
            <LineSegment Point="16,2" />
            <LineSegment Point="22,8" />
            <LineSegment Point="22,16" />
            <LineSegment Point="16,22" />
            <LineSegment Point="8,22" />
            <LineSegment Point="2,16" />
            <LineSegment Point="2,8" />
        </PathFigure>
    </PathGeometry>

    <!-- 星形 -->
    <PathGeometry x:Key="StarGeometry">
        <PathFigure StartPoint="12,2" IsClosed="True">
            <LineSegment Point="14.4,8.8" />
            <LineSegment Point="22,8.8" />
            <LineSegment Point="16.8,13.6" />
            <LineSegment Point="19.2,21.2" />
            <LineSegment Point="12,17.6" />
            <LineSegment Point="4.8,21.2" />
            <LineSegment Point="7.2,13.6" />
            <LineSegment Point="2,8.8" />
            <LineSegment Point="9.6,8.8" />
        </PathFigure>
    </PathGeometry>

    <!-- 五角星 -->
    <PathGeometry x:Key="FivePointStarGeometry">
        <PathFigure StartPoint="12,1" IsClosed="True">
            <LineSegment Point="15.09,8.09" />
            <LineSegment Point="23,8.09" />
            <LineSegment Point="16.95,13.18" />
            <LineSegment Point="19.09,21.27" />
            <LineSegment Point="12,16.18" />
            <LineSegment Point="4.91,21.27" />
            <LineSegment Point="7.05,13.18" />
            <LineSegment Point="1,8.09" />
            <LineSegment Point="8.91,8.09" />
        </PathFigure>
    </PathGeometry>

    <!-- 菱形 -->
    <PathGeometry x:Key="DiamondGeometry">
        <PathFigure StartPoint="12,2" IsClosed="True">
            <LineSegment Point="22,12" />
            <LineSegment Point="12,22" />
            <LineSegment Point="2,12" />
        </PathFigure>
    </PathGeometry>

    <!-- 心形 -->
    <PathGeometry x:Key="HeartGeometry">
        <PathFigure StartPoint="12,21.35">
            <BezierSegment Point1="12,21.35" Point2="6,14" Point3="6,10" />
            <BezierSegment Point1="6,5.5" Point2="9.5,2" Point3="12,5.5" />
            <BezierSegment Point1="14.5,2" Point2="18,5.5" Point3="18,10" />
            <BezierSegment Point1="18,14" Point2="12,21.35" Point3="12,21.35" />
        </PathFigure>
    </PathGeometry>

</ResourceDictionary>
