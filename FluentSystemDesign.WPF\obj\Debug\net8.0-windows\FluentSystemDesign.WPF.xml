<?xml version="1.0"?>
<doc>
    <assembly>
        <name>FluentSystemDesign.WPF</name>
    </assembly>
    <members>
        <member name="T:FluentSystemDesign.WPF.Behaviors.AnimationBehavior">
            <summary>
            动画行为，为UI元素提供常用的动画效果
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.AnimationTypeProperty">
            <summary>
            动画类型
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.DurationProperty">
            <summary>
            动画持续时间
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.DelayProperty">
            <summary>
            动画延迟时间
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.EasingFunctionProperty">
            <summary>
            缓动函数
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.AutoStartProperty">
            <summary>
            是否自动开始动画
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.TriggerProperty">
            <summary>
            触发器
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.AnimationType">
            <summary>
            获取或设置动画类型
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.Duration">
            <summary>
            获取或设置动画持续时间
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.Delay">
            <summary>
            获取或设置动画延迟时间
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.EasingFunction">
            <summary>
            获取或设置缓动函数
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.AutoStart">
            <summary>
            获取或设置是否自动开始动画
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.Trigger">
            <summary>
            获取或设置触发器
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.AnimationStarted">
            <summary>
            动画开始事件
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.AnimationCompleted">
            <summary>
            动画完成事件
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.OnAttached">
            <summary>
            附加到关联对象时调用
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.OnDetaching">
            <summary>
            从关联对象分离时调用
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.StartAnimation">
            <summary>
            开始动画
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.StopAnimation">
            <summary>
            停止动画
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.CreateStoryboard">
            <summary>
            创建故事板
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.EnsureTransformGroup">
            <summary>
            确保元素有TransformGroup，如果没有则创建
            </summary>
            <returns>TransformGroup实例</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.GetOrCreateTranslateTransform(System.Windows.Media.TransformGroup)">
            <summary>
            获取或创建TranslateTransform
            </summary>
            <param name="transformGroup">Transform组</param>
            <returns>TranslateTransform实例</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.GetOrCreateScaleTransform(System.Windows.Media.TransformGroup)">
            <summary>
            获取或创建ScaleTransform
            </summary>
            <param name="transformGroup">Transform组</param>
            <returns>ScaleTransform实例</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.AnimationType">
            <summary>
            动画类型枚举
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationType.FadeIn">
            <summary>
            淡入
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationType.FadeOut">
            <summary>
            淡出
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationType.SlideInFromLeft">
            <summary>
            从左侧滑入
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationType.SlideInFromRight">
            <summary>
            从右侧滑入
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationType.SlideInFromTop">
            <summary>
            从顶部滑入
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationType.SlideInFromBottom">
            <summary>
            从底部滑入
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationType.ScaleIn">
            <summary>
            缩放进入
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationType.ScaleOut">
            <summary>
            缩放退出
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.AnimationTrigger">
            <summary>
            动画触发器枚举
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationTrigger.Loaded">
            <summary>
            加载时触发
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationTrigger.MouseEnter">
            <summary>
            鼠标进入时触发
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationTrigger.MouseLeave">
            <summary>
            鼠标离开时触发
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationTrigger.Manual">
            <summary>
            手动触发
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions">
            <summary>
            行为扩展方法，提供便捷的行为附加方式
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.AddDragBehavior(System.Windows.FrameworkElement,System.Object,System.Windows.DragDropEffects)">
            <summary>
            为元素添加拖拽行为
            </summary>
            <param name="element">目标元素</param>
            <param name="dragData">拖拽数据</param>
            <param name="dragEffects">拖拽效果</param>
            <returns>拖拽行为实例</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.AddAnimationBehavior(System.Windows.FrameworkElement,FluentSystemDesign.WPF.Behaviors.AnimationType,FluentSystemDesign.WPF.Behaviors.AnimationTrigger,System.Boolean)">
            <summary>
            为元素添加动画行为
            </summary>
            <param name="element">目标元素</param>
            <param name="animationType">动画类型</param>
            <param name="trigger">触发器</param>
            <param name="autoStart">是否自动开始</param>
            <returns>动画行为实例</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.AddFadeInAnimation(System.Windows.FrameworkElement,System.Boolean)">
            <summary>
            为元素添加淡入动画
            </summary>
            <param name="element">目标元素</param>
            <param name="autoStart">是否自动开始</param>
            <returns>动画行为实例</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.AddSlideInAnimation(System.Windows.FrameworkElement,FluentSystemDesign.WPF.Behaviors.SlideDirection,System.Boolean)">
            <summary>
            为元素添加滑入动画
            </summary>
            <param name="element">目标元素</param>
            <param name="direction">滑入方向</param>
            <param name="autoStart">是否自动开始</param>
            <returns>动画行为实例</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.AddFocusManagementBehavior(System.Windows.FrameworkElement,System.Boolean,System.Boolean)">
            <summary>
            为元素添加焦点管理行为
            </summary>
            <param name="element">目标元素</param>
            <param name="focusOnLoad">是否在加载时获取焦点</param>
            <param name="selectAllOnFocus">是否在获取焦点时选择所有文本</param>
            <returns>焦点管理行为实例</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.AddValidationBehavior(System.Windows.FrameworkElement,FluentSystemDesign.WPF.Behaviors.ValidationType,System.Boolean,System.String)">
            <summary>
            为元素添加验证行为
            </summary>
            <param name="element">目标元素</param>
            <param name="validationType">验证类型</param>
            <param name="isRequired">是否必填</param>
            <param name="errorMessage">错误消息</param>
            <returns>验证行为实例</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.AddEmailValidation(System.Windows.FrameworkElement,System.Boolean)">
            <summary>
            为元素添加邮箱验证
            </summary>
            <param name="element">目标元素</param>
            <param name="isRequired">是否必填</param>
            <returns>验证行为实例</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.AddPhoneValidation(System.Windows.FrameworkElement,System.Boolean)">
            <summary>
            为元素添加手机号验证
            </summary>
            <param name="element">目标元素</param>
            <param name="isRequired">是否必填</param>
            <returns>验证行为实例</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.AddWatermarkBehavior(System.Windows.FrameworkElement,System.String,System.Boolean)">
            <summary>
            为元素添加水印行为
            </summary>
            <param name="element">目标元素</param>
            <param name="watermarkText">水印文本</param>
            <param name="hideOnFocus">是否在获得焦点时隐藏</param>
            <returns>水印行为实例</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.AddWindowBehavior(System.Windows.Window,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            为窗口添加窗口行为
            </summary>
            <param name="window">目标窗口</param>
            <param name="enableDragMove">是否启用拖拽移动</param>
            <param name="enableDoubleClickMaximize">是否启用双击最大化</param>
            <param name="saveWindowPlacement">是否保存窗口位置</param>
            <returns>窗口行为实例</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.RemoveBehavior``1(System.Windows.DependencyObject)">
            <summary>
            移除指定类型的行为
            </summary>
            <typeparam name="T">行为类型</typeparam>
            <param name="element">目标元素</param>
            <returns>是否成功移除</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.GetBehavior``1(System.Windows.DependencyObject)">
            <summary>
            获取指定类型的行为
            </summary>
            <typeparam name="T">行为类型</typeparam>
            <param name="element">目标元素</param>
            <returns>行为实例，如果不存在则返回null</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.ClearBehaviors(System.Windows.DependencyObject)">
            <summary>
            清除所有行为
            </summary>
            <param name="element">目标元素</param>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.SlideDirection">
            <summary>
            滑入方向枚举
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.SlideDirection.Left">
            <summary>
            从左侧滑入
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.SlideDirection.Right">
            <summary>
            从右侧滑入
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.SlideDirection.Top">
            <summary>
            从顶部滑入
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.SlideDirection.Bottom">
            <summary>
            从底部滑入
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.DragBehavior">
            <summary>
            拖拽行为，为UI元素提供拖拽功能
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.DragBehavior.IsEnabledProperty">
            <summary>
            是否启用拖拽
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.DragBehavior.DragDataProperty">
            <summary>
            拖拽数据
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.DragBehavior.DragEffectsProperty">
            <summary>
            拖拽效果
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.DragBehavior.DragThresholdProperty">
            <summary>
            拖拽开始阈值（像素）
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.DragBehavior.IsEnabled">
            <summary>
            获取或设置是否启用拖拽
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.DragBehavior.DragData">
            <summary>
            获取或设置拖拽数据
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.DragBehavior.DragEffects">
            <summary>
            获取或设置拖拽效果
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.DragBehavior.DragThreshold">
            <summary>
            获取或设置拖拽开始阈值
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.DragBehavior.DragStarted">
            <summary>
            拖拽开始事件
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.DragBehavior.DragCompleted">
            <summary>
            拖拽完成事件
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.DragBehavior.OnAttached">
            <summary>
            附加到关联对象时调用
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.DragBehavior.OnDetaching">
            <summary>
            从关联对象分离时调用
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.DragBehavior.OnMouseLeftButtonDown(System.Object,System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            鼠标左键按下事件处理
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.DragBehavior.OnMouseMove(System.Object,System.Windows.Input.MouseEventArgs)">
            <summary>
            鼠标移动事件处理
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.DragBehavior.OnMouseLeftButtonUp(System.Object,System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            鼠标左键释放事件处理
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.DragBehavior.StartDrag">
            <summary>
            开始拖拽操作
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.DragStartedEventArgs">
            <summary>
            拖拽开始事件参数
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.DragStartedEventArgs.Data">
            <summary>
            拖拽数据
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.DragStartedEventArgs.Cancel">
            <summary>
            是否取消拖拽
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.DragStartedEventArgs.#ctor(System.Object)">
            <summary>
            构造函数
            </summary>
            <param name="data">拖拽数据</param>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.DragCompletedEventArgs">
            <summary>
            拖拽完成事件参数
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.DragCompletedEventArgs.Data">
            <summary>
            拖拽数据
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.DragCompletedEventArgs.Result">
            <summary>
            拖拽结果
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.DragCompletedEventArgs.#ctor(System.Object,System.Windows.DragDropEffects)">
            <summary>
            构造函数
            </summary>
            <param name="data">拖拽数据</param>
            <param name="result">拖拽结果</param>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior">
            <summary>
            焦点管理行为，提供高级的焦点管理功能
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.FocusOnLoadProperty">
            <summary>
            是否在加载时自动获取焦点
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.FocusOnMouseEnterProperty">
            <summary>
            是否在鼠标进入时获取焦点
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.SelectAllOnFocusProperty">
            <summary>
            是否选择所有文本（仅适用于TextBox）
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.TabIndexProperty">
            <summary>
            Tab键导航顺序
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.IsTabStopProperty">
            <summary>
            是否参与Tab键导航
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.FocusDelayProperty">
            <summary>
            焦点延迟时间（毫秒）
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.NextFocusElementProperty">
            <summary>
            下一个焦点元素
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.PreviousFocusElementProperty">
            <summary>
            上一个焦点元素
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.FocusOnLoad">
            <summary>
            获取或设置是否在加载时自动获取焦点
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.FocusOnMouseEnter">
            <summary>
            获取或设置是否在鼠标进入时获取焦点
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.SelectAllOnFocus">
            <summary>
            获取或设置是否选择所有文本
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.TabIndex">
            <summary>
            获取或设置Tab键导航顺序
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.IsTabStop">
            <summary>
            获取或设置是否参与Tab键导航
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.FocusDelay">
            <summary>
            获取或设置焦点延迟时间
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.NextFocusElement">
            <summary>
            获取或设置下一个焦点元素
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.PreviousFocusElement">
            <summary>
            获取或设置上一个焦点元素
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.FocusReceived">
            <summary>
            获得焦点事件
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.FocusLost">
            <summary>
            失去焦点事件
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.OnAttached">
            <summary>
            附加到关联对象时调用
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.OnDetaching">
            <summary>
            从关联对象分离时调用
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.SetFocus">
            <summary>
            设置焦点到关联对象
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.MoveFocusToNext">
            <summary>
            移动焦点到下一个元素
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.MoveFocusToPrevious">
            <summary>
            移动焦点到上一个元素
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.FocusEventArgs">
            <summary>
            焦点事件参数
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.FocusEventArgs.Element">
            <summary>
            焦点元素
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.FocusEventArgs.#ctor(System.Windows.FrameworkElement)">
            <summary>
            构造函数
            </summary>
            <param name="element">焦点元素</param>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.ScrollBehavior">
            <summary>
            滚动行为，为ScrollViewer提供增强的滚动功能
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.IsSmoothScrollEnabledProperty">
            <summary>
            是否启用平滑滚动
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollDurationProperty">
            <summary>
            滚动动画持续时间
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollEasingFunctionProperty">
            <summary>
            滚动缓动函数
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.MouseWheelStepProperty">
            <summary>
            鼠标滚轮滚动步长
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.AutoHideScrollBarProperty">
            <summary>
            是否启用自动隐藏滚动条
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollBarHideDelayProperty">
            <summary>
            滚动条自动隐藏延迟时间
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.EnableBounceEffectProperty">
            <summary>
            是否启用边界反弹效果
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.BounceIntensityProperty">
            <summary>
            反弹强度
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.IsSmoothScrollEnabled">
            <summary>
            获取或设置是否启用平滑滚动
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollDuration">
            <summary>
            获取或设置滚动动画持续时间
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollEasingFunction">
            <summary>
            获取或设置滚动缓动函数
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.MouseWheelStep">
            <summary>
            获取或设置鼠标滚轮滚动步长
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.AutoHideScrollBar">
            <summary>
            获取或设置是否启用自动隐藏滚动条
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollBarHideDelay">
            <summary>
            获取或设置滚动条自动隐藏延迟时间
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.EnableBounceEffect">
            <summary>
            获取或设置是否启用边界反弹效果
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.BounceIntensity">
            <summary>
            获取或设置反弹强度
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollStarted">
            <summary>
            滚动开始事件
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollCompleted">
            <summary>
            滚动结束事件
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ReachedTop">
            <summary>
            到达顶部事件
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ReachedBottom">
            <summary>
            到达底部事件
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.OnAttached">
            <summary>
            附加到关联对象时调用
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.OnDetaching">
            <summary>
            从关联对象分离时调用
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollToVerticalOffset(System.Double)">
            <summary>
            平滑滚动到指定垂直位置
            </summary>
            <param name="offset">垂直偏移量</param>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollToHorizontalOffset(System.Double)">
            <summary>
            平滑滚动到指定水平位置
            </summary>
            <param name="offset">水平偏移量</param>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollToTop">
            <summary>
            滚动到顶部
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollToBottom">
            <summary>
            滚动到底部
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollToLeft">
            <summary>
            滚动到左侧
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollToRight">
            <summary>
            滚动到右侧
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.ScrollEventArgs">
            <summary>
            滚动事件参数
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ScrollEventArgs.FromOffset">
            <summary>
            起始位置
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ScrollEventArgs.ToOffset">
            <summary>
            目标位置
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ScrollEventArgs.Direction">
            <summary>
            滚动方向
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ScrollEventArgs.#ctor(System.Double,System.Double,FluentSystemDesign.WPF.Behaviors.ScrollDirection)">
            <summary>
            构造函数
            </summary>
            <param name="fromOffset">起始位置</param>
            <param name="toOffset">目标位置</param>
            <param name="direction">滚动方向</param>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.ScrollDirection">
            <summary>
            滚动方向枚举
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ScrollDirection.Vertical">
            <summary>
            垂直滚动
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ScrollDirection.Horizontal">
            <summary>
            水平滚动
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.ValidationBehavior">
            <summary>
            输入验证行为，为输入控件提供实时验证功能
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.ValidationTypeProperty">
            <summary>
            验证规则类型
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.RegexPatternProperty">
            <summary>
            正则表达式模式
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.MinLengthProperty">
            <summary>
            最小长度
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.MaxLengthProperty">
            <summary>
            最大长度
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.IsRequiredProperty">
            <summary>
            是否必填
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.ErrorMessageProperty">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.ValidateOnTextChangedProperty">
            <summary>
            是否实时验证
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.ErrorBorderBrushProperty">
            <summary>
            错误边框颜色
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.NormalBorderBrushProperty">
            <summary>
            正常边框颜色
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.ValidationType">
            <summary>
            获取或设置验证规则类型
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.RegexPattern">
            <summary>
            获取或设置正则表达式模式
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.MinLength">
            <summary>
            获取或设置最小长度
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.MaxLength">
            <summary>
            获取或设置最大长度
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.IsRequired">
            <summary>
            获取或设置是否必填
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.ErrorMessage">
            <summary>
            获取或设置错误消息
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.ValidateOnTextChanged">
            <summary>
            获取或设置是否实时验证
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.ErrorBorderBrush">
            <summary>
            获取或设置错误边框颜色
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.NormalBorderBrush">
            <summary>
            获取或设置正常边框颜色
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.IsValid">
            <summary>
            获取当前验证状态
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.CurrentError">
            <summary>
            获取当前错误信息
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.ValidationChanged">
            <summary>
            验证状态改变事件
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.OnAttached">
            <summary>
            附加到关联对象时调用
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.OnDetaching">
            <summary>
            从关联对象分离时调用
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.Validate">
            <summary>
            手动验证
            </summary>
            <returns>验证是否通过</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.ClearValidation">
            <summary>
            清除验证状态
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.ValidationType">
            <summary>
            验证类型枚举
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationType.None">
            <summary>
            无验证
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationType.Email">
            <summary>
            邮箱验证
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationType.Phone">
            <summary>
            手机号验证
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationType.Number">
            <summary>
            数字验证
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationType.Decimal">
            <summary>
            小数验证
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationType.Url">
            <summary>
            网址验证
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationType.Chinese">
            <summary>
            中文验证
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationType.English">
            <summary>
            英文验证
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationType.AlphaNumeric">
            <summary>
            字母数字验证
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.ValidationChangedEventArgs">
            <summary>
            验证状态改变事件参数
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationChangedEventArgs.IsValid">
            <summary>
            是否验证通过
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationChangedEventArgs.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ValidationChangedEventArgs.#ctor(System.Boolean,System.String)">
            <summary>
            构造函数
            </summary>
            <param name="isValid">是否验证通过</param>
            <param name="errorMessage">错误消息</param>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior">
            <summary>
            水印行为，为文本输入控件提供水印功能
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkTextProperty">
            <summary>
            水印文本
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkFontSizeProperty">
            <summary>
            水印字体大小
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkFontStyleProperty">
            <summary>
            水印字体样式
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkFontWeightProperty">
            <summary>
            水印字体粗细
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkForegroundProperty">
            <summary>
            水印前景色
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkOpacityProperty">
            <summary>
            水印透明度
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkHorizontalAlignmentProperty">
            <summary>
            水印水平对齐方式
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkVerticalAlignmentProperty">
            <summary>
            水印垂直对齐方式
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkMarginProperty">
            <summary>
            水印边距
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.HideOnFocusProperty">
            <summary>
            是否在获得焦点时隐藏水印
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkText">
            <summary>
            获取或设置水印文本
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkFontSize">
            <summary>
            获取或设置水印字体大小
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkFontStyle">
            <summary>
            获取或设置水印字体样式
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkFontWeight">
            <summary>
            获取或设置水印字体粗细
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkForeground">
            <summary>
            获取或设置水印前景色
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkOpacity">
            <summary>
            获取或设置水印透明度
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkHorizontalAlignment">
            <summary>
            获取或设置水印水平对齐方式
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkVerticalAlignment">
            <summary>
            获取或设置水印垂直对齐方式
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkMargin">
            <summary>
            获取或设置水印边距
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.HideOnFocus">
            <summary>
            获取或设置是否在获得焦点时隐藏水印
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.OnAttached">
            <summary>
            附加到关联对象时调用
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.OnDetaching">
            <summary>
            从关联对象分离时调用
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.WatermarkAdorner">
            <summary>
            水印装饰器
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.WindowBehavior">
            <summary>
            窗口行为，为Window提供增强功能
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WindowBehavior.EnableDragMoveProperty">
            <summary>
            是否启用拖拽移动
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WindowBehavior.EnableDoubleClickMaximizeProperty">
            <summary>
            是否启用双击最大化/还原
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WindowBehavior.SaveWindowPlacementProperty">
            <summary>
            是否保存窗口位置和大小
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WindowBehavior.PlacementKeyProperty">
            <summary>
            窗口位置保存键
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WindowBehavior.EnableFadeEffectProperty">
            <summary>
            是否启用窗口淡入淡出效果
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WindowBehavior.FadeDurationProperty">
            <summary>
            淡入淡出持续时间
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WindowBehavior.HideMinimizeButtonProperty">
            <summary>
            是否隐藏最小化按钮
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WindowBehavior.HideMaximizeButtonProperty">
            <summary>
            是否隐藏最大化按钮
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WindowBehavior.HideCloseButtonProperty">
            <summary>
            是否隐藏关闭按钮
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WindowBehavior.EnableDropShadowProperty">
            <summary>
            是否启用窗口阴影
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WindowBehavior.EnableEscapeKeyCloseProperty">
            <summary>
            是否启用ESC键关闭窗口
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowBehavior.EnableDragMove">
            <summary>
            获取或设置是否启用拖拽移动
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowBehavior.EnableDoubleClickMaximize">
            <summary>
            获取或设置是否启用双击最大化/还原
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowBehavior.SaveWindowPlacement">
            <summary>
            获取或设置是否保存窗口位置和大小
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowBehavior.PlacementKey">
            <summary>
            获取或设置窗口位置保存键
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowBehavior.EnableFadeEffect">
            <summary>
            获取或设置是否启用窗口淡入淡出效果
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowBehavior.FadeDuration">
            <summary>
            获取或设置淡入淡出持续时间
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowBehavior.HideMinimizeButton">
            <summary>
            获取或设置是否隐藏最小化按钮
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowBehavior.HideMaximizeButton">
            <summary>
            获取或设置是否隐藏最大化按钮
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowBehavior.HideCloseButton">
            <summary>
            获取或设置是否隐藏关闭按钮
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowBehavior.EnableDropShadow">
            <summary>
            获取或设置是否启用窗口阴影
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowBehavior.EnableEscapeKeyClose">
            <summary>
            获取或设置是否启用ESC键关闭窗口
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.WindowBehavior.WindowShowing">
            <summary>
            窗口显示前事件
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.WindowBehavior.WindowClosing">
            <summary>
            窗口关闭前事件
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.WindowBehavior.OnAttached">
            <summary>
            附加到关联对象时调用
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.WindowBehavior.OnDetaching">
            <summary>
            从关联对象分离时调用
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.WindowEventArgs">
            <summary>
            窗口事件参数
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowEventArgs.Window">
            <summary>
            窗口对象
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.WindowEventArgs.#ctor(System.Windows.Window)">
            <summary>
            构造函数
            </summary>
            <param name="window">窗口对象</param>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.WindowClosingEventArgs">
            <summary>
            窗口关闭事件参数
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowClosingEventArgs.Cancel">
            <summary>
            是否取消关闭
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.WindowClosingEventArgs.#ctor(System.Windows.Window)">
            <summary>
            构造函数
            </summary>
            <param name="window">窗口对象</param>
        </member>
        <member name="T:FluentSystemDesign.WPF.Controls.ColorPalette">
            <summary>
            色彩调色板控件，用于展示色彩系统
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Controls.ColorPalette.Title">
            <summary>
            调色板标题
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Controls.ColorPalette.TitleProperty">
            <summary>
            标识 Title 依赖属性
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Controls.ColorPalette.ColorItems">
            <summary>
            色彩项集合
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Controls.ColorPalette.ColorItemsProperty">
            <summary>
            标识 ColorItems 依赖属性
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Controls.ColorPalette.#ctor">
            <summary>
            初始化 ColorPalette 类的新实例
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Controls.ColorItem">
            <summary>
            色彩项
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Controls.ColorItem.Name">
            <summary>
            色彩名称
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Controls.ColorItem.NameProperty">
            <summary>
            标识 Name 依赖属性
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Controls.ColorItem.Color">
            <summary>
            色彩值
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Controls.ColorItem.ColorProperty">
            <summary>
            标识 Color 依赖属性
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Controls.ColorItem.Brush">
            <summary>
            色彩画刷
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Controls.ColorItem.BrushProperty">
            <summary>
            标识 Brush 依赖属性
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Controls.ColorItem.HexValue">
            <summary>
            十六进制色值
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Controls.ColorItem.HexValueProperty">
            <summary>
            标识 HexValue 依赖属性
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Controls.ColorItem.IsDark">
            <summary>
            是否为深色
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Controls.ColorItem.IsDarkProperty">
            <summary>
            标识 IsDark 依赖属性
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Controls.ColorItem.#ctor">
            <summary>
            初始化 ColorItem 类的新实例
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Controls.ColorItemCollection">
            <summary>
            色彩项集合
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.BoolToOpacityConverter">
            <summary>
            布尔值到透明度转换器
            将布尔值转换为透明度值，用于控制元素的可见程度
            </summary>
            <remarks>
            默认行为：true -> 1.0（完全不透明）, false -> 0.0（完全透明）
            可以通过参数自定义透明度值，格式："TrueValue,FalseValue"，例如："1.0,0.3"
            使用参数"Invert"可以反转行为
            </remarks>
            <example>
            在XAML中使用：
            <code>
            &lt;TextBlock Opacity="{Binding IsEnabled, Converter={StaticResource BoolToOpacityConverter}}"/&gt;
            &lt;TextBlock Opacity="{Binding IsDisabled, Converter={StaticResource BoolToOpacityConverter}, ConverterParameter=Invert}"/&gt;
            &lt;TextBlock Opacity="{Binding IsHighlighted, Converter={StaticResource BoolToOpacityConverter}, ConverterParameter=1.0,0.5}"/&gt;
            </code>
            </example>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.BoolToOpacityConverter.TrueOpacity">
            <summary>
            默认的true值对应的透明度
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.BoolToOpacityConverter.FalseOpacity">
            <summary>
            默认的false值对应的透明度
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.BoolToOpacityConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将布尔值转换为透明度
            </summary>
            <param name="value">布尔值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数：Invert（反转）或"TrueValue,FalseValue"格式的自定义值</param>
            <param name="culture">文化信息</param>
            <returns>透明度值</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.BoolToOpacityConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将透明度转换为布尔值
            </summary>
            <param name="value">透明度值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数</param>
            <param name="culture">文化信息</param>
            <returns>布尔值</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.BoolToVisibilityConverter">
            <summary>
            布尔值到可见性转换器
            将布尔值转换为Visibility枚举值，支持反向转换
            </summary>
            <remarks>
            默认行为：true -> Visible, false -> Collapsed
            使用参数"Invert"可以反转行为：true -> Collapsed, false -> Visible
            使用参数"Hidden"可以将false转换为Hidden而不是Collapsed
            </remarks>
            <example>
            在XAML中使用：
            <code>
            &lt;TextBlock Visibility="{Binding IsVisible, Converter={StaticResource BoolToVisibilityConverter}}"/&gt;
            &lt;TextBlock Visibility="{Binding IsHidden, Converter={StaticResource BoolToVisibilityConverter}, ConverterParameter=Invert}"/&gt;
            &lt;TextBlock Visibility="{Binding IsVisible, Converter={StaticResource BoolToVisibilityConverter}, ConverterParameter=Hidden}"/&gt;
            </code>
            </example>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.BoolToVisibilityConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将布尔值转换为可见性
            </summary>
            <param name="value">布尔值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数：Invert（反转）、Hidden（使用Hidden而不是Collapsed）</param>
            <param name="culture">文化信息</param>
            <returns>可见性值</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.BoolToVisibilityConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将可见性转换为布尔值
            </summary>
            <param name="value">可见性值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数</param>
            <param name="culture">文化信息</param>
            <returns>布尔值</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.CollectionToVisibilityConverter">
            <summary>
            集合到可见性转换器
            根据集合是否为空来控制元素的可见性
            </summary>
            <remarks>
            默认行为：
            - 集合有元素时显示（Visible）
            - 集合为空或null时隐藏（Collapsed）
            
            支持的参数：
            - "Invert": 反转逻辑（空时显示，有元素时隐藏）
            - "Hidden": 使用Hidden而不是Collapsed
            - "MinCount=N": 最小元素数量阈值
            - "MaxCount=N": 最大元素数量阈值
            </remarks>
            <example>
            在XAML中使用：
            <code>
            &lt;TextBlock Visibility="{Binding Items, Converter={StaticResource CollectionToVisibilityConverter}}" Text="有数据"/&gt;
            &lt;TextBlock Visibility="{Binding Items, Converter={StaticResource CollectionToVisibilityConverter}, ConverterParameter=Invert}" Text="无数据"/&gt;
            &lt;Button Visibility="{Binding Items, Converter={StaticResource CollectionToVisibilityConverter}, ConverterParameter=MinCount=5}"/&gt;
            </code>
            </example>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.CollectionToVisibilityConverter.DefaultHiddenVisibility">
            <summary>
            默认的隐藏方式
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.CollectionToVisibilityConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将集合转换为可见性
            </summary>
            <param name="value">集合对象</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数</param>
            <param name="culture">文化信息</param>
            <returns>可见性值</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.CollectionToVisibilityConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            反向转换（不支持）
            </summary>
            <param name="value">可见性值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数</param>
            <param name="culture">文化信息</param>
            <returns>不支持的操作异常</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.CollectionToVisibilityConverter.GetCollectionCount(System.Object)">
            <summary>
            获取集合的元素数量
            </summary>
            <param name="collection">集合对象</param>
            <returns>元素数量</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.CollectionToVisibilityConverter.ParseParameter(System.String)">
            <summary>
            解析参数字符串
            </summary>
            <param name="parameter">参数字符串</param>
            <returns>解析结果</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.ColorToBrushConverter">
            <summary>
            颜色到画刷转换器
            将Color对象转换为SolidColorBrush，支持透明度调整
            </summary>
            <remarks>
            支持的功能：
            - Color -> SolidColorBrush 转换
            - 通过参数调整透明度，格式："Alpha=0.5"
            - 支持冻结画刷以提高性能
            - 支持从十六进制字符串创建画刷
            </remarks>
            <example>
            在XAML中使用：
            <code>
            &lt;Rectangle Fill="{Binding BackgroundColor, Converter={StaticResource ColorToBrushConverter}}"/&gt;
            &lt;Rectangle Fill="{Binding BackgroundColor, Converter={StaticResource ColorToBrushConverter}, ConverterParameter=Alpha=0.5}"/&gt;
            &lt;Border Background="{Binding HexColor, Converter={StaticResource ColorToBrushConverter}}"/&gt;
            </code>
            </example>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.ColorToBrushConverter.FreezeBrush">
            <summary>
            是否冻结创建的画刷以提高性能
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ColorToBrushConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将颜色转换为画刷
            </summary>
            <param name="value">Color对象或十六进制颜色字符串</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数：Alpha=透明度值</param>
            <param name="culture">文化信息</param>
            <returns>SolidColorBrush对象</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ColorToBrushConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将画刷转换为颜色
            </summary>
            <param name="value">SolidColorBrush对象</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数</param>
            <param name="culture">文化信息</param>
            <returns>Color对象或十六进制字符串</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ColorToBrushConverter.TryParseColor(System.String,System.Windows.Media.Color@)">
            <summary>
            尝试解析颜色字符串
            </summary>
            <param name="colorString">颜色字符串</param>
            <param name="color">解析出的颜色</param>
            <returns>是否解析成功</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ColorToBrushConverter.TryParseHexColor(System.String,System.Windows.Media.Color@)">
            <summary>
            尝试解析十六进制颜色
            </summary>
            <param name="hex">十六进制颜色字符串</param>
            <param name="color">解析出的颜色</param>
            <returns>是否解析成功</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ColorToBrushConverter.ParseAlphaParameter(System.String)">
            <summary>
            解析透明度参数
            </summary>
            <param name="parameter">参数字符串</param>
            <returns>透明度值（0-1）</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ColorToBrushConverter.ColorToHex(System.Windows.Media.Color)">
            <summary>
            将颜色转换为十六进制字符串
            </summary>
            <param name="color">颜色</param>
            <returns>十六进制字符串</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.ColorToContrastConverter">
            <summary>
            颜色对比度转换器
            根据输入颜色的亮度自动选择对比度最佳的前景色（通常是黑色或白色）
            </summary>
            <remarks>
            使用相对亮度计算公式来确定颜色的亮度，然后选择对比度最佳的前景色。
            默认行为：亮色背景返回黑色，暗色背景返回白色。
            可以通过参数自定义返回的颜色。
            </remarks>
            <example>
            在XAML中使用：
            <code>
            &lt;TextBlock Foreground="{Binding BackgroundColor, Converter={StaticResource ColorToContrastConverter}}"/&gt;
            &lt;TextBlock Foreground="{Binding BackgroundColor, Converter={StaticResource ColorToContrastConverter}, ConverterParameter=Light=#FFFFFF,Dark=#000000}"/&gt;
            </code>
            </example>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.ColorToContrastConverter.LuminanceThreshold">
            <summary>
            亮度阈值（0-1），超过此值认为是亮色
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.ColorToContrastConverter.LightBackgroundForeground">
            <summary>
            亮色背景时使用的前景色
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.ColorToContrastConverter.DarkBackgroundForeground">
            <summary>
            暗色背景时使用的前景色
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ColorToContrastConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将颜色转换为对比色
            </summary>
            <param name="value">背景颜色（Color、Brush或十六进制字符串）</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">自定义颜色参数，格式："Light=#FFFFFF,Dark=#000000"</param>
            <param name="culture">文化信息</param>
            <returns>对比色</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ColorToContrastConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            反向转换（不支持）
            </summary>
            <param name="value">值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">参数</param>
            <param name="culture">文化信息</param>
            <returns>不支持的操作异常</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ColorToContrastConverter.CalculateRelativeLuminance(System.Windows.Media.Color)">
            <summary>
            计算颜色的相对亮度
            </summary>
            <param name="color">颜色</param>
            <returns>相对亮度（0-1）</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ColorToContrastConverter.ParseColorParameters(System.String)">
            <summary>
            解析颜色参数
            </summary>
            <param name="parameter">参数字符串</param>
            <returns>亮色和暗色</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ColorToContrastConverter.TryParseColor(System.String,System.Windows.Media.Color@)">
            <summary>
            尝试解析颜色字符串
            </summary>
            <param name="colorString">颜色字符串</param>
            <param name="color">解析出的颜色</param>
            <returns>是否解析成功</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ColorToContrastConverter.TryParseHexColor(System.String,System.Windows.Media.Color@)">
            <summary>
            尝试解析十六进制颜色
            </summary>
            <param name="hex">十六进制颜色字符串</param>
            <param name="color">解析出的颜色</param>
            <returns>是否解析成功</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.ConverterTestHelper">
            <summary>
            转换器测试辅助类
            提供转换器功能验证和测试的辅助方法
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ConverterTestHelper.TestConvert(System.Windows.Data.IValueConverter,System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            测试转换器的基本功能
            </summary>
            <param name="converter">要测试的转换器</param>
            <param name="value">输入值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数</param>
            <param name="culture">文化信息</param>
            <returns>转换结果</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ConverterTestHelper.TestConvertBack(System.Windows.Data.IValueConverter,System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            测试转换器的反向转换功能
            </summary>
            <param name="converter">要测试的转换器</param>
            <param name="value">输入值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数</param>
            <param name="culture">文化信息</param>
            <returns>反向转换结果</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ConverterTestHelper.TestNullHandling(System.Windows.Data.IValueConverter,System.Type)">
            <summary>
            验证转换器是否正确处理null值
            </summary>
            <param name="converter">要测试的转换器</param>
            <param name="targetType">目标类型</param>
            <returns>是否正确处理null值</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ConverterTestHelper.TestInvalidInputHandling(System.Windows.Data.IValueConverter,System.Object,System.Type)">
            <summary>
            验证转换器是否正确处理无效输入
            </summary>
            <param name="converter">要测试的转换器</param>
            <param name="invalidValue">无效输入值</param>
            <param name="targetType">目标类型</param>
            <returns>是否正确处理无效输入</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ConverterTestHelper.GetConverterInfo(System.Windows.Data.IValueConverter)">
            <summary>
            获取转换器的基本信息
            </summary>
            <param name="converter">转换器实例</param>
            <returns>转换器信息</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ConverterTestHelper.RunFullTest(System.Windows.Data.IValueConverter,System.Object,System.Type,System.Object)">
            <summary>
            运行转换器的完整测试套件
            </summary>
            <param name="converter">要测试的转换器</param>
            <param name="testValue">测试值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数</param>
            <returns>测试结果报告</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.CountToVisibilityConverter">
            <summary>
            数量到可见性转换器
            根据数值与阈值的比较结果来控制元素的可见性
            </summary>
            <remarks>
            支持的比较操作：
            - "&gt;" 或 "GreaterThan": 大于指定值时显示
            - "&gt;=" 或 "GreaterThanOrEqual": 大于等于指定值时显示
            - "&lt;" 或 "LessThan": 小于指定值时显示
            - "&lt;=" 或 "LessThanOrEqual": 小于等于指定值时显示
            - "=" 或 "Equal": 等于指定值时显示
            - "!=" 或 "NotEqual": 不等于指定值时显示
            - "Range": 在指定范围内时显示，格式："Range:min,max"
            
            其他选项：
            - "Hidden": 使用Hidden而不是Collapsed
            - "Invert": 反转结果
            </remarks>
            <example>
            在XAML中使用：
            <code>
            &lt;TextBlock Visibility="{Binding ItemCount, Converter={StaticResource CountToVisibilityConverter}, ConverterParameter=>0}"/&gt;
            &lt;Button Visibility="{Binding SelectedCount, Converter={StaticResource CountToVisibilityConverter}, ConverterParameter=>=5}"/&gt;
            &lt;ProgressBar Visibility="{Binding Progress, Converter={StaticResource CountToVisibilityConverter}, ConverterParameter=Range:1,99}"/&gt;
            </code>
            </example>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.CountToVisibilityConverter.DefaultHiddenVisibility">
            <summary>
            默认的隐藏方式
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.CountToVisibilityConverter.DefaultOperation">
            <summary>
            默认的比较操作
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.CountToVisibilityConverter.DefaultThreshold">
            <summary>
            默认的比较值
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.CountToVisibilityConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将数值转换为可见性
            </summary>
            <param name="value">数值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">比较参数</param>
            <param name="culture">文化信息</param>
            <returns>可见性值</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.CountToVisibilityConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            反向转换（不支持）
            </summary>
            <param name="value">可见性值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数</param>
            <param name="culture">文化信息</param>
            <returns>不支持的操作异常</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.CountToVisibilityConverter.TryConvertToDouble(System.Object,System.Double@)">
            <summary>
            尝试将对象转换为double
            </summary>
            <param name="value">要转换的值</param>
            <param name="result">转换结果</param>
            <returns>是否转换成功</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.CountToVisibilityConverter.ParseParameter(System.String,System.Globalization.CultureInfo)">
            <summary>
            解析参数字符串
            </summary>
            <param name="parameter">参数字符串</param>
            <param name="culture">文化信息</param>
            <returns>解析结果</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.CountToVisibilityConverter.ComparisonOperation">
            <summary>
            比较操作类型
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.EnumToBoolConverter">
            <summary>
            枚举到布尔值转换器
            将枚举值与指定值比较，相等时返回true，否则返回false
            </summary>
            <remarks>
            主要用于RadioButton和CheckBox的绑定场景。
            参数可以是：
            - 单个枚举值名称
            - 多个枚举值名称（用逗号分隔），任一匹配即返回true
            - "Invert"前缀可以反转结果
            </remarks>
            <example>
            在XAML中使用：
            <code>
            &lt;RadioButton IsChecked="{Binding Status, Converter={StaticResource EnumToBoolConverter}, ConverterParameter=Active}"/&gt;
            &lt;RadioButton IsChecked="{Binding Status, Converter={StaticResource EnumToBoolConverter}, ConverterParameter=Inactive,Disabled}"/&gt;
            &lt;CheckBox IsChecked="{Binding Status, Converter={StaticResource EnumToBoolConverter}, ConverterParameter=Invert:Active}"/&gt;
            </code>
            </example>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.EnumToBoolConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将枚举值转换为布尔值
            </summary>
            <param name="value">枚举值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">比较的枚举值名称</param>
            <param name="culture">文化信息</param>
            <returns>比较结果</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.EnumToBoolConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将布尔值转换为枚举值
            </summary>
            <param name="value">布尔值</param>
            <param name="targetType">目标枚举类型</param>
            <param name="parameter">目标枚举值名称</param>
            <param name="culture">文化信息</param>
            <returns>枚举值</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.EnumToBoolConverter.ParseParameter(System.String)">
            <summary>
            解析参数字符串
            </summary>
            <param name="parameter">参数字符串</param>
            <returns>是否反转和目标值列表</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.EnumToStringConverter">
            <summary>
            枚举到字符串转换器
            将枚举值转换为字符串，支持Description特性和本地化
            </summary>
            <remarks>
            转换优先级：
            1. Description特性的值
            2. 枚举值的名称
            支持参数：
            - "UseDescription": 强制使用Description特性
            - "UseName": 强制使用枚举名称
            - "ToUpper": 转换为大写
            - "ToLower": 转换为小写
            </remarks>
            <example>
            在XAML中使用：
            <code>
            &lt;TextBlock Text="{Binding Status, Converter={StaticResource EnumToStringConverter}}"/&gt;
            &lt;TextBlock Text="{Binding Status, Converter={StaticResource EnumToStringConverter}, ConverterParameter=UseDescription}"/&gt;
            &lt;TextBlock Text="{Binding Status, Converter={StaticResource EnumToStringConverter}, ConverterParameter=ToUpper}"/&gt;
            </code>
            </example>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.EnumToStringConverter.UseDescriptionByDefault">
            <summary>
            默认是否使用Description特性
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.EnumToStringConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将枚举值转换为字符串
            </summary>
            <param name="value">枚举值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数</param>
            <param name="culture">文化信息</param>
            <returns>字符串</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.EnumToStringConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将字符串转换为枚举值
            </summary>
            <param name="value">字符串</param>
            <param name="targetType">目标枚举类型</param>
            <param name="parameter">转换参数</param>
            <param name="culture">文化信息</param>
            <returns>枚举值</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.EnumToStringConverter.GetEnumDescription(System.Enum)">
            <summary>
            获取枚举值的Description特性值
            </summary>
            <param name="enumValue">枚举值</param>
            <returns>Description特性值，如果没有则返回null</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.EnumToStringConverter.ApplyCaseTransform(System.String,FluentSystemDesign.WPF.Converters.EnumToStringConverter.CaseTransform,System.Globalization.CultureInfo)">
            <summary>
            应用大小写转换
            </summary>
            <param name="input">输入字符串</param>
            <param name="transform">转换类型</param>
            <param name="culture">文化信息</param>
            <returns>转换后的字符串</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.EnumToStringConverter.CaseTransform">
            <summary>
            大小写转换类型
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.EnumToVisibilityConverter">
            <summary>
            枚举到可见性转换器
            将枚举值与指定值比较，匹配时显示，否则隐藏
            </summary>
            <remarks>
            参数格式：
            - 单个枚举值名称：匹配时显示
            - 多个枚举值名称（用逗号分隔）：任一匹配即显示
            - "Invert:"前缀：反转逻辑
            - "Hidden"后缀：使用Hidden而不是Collapsed
            例如："Active,Processing" 或 "Invert:Disabled,Hidden"
            </remarks>
            <example>
            在XAML中使用：
            <code>
            &lt;TextBlock Visibility="{Binding Status, Converter={StaticResource EnumToVisibilityConverter}, ConverterParameter=Active}"/&gt;
            &lt;Button Visibility="{Binding Status, Converter={StaticResource EnumToVisibilityConverter}, ConverterParameter=Active,Processing}"/&gt;
            &lt;ProgressBar Visibility="{Binding Status, Converter={StaticResource EnumToVisibilityConverter}, ConverterParameter=Invert:Completed,Hidden}"/&gt;
            </code>
            </example>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.EnumToVisibilityConverter.DefaultHiddenVisibility">
            <summary>
            默认的隐藏方式
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.EnumToVisibilityConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将枚举值转换为可见性
            </summary>
            <param name="value">枚举值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">比较的枚举值名称和选项</param>
            <param name="culture">文化信息</param>
            <returns>可见性值</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.EnumToVisibilityConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将可见性转换为枚举值
            </summary>
            <param name="value">可见性值</param>
            <param name="targetType">目标枚举类型</param>
            <param name="parameter">目标枚举值名称</param>
            <param name="culture">文化信息</param>
            <returns>枚举值</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.EnumToVisibilityConverter.ParseParameter(System.String)">
            <summary>
            解析参数字符串
            </summary>
            <param name="parameter">参数字符串</param>
            <returns>是否反转、目标值列表和隐藏方式</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.HexToColorConverter">
            <summary>
            十六进制字符串到颜色转换器
            将十六进制颜色字符串转换为Color对象，支持多种格式
            </summary>
            <remarks>
            支持的十六进制格式：
            - #RGB (例如: #F0A)
            - #RRGGBB (例如: #FF00AA)
            - #AARRGGBB (例如: #80FF00AA)
            - RGB (不带#前缀)
            - RRGGBB (不带#前缀)
            - AARRGGBB (不带#前缀)
            </remarks>
            <example>
            在XAML中使用：
            <code>
            &lt;Rectangle Fill="{Binding HexColor, Converter={StaticResource HexToColorConverter}}"/&gt;
            &lt;Border BorderBrush="{Binding BorderHex, Converter={StaticResource HexToColorConverter}}"/&gt;
            </code>
            </example>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.HexToColorConverter.DefaultColor">
            <summary>
            默认颜色（当转换失败时使用）
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.HexToColorConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将十六进制字符串转换为颜色
            </summary>
            <param name="value">十六进制颜色字符串</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数（未使用）</param>
            <param name="culture">文化信息</param>
            <returns>Color对象</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.HexToColorConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将颜色转换为十六进制字符串
            </summary>
            <param name="value">Color对象</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数：格式选项</param>
            <param name="culture">文化信息</param>
            <returns>十六进制字符串</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.HexToColorConverter.TryParseHexColor(System.String,System.Windows.Media.Color@)">
            <summary>
            尝试解析十六进制颜色字符串
            </summary>
            <param name="hex">十六进制字符串</param>
            <param name="color">解析出的颜色</param>
            <returns>是否解析成功</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.HexToColorConverter.IsValidHexString(System.String)">
            <summary>
            验证字符串是否为有效的十六进制字符串
            </summary>
            <param name="hex">十六进制字符串</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.HexToColorConverter.ColorToHex(System.Windows.Media.Color,System.Boolean)">
            <summary>
            将颜色转换为十六进制字符串
            </summary>
            <param name="color">颜色</param>
            <param name="includePrefix">是否包含#前缀</param>
            <returns>十六进制字符串</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.HexToColorConverter.ColorToShortHex(System.Windows.Media.Color,System.Boolean)">
            <summary>
            将颜色转换为短格式十六进制字符串（如果可能）
            </summary>
            <param name="color">颜色</param>
            <param name="includePrefix">是否包含#前缀</param>
            <returns>十六进制字符串</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.HexToColorConverter.CanUseShortFormat(System.Byte)">
            <summary>
            检查字节值是否可以使用短格式表示
            </summary>
            <param name="value">字节值</param>
            <returns>是否可以使用短格式</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.InverseBoolConverter">
            <summary>
            反向布尔值转换器
            将布尔值进行反转：true -> false, false -> true
            </summary>
            <remarks>
            这个转换器常用于需要反向逻辑的场景，比如：
            - 当某个条件为true时隐藏控件
            - 当某个条件为false时启用控件
            </remarks>
            <example>
            在XAML中使用：
            <code>
            &lt;Button IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBoolConverter}}"/&gt;
            &lt;TextBlock Visibility="{Binding IsEmpty, Converter={StaticResource InverseBoolConverter}}"/&gt;
            </code>
            </example>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.InverseBoolConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将布尔值进行反转
            </summary>
            <param name="value">布尔值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数（未使用）</param>
            <param name="culture">文化信息</param>
            <returns>反转后的布尔值</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.InverseBoolConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将反转后的布尔值转换回原始值
            </summary>
            <param name="value">反转后的布尔值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数（未使用）</param>
            <param name="culture">文化信息</param>
            <returns>原始布尔值</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.IsNullOrEmptyConverter">
            <summary>
            空值或空集合检查转换器
            检查对象是否为null、空字符串或空集合，返回相应的布尔值
            </summary>
            <remarks>
            检查规则：
            - null -> true
            - 空字符串 -> true
            - 空集合 -> true
            - 其他情况 -> false
            
            支持的参数：
            - "Invert": 反转结果
            - "TrimString": 对字符串进行Trim后再检查
            - "IgnoreWhitespace": 将只包含空白字符的字符串视为空
            </remarks>
            <example>
            在XAML中使用：
            <code>
            &lt;Button IsEnabled="{Binding UserName, Converter={StaticResource IsNullOrEmptyConverter}, ConverterParameter=Invert}"/&gt;
            &lt;TextBlock Visibility="{Binding Items, Converter={StaticResource IsNullOrEmptyConverter}}" Text="无数据"/&gt;
            &lt;TextBox BorderBrush="Red" Visibility="{Binding ErrorMessage, Converter={StaticResource IsNullOrEmptyConverter}, ConverterParameter=Invert,IgnoreWhitespace}"/&gt;
            </code>
            </example>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.IsNullOrEmptyConverter.DefaultIgnoreWhitespace">
            <summary>
            默认是否忽略空白字符
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.IsNullOrEmptyConverter.DefaultTrimString">
            <summary>
            默认是否对字符串进行Trim
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.IsNullOrEmptyConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            检查对象是否为null或空
            </summary>
            <param name="value">要检查的对象</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数</param>
            <param name="culture">文化信息</param>
            <returns>是否为null或空</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.IsNullOrEmptyConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            反向转换
            </summary>
            <param name="value">布尔值或可见性值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数</param>
            <param name="culture">文化信息</param>
            <returns>转换结果</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.IsNullOrEmptyConverter.IsNullOrEmpty(System.Object,System.Boolean,System.Boolean)">
            <summary>
            检查对象是否为null或空
            </summary>
            <param name="value">要检查的对象</param>
            <param name="trimString">是否对字符串进行Trim</param>
            <param name="ignoreWhitespace">是否忽略空白字符</param>
            <returns>是否为null或空</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.IsNullOrEmptyConverter.IsNumericType(System.Type)">
            <summary>
            检查类型是否为数值类型
            </summary>
            <param name="type">类型</param>
            <returns>是否为数值类型</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.IsNullOrEmptyConverter.ParseParameter(System.String)">
            <summary>
            解析参数字符串
            </summary>
            <param name="parameter">参数字符串</param>
            <returns>解析结果</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.NumberToStringConverter">
            <summary>
            数值到字符串转换器
            将数值转换为格式化的字符串，支持自定义格式
            </summary>
            <remarks>
            支持标准数值格式字符串和自定义格式字符串
            常用格式：
            - "N" 或 "N2": 数字格式（带千位分隔符）
            - "C" 或 "C2": 货币格式
            - "P" 或 "P2": 百分比格式
            - "F" 或 "F2": 固定点格式
            - "0.00": 自定义格式
            </remarks>
            <example>
            在XAML中使用：
            <code>
            &lt;TextBlock Text="{Binding Price, Converter={StaticResource NumberToStringConverter}, ConverterParameter=C2}"/&gt;
            &lt;TextBlock Text="{Binding Percentage, Converter={StaticResource NumberToStringConverter}, ConverterParameter=P1}"/&gt;
            &lt;TextBlock Text="{Binding Count, Converter={StaticResource NumberToStringConverter}, ConverterParameter=N0}"/&gt;
            </code>
            </example>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.NumberToStringConverter.DefaultFormat">
            <summary>
            默认格式字符串
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.NumberToStringConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将数值转换为格式化字符串
            </summary>
            <param name="value">数值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">格式字符串参数</param>
            <param name="culture">文化信息</param>
            <returns>格式化的字符串</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.NumberToStringConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将格式化字符串转换回数值
            </summary>
            <param name="value">格式化字符串</param>
            <param name="targetType">目标数值类型</param>
            <param name="parameter">格式字符串参数</param>
            <param name="culture">文化信息</param>
            <returns>数值</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.NumberToStringConverter.CleanNumericString(System.String,System.Globalization.CultureInfo)">
            <summary>
            清理数值字符串，移除格式化字符
            </summary>
            <param name="input">输入字符串</param>
            <param name="culture">文化信息</param>
            <returns>清理后的数值字符串</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.PercentageConverter">
            <summary>
            百分比转换器
            将0-1之间的小数值转换为百分比字符串，或将百分比字符串转换回小数值
            </summary>
            <remarks>
            默认行为：0.5 -> "50%", 0.75 -> "75%"
            支持自定义小数位数，通过参数指定，例如："2"表示保留2位小数
            支持不同的输入范围：
            - 默认：0-1范围（0.5 = 50%）
            - 参数"0-100"：0-100范围（50 = 50%）
            </remarks>
            <example>
            在XAML中使用：
            <code>
            &lt;TextBlock Text="{Binding Progress, Converter={StaticResource PercentageConverter}}"/&gt;
            &lt;TextBlock Text="{Binding Progress, Converter={StaticResource PercentageConverter}, ConverterParameter=1}"/&gt;
            &lt;TextBlock Text="{Binding Score, Converter={StaticResource PercentageConverter}, ConverterParameter=0-100}"/&gt;
            </code>
            </example>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.PercentageConverter.DefaultDecimalPlaces">
            <summary>
            默认小数位数
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.PercentageConverter.DefaultIsZeroToOneRange">
            <summary>
            默认输入范围（true: 0-1, false: 0-100）
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.PercentageConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将数值转换为百分比字符串
            </summary>
            <param name="value">数值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数：小数位数或"0-100"表示输入范围</param>
            <param name="culture">文化信息</param>
            <returns>百分比字符串</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.PercentageConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将百分比字符串转换为数值
            </summary>
            <param name="value">百分比字符串</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数</param>
            <param name="culture">文化信息</param>
            <returns>数值</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.PercentageConverter.TryConvertToDouble(System.Object,System.Double@)">
            <summary>
            尝试将对象转换为double
            </summary>
            <param name="value">要转换的值</param>
            <param name="result">转换结果</param>
            <returns>是否转换成功</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.StringFormatConverter">
            <summary>
            字符串格式化转换器
            使用指定的格式字符串对输入值进行格式化
            </summary>
            <remarks>
            支持标准的.NET字符串格式化功能：
            - 复合格式字符串："{0} - {1}"
            - 格式说明符："{0:C}", "{0:D}", "{0:F2}"
            - 自定义格式：任何有效的格式字符串
            
            特殊参数：
            - "Null=替换文本": 当输入为null时显示的文本
            - "Empty=替换文本": 当输入为空字符串时显示的文本
            - "Trim": 对字符串输入进行Trim操作
            </remarks>
            <example>
            在XAML中使用：
            <code>
            &lt;TextBlock Text="{Binding Price, Converter={StaticResource StringFormatConverter}, ConverterParameter='价格: {0:C}'}"/&gt;
            &lt;TextBlock Text="{Binding Count, Converter={StaticResource StringFormatConverter}, ConverterParameter='共 {0} 项'}"/&gt;
            &lt;TextBlock Text="{Binding UserName, Converter={StaticResource StringFormatConverter}, ConverterParameter='用户: {0},Null=未登录'}"/&gt;
            </code>
            </example>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.StringFormatConverter.DefaultFormat">
            <summary>
            默认格式字符串
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.StringFormatConverter.DefaultNullText">
            <summary>
            默认的null值替换文本
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.StringFormatConverter.DefaultEmptyText">
            <summary>
            默认的空字符串替换文本
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.StringFormatConverter.DefaultTrimString">
            <summary>
            默认是否对字符串进行Trim
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.StringFormatConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            格式化输入值
            </summary>
            <param name="value">输入值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">格式字符串和选项</param>
            <param name="culture">文化信息</param>
            <returns>格式化后的字符串</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.StringFormatConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            反向转换（尝试从格式化字符串中提取原始值）
            </summary>
            <param name="value">格式化后的字符串</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">格式字符串和选项</param>
            <param name="culture">文化信息</param>
            <returns>提取的原始值</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.StringFormatConverter.ExtractValueFromFormattedString(System.String,System.String)">
            <summary>
            从格式化字符串中提取原始值
            </summary>
            <param name="formattedString">格式化后的字符串</param>
            <param name="format">格式字符串</param>
            <returns>提取的值</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.StringFormatConverter.ConvertToTargetType(System.String,System.Type,System.Globalization.CultureInfo)">
            <summary>
            将字符串值转换为目标类型
            </summary>
            <param name="value">字符串值</param>
            <param name="targetType">目标类型</param>
            <param name="culture">文化信息</param>
            <returns>转换后的值</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.StringFormatConverter.ParseParameter(System.String)">
            <summary>
            解析参数字符串
            </summary>
            <param name="parameter">参数字符串</param>
            <returns>解析结果</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.StringToLowerConverter">
            <summary>
            字符串转小写转换器
            将字符串转换为小写形式
            </summary>
            <remarks>
            支持的参数：
            - "Invariant": 使用不变区域性进行转换
            - "Current": 使用当前区域性进行转换（默认）
            - "FirstOnly": 只将首字母转换为小写
            - "CamelCase": 转换为驼峰命名格式（首字母小写，其他单词首字母大写）
            </remarks>
            <example>
            在XAML中使用：
            <code>
            &lt;TextBlock Text="{Binding UserName, Converter={StaticResource StringToLowerConverter}}"/&gt;
            &lt;TextBlock Text="{Binding PropertyName, Converter={StaticResource StringToLowerConverter}, ConverterParameter=CamelCase}"/&gt;
            &lt;TextBlock Text="{Binding Code, Converter={StaticResource StringToLowerConverter}, ConverterParameter=Invariant}"/&gt;
            </code>
            </example>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.StringToLowerConverter.DefaultMode">
            <summary>
            默认的转换模式
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.StringToLowerConverter.UseInvariantCulture">
            <summary>
            默认是否使用不变区域性
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.StringToLowerConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将字符串转换为小写
            </summary>
            <param name="value">字符串值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数</param>
            <param name="culture">文化信息</param>
            <returns>小写字符串</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.StringToLowerConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将小写字符串转换回原始形式（不支持完全恢复）
            </summary>
            <param name="value">小写字符串</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数</param>
            <param name="culture">文化信息</param>
            <returns>转换后的字符串</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.StringToLowerConverter.ConvertFirstLetterToLower(System.String,System.Globalization.CultureInfo)">
            <summary>
            将首字母转换为小写
            </summary>
            <param name="input">输入字符串</param>
            <param name="culture">文化信息</param>
            <returns>首字母小写的字符串</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.StringToLowerConverter.ConvertFirstLetterToUpper(System.String,System.Globalization.CultureInfo)">
            <summary>
            将首字母转换为大写
            </summary>
            <param name="input">输入字符串</param>
            <param name="culture">文化信息</param>
            <returns>首字母大写的字符串</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.StringToLowerConverter.ConvertToCamelCase(System.String,System.Globalization.CultureInfo)">
            <summary>
            转换为驼峰命名格式
            </summary>
            <param name="input">输入字符串</param>
            <param name="culture">文化信息</param>
            <returns>驼峰格式的字符串</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.StringToLowerConverter.ConvertToPascalCase(System.String,System.Globalization.CultureInfo)">
            <summary>
            转换为帕斯卡命名格式（首字母大写的驼峰格式）
            </summary>
            <param name="input">输入字符串</param>
            <param name="culture">文化信息</param>
            <returns>帕斯卡格式的字符串</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.StringToLowerConverter.ParseParameter(System.String)">
            <summary>
            解析参数字符串
            </summary>
            <param name="parameter">参数字符串</param>
            <returns>转换模式和区域性设置</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.StringToLowerConverter.LowerCaseMode">
            <summary>
            小写转换模式
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Converters.StringToLowerConverter.LowerCaseMode.All">
            <summary>
            全部转换为小写
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Converters.StringToLowerConverter.LowerCaseMode.FirstOnly">
            <summary>
            只将首字母转换为小写
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Converters.StringToLowerConverter.LowerCaseMode.CamelCase">
            <summary>
            转换为驼峰命名格式
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.StringToUpperConverter">
            <summary>
            字符串转大写转换器
            将字符串转换为大写形式
            </summary>
            <remarks>
            支持的参数：
            - "Invariant": 使用不变区域性进行转换
            - "Current": 使用当前区域性进行转换（默认）
            - "FirstOnly": 只将首字母转换为大写
            - "Words": 将每个单词的首字母转换为大写（标题格式）
            </remarks>
            <example>
            在XAML中使用：
            <code>
            &lt;TextBlock Text="{Binding UserName, Converter={StaticResource StringToUpperConverter}}"/&gt;
            &lt;TextBlock Text="{Binding Title, Converter={StaticResource StringToUpperConverter}, ConverterParameter=Words}"/&gt;
            &lt;TextBlock Text="{Binding Code, Converter={StaticResource StringToUpperConverter}, ConverterParameter=Invariant}"/&gt;
            </code>
            </example>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.StringToUpperConverter.DefaultMode">
            <summary>
            默认的转换模式
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.StringToUpperConverter.UseInvariantCulture">
            <summary>
            默认是否使用不变区域性
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.StringToUpperConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将字符串转换为大写
            </summary>
            <param name="value">字符串值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数</param>
            <param name="culture">文化信息</param>
            <returns>大写字符串</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.StringToUpperConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将大写字符串转换回原始形式（不支持完全恢复）
            </summary>
            <param name="value">大写字符串</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数</param>
            <param name="culture">文化信息</param>
            <returns>转换后的字符串</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.StringToUpperConverter.ConvertFirstLetterToUpper(System.String,System.Globalization.CultureInfo)">
            <summary>
            将首字母转换为大写
            </summary>
            <param name="input">输入字符串</param>
            <param name="culture">文化信息</param>
            <returns>首字母大写的字符串</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.StringToUpperConverter.ConvertFirstLetterToLower(System.String,System.Globalization.CultureInfo)">
            <summary>
            将首字母转换为小写
            </summary>
            <param name="input">输入字符串</param>
            <param name="culture">文化信息</param>
            <returns>首字母小写的字符串</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.StringToUpperConverter.ConvertToTitleCase(System.String,System.Globalization.CultureInfo)">
            <summary>
            转换为标题格式（每个单词首字母大写）
            </summary>
            <param name="input">输入字符串</param>
            <param name="culture">文化信息</param>
            <returns>标题格式的字符串</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.StringToUpperConverter.ParseParameter(System.String)">
            <summary>
            解析参数字符串
            </summary>
            <param name="parameter">参数字符串</param>
            <returns>转换模式和区域性设置</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.StringToUpperConverter.UpperCaseMode">
            <summary>
            大写转换模式
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Converters.StringToUpperConverter.UpperCaseMode.All">
            <summary>
            全部转换为大写
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Converters.StringToUpperConverter.UpperCaseMode.FirstOnly">
            <summary>
            只将首字母转换为大写
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Converters.StringToUpperConverter.UpperCaseMode.Words">
            <summary>
            将每个单词的首字母转换为大写
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.StringToVisibilityConverter">
            <summary>
            字符串到可见性转换器
            空字符串或null转换为Collapsed，非空字符串转换为Visible
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.StringToVisibilityConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将字符串转换为可见性
            </summary>
            <param name="value">字符串值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">参数</param>
            <param name="culture">文化信息</param>
            <returns>可见性值</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.StringToVisibilityConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将可见性转换为字符串（不支持）
            </summary>
            <param name="value">可见性值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">参数</param>
            <param name="culture">文化信息</param>
            <returns>不支持的操作异常</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.TextCaseConverter">
            <summary>
            文本大小写转换器
            用于在XAML中实现文本的大小写转换功能
            </summary>
            <remarks>
            由于WPF的TextBlock不支持CSS的text-transform属性，
            此转换器提供了在绑定时进行文本大小写转换的功能。
            </remarks>
            <example>
            在XAML中使用：
            <code>
            &lt;TextBlock Text="{Binding SomeText, Converter={StaticResource TextCaseConverter}, ConverterParameter=upper}"/&gt;
            </code>
            </example>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.TextCaseConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将文本转换为指定的大小写格式
            </summary>
            <param name="value">要转换的文本值</param>
            <param name="targetType">目标类型（通常为string）</param>
            <param name="parameter">转换参数：upper（大写）、lower（小写）、title（标题格式）</param>
            <param name="culture">区域性信息</param>
            <returns>转换后的文本</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.TextCaseConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            反向转换（不支持）
            </summary>
            <param name="value">值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">参数</param>
            <param name="culture">区域性信息</param>
            <returns>抛出NotImplementedException</returns>
            <exception cref="T:System.NotImplementedException">此转换器不支持反向转换</exception>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.TextCaseConverter.CapitalizeFirstLetter(System.String,System.Globalization.CultureInfo)">
            <summary>
            将文本的首字母大写
            </summary>
            <param name="text">要处理的文本</param>
            <param name="culture">区域性信息</param>
            <returns>首字母大写的文本</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.ThemeToBrushConverter">
            <summary>
            主题到画刷转换器
            根据当前主题（深色/浅色）返回相应的画刷对象
            </summary>
            <remarks>
            支持的主题类型：
            - "Light": 浅色主题
            - "Dark": 深色主题
            - "Auto": 自动检测系统主题
            
            参数格式：
            - "Light=#FFFFFF,Dark=#000000": 指定浅色和深色主题的颜色
            - "Primary": 使用主色调的浅色/深色变体
            - "Secondary": 使用次要色调的浅色/深色变体
            - "Accent": 使用强调色的浅色/深色变体
            - "Background": 使用背景色的浅色/深色变体
            - "Text": 使用文本色的浅色/深色变体
            - "ResourceKey": 直接使用资源键获取画刷
            
            特殊选项：
            - "Freeze": 冻结画刷以提高性能
            - "Opacity=0.5": 设置画刷透明度
            </remarks>
            <example>
            在XAML中使用：
            <code>
            &lt;Rectangle Fill="{Binding CurrentTheme, Converter={StaticResource ThemeToBrushConverter}, ConverterParameter=Background}"/&gt;
            &lt;TextBlock Foreground="{Binding CurrentTheme, Converter={StaticResource ThemeToBrushConverter}, ConverterParameter=Text}"/&gt;
            &lt;Border Background="{Binding CurrentTheme, Converter={StaticResource ThemeToBrushConverter}, ConverterParameter=Primary,Opacity=0.1}"/&gt;
            </code>
            </example>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.ThemeToBrushConverter.DefaultLightBrush">
            <summary>
            默认浅色主题画刷
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.ThemeToBrushConverter.DefaultDarkBrush">
            <summary>
            默认深色主题画刷
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.ThemeToBrushConverter.FreezeBrushes">
            <summary>
            是否冻结创建的画刷
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ThemeToBrushConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将主题值转换为画刷
            </summary>
            <param name="value">主题值（字符串或ApplicationTheme枚举）</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">画刷参数</param>
            <param name="culture">文化信息</param>
            <returns>对应主题的画刷</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ThemeToBrushConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            反向转换（不支持）
            </summary>
            <param name="value">画刷值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数</param>
            <param name="culture">文化信息</param>
            <returns>不支持的操作异常</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ThemeToBrushConverter.DetermineIsDarkTheme(System.Object)">
            <summary>
            确定是否为深色主题
            </summary>
            <param name="themeValue">主题值</param>
            <returns>是否为深色主题</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ThemeToBrushConverter.IsSystemDarkTheme">
            <summary>
            检测系统是否使用深色主题
            </summary>
            <returns>系统是否使用深色主题</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ThemeToBrushConverter.ParseBrushParameter(System.String,System.Boolean)">
            <summary>
            解析画刷参数
            </summary>
            <param name="parameter">参数字符串</param>
            <param name="isDarkTheme">是否为深色主题</param>
            <returns>解析结果</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ThemeToBrushConverter.GetThemeBrush(System.String,System.Boolean)">
            <summary>
            从应用程序资源中获取主题画刷
            </summary>
            <param name="resourceKey">资源键</param>
            <param name="isDarkTheme">是否为深色主题</param>
            <returns>主题画刷</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ThemeToBrushConverter.TryParseColor(System.String,System.Windows.Media.Color@)">
            <summary>
            尝试解析颜色字符串
            </summary>
            <param name="colorString">颜色字符串</param>
            <param name="color">解析出的颜色</param>
            <returns>是否解析成功</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.ThemeToColorConverter">
            <summary>
            主题到颜色转换器
            根据当前主题（深色/浅色）返回相应的颜色值
            </summary>
            <remarks>
            支持的主题类型：
            - "Light": 浅色主题
            - "Dark": 深色主题
            - "Auto": 自动检测系统主题
            
            参数格式：
            - "Light=#FFFFFF,Dark=#000000": 指定浅色和深色主题的颜色
            - "Primary": 使用主色调的浅色/深色变体
            - "Secondary": 使用次要色调的浅色/深色变体
            - "Accent": 使用强调色的浅色/深色变体
            - "Background": 使用背景色的浅色/深色变体
            - "Text": 使用文本色的浅色/深色变体
            </remarks>
            <example>
            在XAML中使用：
            <code>
            &lt;Rectangle Fill="{Binding CurrentTheme, Converter={StaticResource ThemeToColorConverter}, ConverterParameter=Light=#FFFFFF,Dark=#1E1E1E}"/&gt;
            &lt;TextBlock Foreground="{Binding CurrentTheme, Converter={StaticResource ThemeToColorConverter}, ConverterParameter=Text}"/&gt;
            &lt;Border Background="{Binding CurrentTheme, Converter={StaticResource ThemeToColorConverter}, ConverterParameter=Primary}"/&gt;
            </code>
            </example>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.ThemeToColorConverter.DefaultLightColor">
            <summary>
            默认浅色主题颜色
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Converters.ThemeToColorConverter.DefaultDarkColor">
            <summary>
            默认深色主题颜色
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ThemeToColorConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将主题值转换为颜色
            </summary>
            <param name="value">主题值（字符串或ApplicationTheme枚举）</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">颜色参数</param>
            <param name="culture">文化信息</param>
            <returns>对应主题的颜色</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ThemeToColorConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            反向转换（不支持）
            </summary>
            <param name="value">颜色值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数</param>
            <param name="culture">文化信息</param>
            <returns>不支持的操作异常</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ThemeToColorConverter.DetermineIsDarkTheme(System.Object)">
            <summary>
            确定是否为深色主题
            </summary>
            <param name="themeValue">主题值</param>
            <returns>是否为深色主题</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ThemeToColorConverter.IsSystemDarkTheme">
            <summary>
            检测系统是否使用深色主题
            </summary>
            <returns>系统是否使用深色主题</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ThemeToColorConverter.ParseColorParameter(System.String)">
            <summary>
            解析颜色参数
            </summary>
            <param name="parameter">参数字符串</param>
            <returns>浅色和深色主题的颜色</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ThemeToColorConverter.GetThemeColor(System.String,System.Windows.Media.Color)">
            <summary>
            从应用程序资源中获取主题颜色
            </summary>
            <param name="resourceKey">资源键</param>
            <param name="fallbackColor">回退颜色</param>
            <returns>主题颜色</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ThemeToColorConverter.TryParseColor(System.String,System.Windows.Media.Color@)">
            <summary>
            尝试解析颜色字符串
            </summary>
            <param name="colorString">颜色字符串</param>
            <param name="color">解析出的颜色</param>
            <returns>是否解析成功</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.ApplicationTheme">
            <summary>
            应用程序主题枚举
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Converters.ApplicationTheme.Light">
            <summary>
            浅色主题
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Converters.ApplicationTheme.Dark">
            <summary>
            深色主题
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Converters.ApplicationTheme.Auto">
            <summary>
            自动检测系统主题
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.ThicknessConverter">
            <summary>
            厚度转换器
            将数值转换为Thickness对象，支持多种输入格式
            </summary>
            <remarks>
            支持的转换格式：
            - 单个数值 -> 四边相等的Thickness
            - "left,top,right,bottom" -> 指定四边的Thickness
            - "horizontal,vertical" -> 水平和垂直方向的Thickness
            - 参数可以指定应用方向："Left", "Top", "Right", "Bottom", "Horizontal", "Vertical", "All"
            </remarks>
            <example>
            在XAML中使用：
            <code>
            &lt;Border BorderThickness="{Binding BorderSize, Converter={StaticResource ThicknessConverter}}"/&gt;
            &lt;Border Margin="{Binding Spacing, Converter={StaticResource ThicknessConverter}, ConverterParameter=Horizontal}"/&gt;
            &lt;Border Padding="{Binding Padding, Converter={StaticResource ThicknessConverter}, ConverterParameter=Left}"/&gt;
            </code>
            </example>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ThicknessConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将数值或字符串转换为Thickness
            </summary>
            <param name="value">数值或字符串</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">应用方向参数</param>
            <param name="culture">文化信息</param>
            <returns>Thickness对象</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ThicknessConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将Thickness转换回数值或字符串
            </summary>
            <param name="value">Thickness对象</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">转换参数</param>
            <param name="culture">文化信息</param>
            <returns>数值或字符串</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ThicknessConverter.ParseThicknessFromString(System.String,System.String,System.Globalization.CultureInfo)">
            <summary>
            从字符串解析Thickness
            </summary>
            <param name="input">输入字符串</param>
            <param name="direction">应用方向</param>
            <param name="culture">文化信息</param>
            <returns>Thickness对象</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ThicknessConverter.CreateThicknessFromValue(System.Double,System.String)">
            <summary>
            根据单个数值和方向创建Thickness
            </summary>
            <param name="value">数值</param>
            <param name="direction">应用方向</param>
            <returns>Thickness对象</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ThicknessConverter.TryConvertToDouble(System.Object,System.Double@)">
            <summary>
            尝试将对象转换为double
            </summary>
            <param name="value">要转换的值</param>
            <param name="result">转换结果</param>
            <returns>是否转换成功</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.ThicknessConverter.ConvertToTargetType(System.Double,System.Type)">
            <summary>
            将double值转换为目标类型
            </summary>
            <param name="value">double值</param>
            <param name="targetType">目标类型</param>
            <returns>转换后的值</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Effects.Helpers.EffectAttachedProperties">
            <summary>
            提供效果相关的附加属性，用于在XAML中方便地应用视觉效果
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Effects.Helpers.EffectAttachedProperties.ElevationProperty">
            <summary>
            Elevation 附加属性
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Effects.Helpers.EffectAttachedProperties.GetElevation(System.Windows.DependencyObject)">
            <summary>
            获取元素的高度级别
            </summary>
            <param name="obj">目标元素</param>
            <returns>高度级别</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Effects.Helpers.EffectAttachedProperties.SetElevation(System.Windows.DependencyObject,System.Int32)">
            <summary>
            设置元素的高度级别
            </summary>
            <param name="obj">目标元素</param>
            <param name="value">高度级别</param>
        </member>
        <member name="F:FluentSystemDesign.WPF.Effects.Helpers.EffectAttachedProperties.IsDarkThemeProperty">
            <summary>
            IsDarkTheme 附加属性
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Effects.Helpers.EffectAttachedProperties.GetIsDarkTheme(System.Windows.DependencyObject)">
            <summary>
            获取是否为深色主题
            </summary>
            <param name="obj">目标元素</param>
            <returns>是否为深色主题</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Effects.Helpers.EffectAttachedProperties.SetIsDarkTheme(System.Windows.DependencyObject,System.Boolean)">
            <summary>
            设置是否为深色主题
            </summary>
            <param name="obj">目标元素</param>
            <param name="value">是否为深色主题</param>
        </member>
        <member name="F:FluentSystemDesign.WPF.Effects.Helpers.EffectAttachedProperties.GlowColorProperty">
            <summary>
            GlowColor 附加属性
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Effects.Helpers.EffectAttachedProperties.GetGlowColor(System.Windows.DependencyObject)">
            <summary>
            获取发光颜色
            </summary>
            <param name="obj">目标元素</param>
            <returns>发光颜色</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Effects.Helpers.EffectAttachedProperties.SetGlowColor(System.Windows.DependencyObject,System.Nullable{System.Windows.Media.Color})">
            <summary>
            设置发光颜色
            </summary>
            <param name="obj">目标元素</param>
            <param name="value">发光颜色</param>
        </member>
        <member name="F:FluentSystemDesign.WPF.Effects.Helpers.EffectAttachedProperties.GlowIntensityProperty">
            <summary>
            GlowIntensity 附加属性
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Effects.Helpers.EffectAttachedProperties.GetGlowIntensity(System.Windows.DependencyObject)">
            <summary>
            获取发光强度
            </summary>
            <param name="obj">目标元素</param>
            <returns>发光强度</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Effects.Helpers.EffectAttachedProperties.SetGlowIntensity(System.Windows.DependencyObject,System.Double)">
            <summary>
            设置发光强度
            </summary>
            <param name="obj">目标元素</param>
            <param name="value">发光强度</param>
        </member>
        <member name="F:FluentSystemDesign.WPF.Effects.Helpers.EffectAttachedProperties.GlowRadiusProperty">
            <summary>
            GlowRadius 附加属性
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Effects.Helpers.EffectAttachedProperties.GetGlowRadius(System.Windows.DependencyObject)">
            <summary>
            获取发光半径
            </summary>
            <param name="obj">目标元素</param>
            <returns>发光半径</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Effects.Helpers.EffectAttachedProperties.SetGlowRadius(System.Windows.DependencyObject,System.Double)">
            <summary>
            设置发光半径
            </summary>
            <param name="obj">目标元素</param>
            <param name="value">发光半径</param>
        </member>
        <member name="F:FluentSystemDesign.WPF.Effects.Helpers.EffectAttachedProperties.BlurRadiusProperty">
            <summary>
            BlurRadius 附加属性
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Effects.Helpers.EffectAttachedProperties.GetBlurRadius(System.Windows.DependencyObject)">
            <summary>
            获取模糊半径
            </summary>
            <param name="obj">目标元素</param>
            <returns>模糊半径</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Effects.Helpers.EffectAttachedProperties.SetBlurRadius(System.Windows.DependencyObject,System.Double)">
            <summary>
            设置模糊半径
            </summary>
            <param name="obj">目标元素</param>
            <param name="value">模糊半径</param>
        </member>
        <member name="T:FluentSystemDesign.WPF.Effects.Helpers.EffectHelper">
            <summary>
            提供视觉效果的辅助方法和动态应用功能
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Effects.Helpers.EffectHelper.CreateElevationShadow(System.Int32,System.Boolean)">
            <summary>
            创建指定高度的阴影效果
            </summary>
            <param name="elevation">高度级别 (0-24)</param>
            <param name="isDarkTheme">是否为深色主题</param>
            <returns>阴影效果</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Effects.Helpers.EffectHelper.CreateCustomShadow(System.Double,System.Double,System.Double,System.Windows.Media.Color)">
            <summary>
            创建自定义阴影效果
            </summary>
            <param name="depth">阴影深度</param>
            <param name="blurRadius">模糊半径</param>
            <param name="opacity">不透明度</param>
            <param name="color">阴影颜色</param>
            <returns>阴影效果</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Effects.Helpers.EffectHelper.CreateGlowEffect(System.Windows.Media.Color,System.Double,System.Double)">
            <summary>
            创建发光效果
            </summary>
            <param name="color">发光颜色</param>
            <param name="intensity">发光强度 (0-1)</param>
            <param name="radius">发光半径</param>
            <returns>发光效果</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Effects.Helpers.EffectHelper.CreateThemeGlow(FluentSystemDesign.WPF.Effects.Helpers.ThemeColorType,System.Double,System.Double)">
            <summary>
            创建主题色发光效果
            </summary>
            <param name="colorType">颜色类型</param>
            <param name="intensity">发光强度</param>
            <param name="radius">发光半径</param>
            <returns>发光效果</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Effects.Helpers.EffectHelper.CreateBlurEffect(System.Double,System.Windows.Media.Effects.KernelType)">
            <summary>
            创建模糊效果
            </summary>
            <param name="radius">模糊半径</param>
            <param name="kernelType">模糊核心类型</param>
            <returns>模糊效果</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Effects.Helpers.EffectHelper.CreateBlurEffect(FluentSystemDesign.WPF.Effects.Helpers.BlurIntensity)">
            <summary>
            创建预定义强度的模糊效果
            </summary>
            <param name="intensity">模糊强度</param>
            <returns>模糊效果</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Effects.Helpers.EffectHelper.CreateLinearGradient(System.Windows.Media.Color,System.Windows.Media.Color,System.Double)">
            <summary>
            创建线性渐变画刷
            </summary>
            <param name="startColor">起始颜色</param>
            <param name="endColor">结束颜色</param>
            <param name="angle">渐变角度</param>
            <returns>线性渐变画刷</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Effects.Helpers.EffectHelper.CreateRadialGradient(System.Windows.Media.Color,System.Windows.Media.Color,System.Nullable{System.Windows.Point},System.Double,System.Double)">
            <summary>
            创建径向渐变画刷
            </summary>
            <param name="centerColor">中心颜色</param>
            <param name="edgeColor">边缘颜色</param>
            <param name="center">中心点</param>
            <param name="radiusX">X轴半径</param>
            <param name="radiusY">Y轴半径</param>
            <returns>径向渐变画刷</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Effects.Helpers.EffectHelper.GetShadowData(System.Int32)">
            <summary>
            获取阴影数据
            </summary>
            <param name="elevation">高度级别</param>
            <returns>阴影数据</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Effects.Helpers.EffectHelper.GetThemeColor(FluentSystemDesign.WPF.Effects.Helpers.ThemeColorType)">
            <summary>
            获取主题颜色
            </summary>
            <param name="colorType">颜色类型</param>
            <returns>颜色</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Effects.Helpers.ThemeColorType">
            <summary>
            主题颜色类型
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Effects.Helpers.BlurIntensity">
            <summary>
            模糊强度
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Helpers.ThemeManager">
            <summary>
            主题管理器，用于管理应用程序的主题切换
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Helpers.ThemeManager.ThemeType">
            <summary>
            主题类型枚举
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Helpers.ThemeManager.ThemeType.Light">
            <summary>
            浅色主题
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Helpers.ThemeManager.ThemeType.Dark">
            <summary>
            深色主题
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Helpers.ThemeManager.CurrentTheme">
            <summary>
            当前主题类型
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Helpers.ThemeManager.ThemeChanged">
            <summary>
            主题变更事件
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Helpers.ThemeManager.ApplyTheme(System.Windows.Application,FluentSystemDesign.WPF.Helpers.ThemeManager.ThemeType)">
            <summary>
            应用主题到指定的应用程序
            </summary>
            <param name="app">目标应用程序</param>
            <param name="theme">要应用的主题类型</param>
        </member>
        <member name="M:FluentSystemDesign.WPF.Helpers.ThemeManager.ToggleTheme(System.Windows.Application)">
            <summary>
            切换主题（在浅色和深色之间切换）
            </summary>
            <param name="app">目标应用程序</param>
        </member>
        <member name="M:FluentSystemDesign.WPF.Helpers.ThemeManager.GetSystemTheme">
            <summary>
            获取系统主题偏好
            </summary>
            <returns>系统推荐的主题类型</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Helpers.ThemeManager.RemoveThemeResources(System.Windows.Application)">
            <summary>
            移除主题资源
            </summary>
            <param name="app">目标应用程序</param>
        </member>
        <member name="M:FluentSystemDesign.WPF.Helpers.ThemeManager.AddThemeResources(System.Windows.Application,FluentSystemDesign.WPF.Helpers.ThemeManager.ThemeType)">
            <summary>
            添加主题资源
            </summary>
            <param name="app">目标应用程序</param>
            <param name="theme">主题类型</param>
        </member>
        <member name="T:FluentSystemDesign.WPF.Helpers.ThemeChangedEventArgs">
            <summary>
            主题变更事件参数
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Helpers.ThemeChangedEventArgs.OldTheme">
            <summary>
            旧主题
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Helpers.ThemeChangedEventArgs.NewTheme">
            <summary>
            新主题
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Helpers.ThemeChangedEventArgs.#ctor(FluentSystemDesign.WPF.Helpers.ThemeManager.ThemeType,FluentSystemDesign.WPF.Helpers.ThemeManager.ThemeType)">
            <summary>
            构造函数
            </summary>
            <param name="oldTheme">旧主题</param>
            <param name="newTheme">新主题</param>
        </member>
    </members>
</doc>
