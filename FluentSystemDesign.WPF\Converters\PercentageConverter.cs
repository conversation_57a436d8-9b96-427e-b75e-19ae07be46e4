using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace FluentSystemDesign.WPF.Converters
{
    /// <summary>
    /// 百分比转换器
    /// 将0-1之间的小数值转换为百分比字符串，或将百分比字符串转换回小数值
    /// </summary>
    /// <remarks>
    /// 默认行为：0.5 -> "50%", 0.75 -> "75%"
    /// 支持自定义小数位数，通过参数指定，例如："2"表示保留2位小数
    /// 支持不同的输入范围：
    /// - 默认：0-1范围（0.5 = 50%）
    /// - 参数"0-100"：0-100范围（50 = 50%）
    /// </remarks>
    /// <example>
    /// 在XAML中使用：
    /// <code>
    /// &lt;TextBlock Text="{Binding Progress, Converter={StaticResource PercentageConverter}}"/&gt;
    /// &lt;TextBlock Text="{Binding Progress, Converter={StaticResource PercentageConverter}, ConverterParameter=1}"/&gt;
    /// &lt;TextBlock Text="{Binding Score, Converter={StaticResource PercentageConverter}, ConverterParameter=0-100}"/&gt;
    /// </code>
    /// </example>
    [ValueConversion(typeof(double), typeof(string))]
    [ValueConversion(typeof(float), typeof(string))]
    [ValueConversion(typeof(decimal), typeof(string))]
    public class PercentageConverter : IValueConverter
    {
        /// <summary>
        /// 默认小数位数
        /// </summary>
        public int DefaultDecimalPlaces { get; set; } = 0;

        /// <summary>
        /// 默认输入范围（true: 0-1, false: 0-100）
        /// </summary>
        public bool DefaultIsZeroToOneRange { get; set; } = true;

        /// <summary>
        /// 将数值转换为百分比字符串
        /// </summary>
        /// <param name="value">数值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数：小数位数或"0-100"表示输入范围</param>
        /// <param name="culture">文化信息</param>
        /// <returns>百分比字符串</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
            {
                return string.Empty;
            }

            if (!TryConvertToDouble(value, out double numericValue))
            {
                return DependencyProperty.UnsetValue;
            }

            var decimalPlaces = DefaultDecimalPlaces;
            var isZeroToOneRange = DefaultIsZeroToOneRange;

            // 解析参数
            if (parameter is string parameterString && !string.IsNullOrEmpty(parameterString))
            {
                if (parameterString.Contains("0-100"))
                {
                    isZeroToOneRange = false;
                    // 移除范围标识，检查是否还有小数位数
                    parameterString = parameterString.Replace("0-100", "").Trim();
                }

                if (!string.IsNullOrEmpty(parameterString) && int.TryParse(parameterString, out int customDecimalPlaces))
                {
                    decimalPlaces = Math.Max(0, Math.Min(10, customDecimalPlaces)); // 限制在0-10之间
                }
            }

            // 转换为百分比值
            double percentageValue = isZeroToOneRange ? numericValue * 100 : numericValue;

            // 格式化字符串
            var format = decimalPlaces > 0 ? $"F{decimalPlaces}" : "F0";
            var formattedValue = percentageValue.ToString(format, culture);

            return $"{formattedValue}{culture.NumberFormat.PercentSymbol}";
        }

        /// <summary>
        /// 将百分比字符串转换为数值
        /// </summary>
        /// <param name="value">百分比字符串</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>数值</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not string stringValue || string.IsNullOrWhiteSpace(stringValue))
            {
                return DependencyProperty.UnsetValue;
            }

            var isZeroToOneRange = DefaultIsZeroToOneRange;

            // 解析参数
            if (parameter is string parameterString && !string.IsNullOrEmpty(parameterString))
            {
                if (parameterString.Contains("0-100"))
                {
                    isZeroToOneRange = false;
                }
            }

            // 移除百分号和空格
            var cleanString = stringValue.Replace(culture.NumberFormat.PercentSymbol, "").Trim();

            if (!double.TryParse(cleanString, NumberStyles.Float, culture, out double percentageValue))
            {
                return DependencyProperty.UnsetValue;
            }

            // 转换回原始范围
            double result = isZeroToOneRange ? percentageValue / 100 : percentageValue;

            // 转换为目标类型
            try
            {
                return targetType switch
                {
                    Type t when t == typeof(double) || t == typeof(double?) => result,
                    Type t when t == typeof(float) || t == typeof(float?) => (float)result,
                    Type t when t == typeof(decimal) || t == typeof(decimal?) => (decimal)result,
                    _ => result
                };
            }
            catch (OverflowException)
            {
                return DependencyProperty.UnsetValue;
            }
        }

        /// <summary>
        /// 尝试将对象转换为double
        /// </summary>
        /// <param name="value">要转换的值</param>
        /// <param name="result">转换结果</param>
        /// <returns>是否转换成功</returns>
        private static bool TryConvertToDouble(object value, out double result)
        {
            result = 0;

            return value switch
            {
                double d => (result = d) == d,
                float f => (result = f) == f,
                decimal dec => (result = (double)dec) == (double)dec,
                int i => (result = i) == i,
                long l => (result = l) == l,
                short s => (result = s) == s,
                byte b => (result = b) == b,
                uint ui => (result = ui) == ui,
                ulong ul => (result = ul) == ul,
                ushort us => (result = us) == us,
                sbyte sb => (result = sb) == sb,
                string str => double.TryParse(str, out result),
                _ => false
            };
        }
    }
}
