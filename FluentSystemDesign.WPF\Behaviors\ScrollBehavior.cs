using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media.Animation;
using Microsoft.Xaml.Behaviors;

namespace FluentSystemDesign.WPF.Behaviors
{
    /// <summary>
    /// 滚动行为，为ScrollViewer提供增强的滚动功能
    /// </summary>
    public class ScrollBehavior : Behavior<ScrollViewer>
    {
        #region 依赖属性

        /// <summary>
        /// 是否启用平滑滚动
        /// </summary>
        public static readonly DependencyProperty IsSmoothScrollEnabledProperty =
            DependencyProperty.Register(nameof(IsSmoothScrollEnabled), typeof(bool), typeof(ScrollBehavior),
                new PropertyMetadata(true));

        /// <summary>
        /// 滚动动画持续时间
        /// </summary>
        public static readonly DependencyProperty ScrollDurationProperty =
            DependencyProperty.Register(nameof(ScrollDuration), typeof(TimeSpan), typeof(ScrollBehavior),
                new PropertyMetadata(TimeSpan.FromMilliseconds(300)));

        /// <summary>
        /// 滚动缓动函数
        /// </summary>
        public static readonly DependencyProperty ScrollEasingFunctionProperty =
            DependencyProperty.Register(nameof(ScrollEasingFunction), typeof(IEasingFunction), typeof(ScrollBehavior),
                new PropertyMetadata(new QuadraticEase { EasingMode = EasingMode.EaseOut }));

        /// <summary>
        /// 鼠标滚轮滚动步长
        /// </summary>
        public static readonly DependencyProperty MouseWheelStepProperty =
            DependencyProperty.Register(nameof(MouseWheelStep), typeof(double), typeof(ScrollBehavior),
                new PropertyMetadata(48.0));

        /// <summary>
        /// 是否启用自动隐藏滚动条
        /// </summary>
        public static readonly DependencyProperty AutoHideScrollBarProperty =
            DependencyProperty.Register(nameof(AutoHideScrollBar), typeof(bool), typeof(ScrollBehavior),
                new PropertyMetadata(false));

        /// <summary>
        /// 滚动条自动隐藏延迟时间
        /// </summary>
        public static readonly DependencyProperty ScrollBarHideDelayProperty =
            DependencyProperty.Register(nameof(ScrollBarHideDelay), typeof(TimeSpan), typeof(ScrollBehavior),
                new PropertyMetadata(TimeSpan.FromSeconds(2)));

        /// <summary>
        /// 是否启用边界反弹效果
        /// </summary>
        public static readonly DependencyProperty EnableBounceEffectProperty =
            DependencyProperty.Register(nameof(EnableBounceEffect), typeof(bool), typeof(ScrollBehavior),
                new PropertyMetadata(false));

        /// <summary>
        /// 反弹强度
        /// </summary>
        public static readonly DependencyProperty BounceIntensityProperty =
            DependencyProperty.Register(nameof(BounceIntensity), typeof(double), typeof(ScrollBehavior),
                new PropertyMetadata(0.1));

        #endregion

        #region 属性

        /// <summary>
        /// 获取或设置是否启用平滑滚动
        /// </summary>
        public bool IsSmoothScrollEnabled
        {
            get => (bool)GetValue(IsSmoothScrollEnabledProperty);
            set => SetValue(IsSmoothScrollEnabledProperty, value);
        }

        /// <summary>
        /// 获取或设置滚动动画持续时间
        /// </summary>
        public TimeSpan ScrollDuration
        {
            get => (TimeSpan)GetValue(ScrollDurationProperty);
            set => SetValue(ScrollDurationProperty, value);
        }

        /// <summary>
        /// 获取或设置滚动缓动函数
        /// </summary>
        public IEasingFunction ScrollEasingFunction
        {
            get => (IEasingFunction)GetValue(ScrollEasingFunctionProperty);
            set => SetValue(ScrollEasingFunctionProperty, value);
        }

        /// <summary>
        /// 获取或设置鼠标滚轮滚动步长
        /// </summary>
        public double MouseWheelStep
        {
            get => (double)GetValue(MouseWheelStepProperty);
            set => SetValue(MouseWheelStepProperty, value);
        }

        /// <summary>
        /// 获取或设置是否启用自动隐藏滚动条
        /// </summary>
        public bool AutoHideScrollBar
        {
            get => (bool)GetValue(AutoHideScrollBarProperty);
            set => SetValue(AutoHideScrollBarProperty, value);
        }

        /// <summary>
        /// 获取或设置滚动条自动隐藏延迟时间
        /// </summary>
        public TimeSpan ScrollBarHideDelay
        {
            get => (TimeSpan)GetValue(ScrollBarHideDelayProperty);
            set => SetValue(ScrollBarHideDelayProperty, value);
        }

        /// <summary>
        /// 获取或设置是否启用边界反弹效果
        /// </summary>
        public bool EnableBounceEffect
        {
            get => (bool)GetValue(EnableBounceEffectProperty);
            set => SetValue(EnableBounceEffectProperty, value);
        }

        /// <summary>
        /// 获取或设置反弹强度
        /// </summary>
        public double BounceIntensity
        {
            get => (double)GetValue(BounceIntensityProperty);
            set => SetValue(BounceIntensityProperty, value);
        }

        #endregion

        #region 事件

        /// <summary>
        /// 滚动开始事件
        /// </summary>
        public event EventHandler<ScrollEventArgs> ScrollStarted;

        /// <summary>
        /// 滚动结束事件
        /// </summary>
        public event EventHandler<ScrollEventArgs> ScrollCompleted;

        /// <summary>
        /// 到达顶部事件
        /// </summary>
        public event EventHandler ReachedTop;

        /// <summary>
        /// 到达底部事件
        /// </summary>
        public event EventHandler ReachedBottom;

        #endregion

        #region 私有字段

        private System.Windows.Threading.DispatcherTimer _hideScrollBarTimer;
        private bool _isScrolling;

        #endregion

        #region 重写方法

        /// <summary>
        /// 附加到关联对象时调用
        /// </summary>
        protected override void OnAttached()
        {
            base.OnAttached();
            
            if (AssociatedObject != null)
            {
                AssociatedObject.PreviewMouseWheel += OnPreviewMouseWheel;
                AssociatedObject.ScrollChanged += OnScrollChanged;
                AssociatedObject.MouseEnter += OnMouseEnter;
                AssociatedObject.MouseLeave += OnMouseLeave;
                
                InitializeScrollBarTimer();
                SetupScrollBarVisibility();
            }
        }

        /// <summary>
        /// 从关联对象分离时调用
        /// </summary>
        protected override void OnDetaching()
        {
            if (AssociatedObject != null)
            {
                AssociatedObject.PreviewMouseWheel -= OnPreviewMouseWheel;
                AssociatedObject.ScrollChanged -= OnScrollChanged;
                AssociatedObject.MouseEnter -= OnMouseEnter;
                AssociatedObject.MouseLeave -= OnMouseLeave;
            }
            
            _hideScrollBarTimer?.Stop();
            
            base.OnDetaching();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 平滑滚动到指定垂直位置
        /// </summary>
        /// <param name="offset">垂直偏移量</param>
        public void ScrollToVerticalOffset(double offset)
        {
            if (AssociatedObject == null)
                return;

            if (IsSmoothScrollEnabled)
            {
                AnimateScrollToVerticalOffset(offset);
            }
            else
            {
                AssociatedObject.ScrollToVerticalOffset(offset);
            }
        }

        /// <summary>
        /// 平滑滚动到指定水平位置
        /// </summary>
        /// <param name="offset">水平偏移量</param>
        public void ScrollToHorizontalOffset(double offset)
        {
            if (AssociatedObject == null)
                return;

            if (IsSmoothScrollEnabled)
            {
                AnimateScrollToHorizontalOffset(offset);
            }
            else
            {
                AssociatedObject.ScrollToHorizontalOffset(offset);
            }
        }

        /// <summary>
        /// 滚动到顶部
        /// </summary>
        public void ScrollToTop()
        {
            ScrollToVerticalOffset(0);
        }

        /// <summary>
        /// 滚动到底部
        /// </summary>
        public void ScrollToBottom()
        {
            if (AssociatedObject != null)
            {
                ScrollToVerticalOffset(AssociatedObject.ScrollableHeight);
            }
        }

        /// <summary>
        /// 滚动到左侧
        /// </summary>
        public void ScrollToLeft()
        {
            ScrollToHorizontalOffset(0);
        }

        /// <summary>
        /// 滚动到右侧
        /// </summary>
        public void ScrollToRight()
        {
            if (AssociatedObject != null)
            {
                ScrollToHorizontalOffset(AssociatedObject.ScrollableWidth);
            }
        }

        #endregion

        #region 事件处理

        private void OnPreviewMouseWheel(object sender, MouseWheelEventArgs e)
        {
            if (!IsSmoothScrollEnabled)
                return;

            e.Handled = true;
            
            var delta = e.Delta > 0 ? -MouseWheelStep : MouseWheelStep;
            var newOffset = Math.Max(0, Math.Min(AssociatedObject.ScrollableHeight, 
                                                AssociatedObject.VerticalOffset + delta));
            
            ScrollToVerticalOffset(newOffset);
        }

        private void OnScrollChanged(object sender, ScrollChangedEventArgs e)
        {
            // 检查是否到达边界
            if (e.VerticalOffset == 0)
            {
                ReachedTop?.Invoke(this, EventArgs.Empty);
            }
            else if (e.VerticalOffset >= AssociatedObject.ScrollableHeight)
            {
                ReachedBottom?.Invoke(this, EventArgs.Empty);
            }

            // 处理边界反弹效果
            if (EnableBounceEffect)
            {
                HandleBounceEffect(e);
            }

            // 重置滚动条隐藏计时器
            if (AutoHideScrollBar)
            {
                ResetScrollBarTimer();
            }
        }

        private void OnMouseEnter(object sender, MouseEventArgs e)
        {
            if (AutoHideScrollBar)
            {
                ShowScrollBars();
                _hideScrollBarTimer?.Stop();
            }
        }

        private void OnMouseLeave(object sender, MouseEventArgs e)
        {
            if (AutoHideScrollBar)
            {
                ResetScrollBarTimer();
            }
        }

        #endregion

        #region 私有方法

        private void AnimateScrollToVerticalOffset(double offset)
        {
            if (_isScrolling)
                return;

            _isScrolling = true;
            ScrollStarted?.Invoke(this, new ScrollEventArgs(AssociatedObject.VerticalOffset, offset, ScrollDirection.Vertical));

            var animation = new DoubleAnimation
            {
                From = AssociatedObject.VerticalOffset,
                To = offset,
                Duration = ScrollDuration,
                EasingFunction = ScrollEasingFunction
            };

            animation.Completed += (s, e) =>
            {
                _isScrolling = false;
                ScrollCompleted?.Invoke(this, new ScrollEventArgs(AssociatedObject.VerticalOffset, offset, ScrollDirection.Vertical));
            };

            var storyboard = new Storyboard();
            storyboard.Children.Add(animation);
            Storyboard.SetTarget(animation, AssociatedObject);
            Storyboard.SetTargetProperty(animation, new PropertyPath("(ScrollViewer.VerticalOffset)"));
            
            storyboard.Begin();
        }

        private void AnimateScrollToHorizontalOffset(double offset)
        {
            if (_isScrolling)
                return;

            _isScrolling = true;
            ScrollStarted?.Invoke(this, new ScrollEventArgs(AssociatedObject.HorizontalOffset, offset, ScrollDirection.Horizontal));

            var animation = new DoubleAnimation
            {
                From = AssociatedObject.HorizontalOffset,
                To = offset,
                Duration = ScrollDuration,
                EasingFunction = ScrollEasingFunction
            };

            animation.Completed += (s, e) =>
            {
                _isScrolling = false;
                ScrollCompleted?.Invoke(this, new ScrollEventArgs(AssociatedObject.HorizontalOffset, offset, ScrollDirection.Horizontal));
            };

            var storyboard = new Storyboard();
            storyboard.Children.Add(animation);
            Storyboard.SetTarget(animation, AssociatedObject);
            Storyboard.SetTargetProperty(animation, new PropertyPath("(ScrollViewer.HorizontalOffset)"));
            
            storyboard.Begin();
        }

        private void HandleBounceEffect(ScrollChangedEventArgs e)
        {
            // 实现边界反弹效果的逻辑
            // 这里可以添加更复杂的反弹动画
        }

        private void InitializeScrollBarTimer()
        {
            _hideScrollBarTimer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = ScrollBarHideDelay
            };
            _hideScrollBarTimer.Tick += (s, e) =>
            {
                _hideScrollBarTimer.Stop();
                HideScrollBars();
            };
        }

        private void SetupScrollBarVisibility()
        {
            if (AutoHideScrollBar)
            {
                HideScrollBars();
            }
        }

        private void ResetScrollBarTimer()
        {
            ShowScrollBars();
            _hideScrollBarTimer?.Stop();
            _hideScrollBarTimer?.Start();
        }

        private void ShowScrollBars()
        {
            if (AssociatedObject != null)
            {
                AssociatedObject.VerticalScrollBarVisibility = ScrollBarVisibility.Auto;
                AssociatedObject.HorizontalScrollBarVisibility = ScrollBarVisibility.Auto;
            }
        }

        private void HideScrollBars()
        {
            if (AssociatedObject != null)
            {
                AssociatedObject.VerticalScrollBarVisibility = ScrollBarVisibility.Hidden;
                AssociatedObject.HorizontalScrollBarVisibility = ScrollBarVisibility.Hidden;
            }
        }

        #endregion
    }

    /// <summary>
    /// 滚动事件参数
    /// </summary>
    public class ScrollEventArgs : EventArgs
    {
        /// <summary>
        /// 起始位置
        /// </summary>
        public double FromOffset { get; }

        /// <summary>
        /// 目标位置
        /// </summary>
        public double ToOffset { get; }

        /// <summary>
        /// 滚动方向
        /// </summary>
        public ScrollDirection Direction { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="fromOffset">起始位置</param>
        /// <param name="toOffset">目标位置</param>
        /// <param name="direction">滚动方向</param>
        public ScrollEventArgs(double fromOffset, double toOffset, ScrollDirection direction)
        {
            FromOffset = fromOffset;
            ToOffset = toOffset;
            Direction = direction;
        }
    }

    /// <summary>
    /// 滚动方向枚举
    /// </summary>
    public enum ScrollDirection
    {
        /// <summary>
        /// 垂直滚动
        /// </summary>
        Vertical,
        
        /// <summary>
        /// 水平滚动
        /// </summary>
        Horizontal
    }
}
