using System.Windows;
using Microsoft.Xaml.Behaviors;

namespace FluentSystemDesign.WPF.Behaviors
{
    /// <summary>
    /// 行为扩展方法，提供便捷的行为附加方式
    /// </summary>
    public static class BehaviorExtensions
    {
        #region DragBehavior 扩展

        /// <summary>
        /// 为元素添加拖拽行为
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="dragData">拖拽数据</param>
        /// <param name="dragEffects">拖拽效果</param>
        /// <returns>拖拽行为实例</returns>
        public static DragBehavior AddDragBehavior(this FrameworkElement element, object dragData = null, 
            DragDropEffects dragEffects = DragDropEffects.Move)
        {
            var behavior = new DragBehavior
            {
                DragData = dragData,
                DragEffects = dragEffects
            };
            
            Interaction.GetBehaviors(element).Add(behavior);
            return behavior;
        }

        #endregion

        #region AnimationBehavior 扩展

        /// <summary>
        /// 为元素添加动画行为
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="animationType">动画类型</param>
        /// <param name="trigger">触发器</param>
        /// <param name="autoStart">是否自动开始</param>
        /// <returns>动画行为实例</returns>
        public static AnimationBehavior AddAnimationBehavior(this FrameworkElement element, 
            AnimationType animationType = AnimationType.FadeIn,
            AnimationTrigger trigger = AnimationTrigger.Loaded,
            bool autoStart = true)
        {
            var behavior = new AnimationBehavior
            {
                AnimationType = animationType,
                Trigger = trigger,
                AutoStart = autoStart
            };
            
            Interaction.GetBehaviors(element).Add(behavior);
            return behavior;
        }

        /// <summary>
        /// 为元素添加淡入动画
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="autoStart">是否自动开始</param>
        /// <returns>动画行为实例</returns>
        public static AnimationBehavior AddFadeInAnimation(this FrameworkElement element, bool autoStart = true)
        {
            return element.AddAnimationBehavior(AnimationType.FadeIn, AnimationTrigger.Loaded, autoStart);
        }

        /// <summary>
        /// 为元素添加滑入动画
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="direction">滑入方向</param>
        /// <param name="autoStart">是否自动开始</param>
        /// <returns>动画行为实例</returns>
        public static AnimationBehavior AddSlideInAnimation(this FrameworkElement element, 
            SlideDirection direction = SlideDirection.Left, bool autoStart = true)
        {
            var animationType = direction switch
            {
                SlideDirection.Left => AnimationType.SlideInFromLeft,
                SlideDirection.Right => AnimationType.SlideInFromRight,
                SlideDirection.Top => AnimationType.SlideInFromTop,
                SlideDirection.Bottom => AnimationType.SlideInFromBottom,
                _ => AnimationType.SlideInFromLeft
            };
            
            return element.AddAnimationBehavior(animationType, AnimationTrigger.Loaded, autoStart);
        }

        #endregion

        #region FocusManagementBehavior 扩展

        /// <summary>
        /// 为元素添加焦点管理行为
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="focusOnLoad">是否在加载时获取焦点</param>
        /// <param name="selectAllOnFocus">是否在获取焦点时选择所有文本</param>
        /// <returns>焦点管理行为实例</returns>
        public static FocusManagementBehavior AddFocusManagementBehavior(this FrameworkElement element,
            bool focusOnLoad = false, bool selectAllOnFocus = false)
        {
            var behavior = new FocusManagementBehavior
            {
                FocusOnLoad = focusOnLoad,
                SelectAllOnFocus = selectAllOnFocus
            };
            
            Interaction.GetBehaviors(element).Add(behavior);
            return behavior;
        }

        #endregion

        #region ValidationBehavior 扩展

        /// <summary>
        /// 为元素添加验证行为
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="validationType">验证类型</param>
        /// <param name="isRequired">是否必填</param>
        /// <param name="errorMessage">错误消息</param>
        /// <returns>验证行为实例</returns>
        public static ValidationBehavior AddValidationBehavior(this FrameworkElement element,
            ValidationType validationType = ValidationType.None,
            bool isRequired = false,
            string errorMessage = "")
        {
            var behavior = new ValidationBehavior
            {
                ValidationType = validationType,
                IsRequired = isRequired,
                ErrorMessage = errorMessage
            };
            
            Interaction.GetBehaviors(element).Add(behavior);
            return behavior;
        }

        /// <summary>
        /// 为元素添加邮箱验证
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="isRequired">是否必填</param>
        /// <returns>验证行为实例</returns>
        public static ValidationBehavior AddEmailValidation(this FrameworkElement element, bool isRequired = false)
        {
            return element.AddValidationBehavior(ValidationType.Email, isRequired, "请输入有效的邮箱地址");
        }

        /// <summary>
        /// 为元素添加手机号验证
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="isRequired">是否必填</param>
        /// <returns>验证行为实例</returns>
        public static ValidationBehavior AddPhoneValidation(this FrameworkElement element, bool isRequired = false)
        {
            return element.AddValidationBehavior(ValidationType.Phone, isRequired, "请输入有效的手机号码");
        }

        #endregion

        #region WatermarkBehavior 扩展

        /// <summary>
        /// 为元素添加水印行为
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="watermarkText">水印文本</param>
        /// <param name="hideOnFocus">是否在获得焦点时隐藏</param>
        /// <returns>水印行为实例</returns>
        public static WatermarkBehavior AddWatermarkBehavior(this FrameworkElement element,
            string watermarkText, bool hideOnFocus = false)
        {
            var behavior = new WatermarkBehavior
            {
                WatermarkText = watermarkText,
                HideOnFocus = hideOnFocus
            };
            
            Interaction.GetBehaviors(element).Add(behavior);
            return behavior;
        }

        #endregion

        #region WindowBehavior 扩展

        /// <summary>
        /// 为窗口添加窗口行为
        /// </summary>
        /// <param name="window">目标窗口</param>
        /// <param name="enableDragMove">是否启用拖拽移动</param>
        /// <param name="enableDoubleClickMaximize">是否启用双击最大化</param>
        /// <param name="saveWindowPlacement">是否保存窗口位置</param>
        /// <returns>窗口行为实例</returns>
        public static WindowBehavior AddWindowBehavior(this Window window,
            bool enableDragMove = false,
            bool enableDoubleClickMaximize = false,
            bool saveWindowPlacement = false)
        {
            var behavior = new WindowBehavior
            {
                EnableDragMove = enableDragMove,
                EnableDoubleClickMaximize = enableDoubleClickMaximize,
                SaveWindowPlacement = saveWindowPlacement
            };
            
            Interaction.GetBehaviors(window).Add(behavior);
            return behavior;
        }

        #endregion

        #region 通用扩展

        /// <summary>
        /// 移除指定类型的行为
        /// </summary>
        /// <typeparam name="T">行为类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <returns>是否成功移除</returns>
        public static bool RemoveBehavior<T>(this DependencyObject element) where T : Behavior
        {
            var behaviors = Interaction.GetBehaviors(element);
            for (int i = behaviors.Count - 1; i >= 0; i--)
            {
                if (behaviors[i] is T)
                {
                    behaviors.RemoveAt(i);
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 获取指定类型的行为
        /// </summary>
        /// <typeparam name="T">行为类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <returns>行为实例，如果不存在则返回null</returns>
        public static T GetBehavior<T>(this DependencyObject element) where T : Behavior
        {
            var behaviors = Interaction.GetBehaviors(element);
            foreach (var behavior in behaviors)
            {
                if (behavior is T targetBehavior)
                {
                    return targetBehavior;
                }
            }
            return null;
        }

        /// <summary>
        /// 清除所有行为
        /// </summary>
        /// <param name="element">目标元素</param>
        public static void ClearBehaviors(this DependencyObject element)
        {
            Interaction.GetBehaviors(element).Clear();
        }

        #endregion
    }

    /// <summary>
    /// 滑入方向枚举
    /// </summary>
    public enum SlideDirection
    {
        /// <summary>
        /// 从左侧滑入
        /// </summary>
        Left,
        
        /// <summary>
        /// 从右侧滑入
        /// </summary>
        Right,
        
        /// <summary>
        /// 从顶部滑入
        /// </summary>
        Top,
        
        /// <summary>
        /// 从底部滑入
        /// </summary>
        Bottom
    }
}
