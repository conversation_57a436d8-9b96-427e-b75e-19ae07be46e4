D:\Project\05 FluentDesignWPF\obj\Debug\net8.0-windows\AnimationTest.csproj.AssemblyReference.cache
D:\Project\05 FluentDesignWPF\obj\Debug\net8.0-windows\AnimationTest.baml
D:\Project\05 FluentDesignWPF\obj\Debug\net8.0-windows\Docs\ColorSystemTest.baml
D:\Project\05 FluentDesignWPF\obj\Debug\net8.0-windows\FluentSystemDesign.Examples\App.baml
D:\Project\05 FluentDesignWPF\obj\Debug\net8.0-windows\FluentSystemDesign.Examples\MainWindow.baml
D:\Project\05 FluentDesignWPF\obj\Debug\net8.0-windows\FluentSystemDesign.Examples\Pages\Examples\BehaviorsDemoPage.baml
D:\Project\05 FluentDesignWPF\obj\Debug\net8.0-windows\FluentSystemDesign.Examples\Pages\HomePage.baml
D:\Project\05 FluentDesignWPF\obj\Debug\net8.0-windows\FluentSystemDesign.WPF\Themes\Colors.baml
D:\Project\05 FluentDesignWPF\obj\Debug\net8.0-windows\FluentSystemDesign.WPF\Themes\DarkTheme.baml
D:\Project\05 FluentDesignWPF\obj\Debug\net8.0-windows\FluentSystemDesign.WPF\Themes\LightTheme.baml
D:\Project\05 FluentDesignWPF\obj\Debug\net8.0-windows\FluentSystemDesign.WPF\Themes\Styles.baml
D:\Project\05 FluentDesignWPF\obj\Debug\net8.0-windows\AnimationTest.g.cs
D:\Project\05 FluentDesignWPF\obj\Debug\net8.0-windows\Docs\ColorSystemTest.g.cs
D:\Project\05 FluentDesignWPF\obj\Debug\net8.0-windows\FluentSystemDesign.Examples\App.g.cs
D:\Project\05 FluentDesignWPF\obj\Debug\net8.0-windows\FluentSystemDesign.Examples\MainWindow.g.cs
D:\Project\05 FluentDesignWPF\obj\Debug\net8.0-windows\FluentSystemDesign.Examples\Pages\Examples\BehaviorsDemoPage.g.cs
D:\Project\05 FluentDesignWPF\obj\Debug\net8.0-windows\FluentSystemDesign.Examples\Pages\HomePage.g.cs
D:\Project\05 FluentDesignWPF\obj\Debug\net8.0-windows\App.g.cs
D:\Project\05 FluentDesignWPF\obj\Debug\net8.0-windows\GeneratedInternalTypeHelper.g.cs
D:\Project\05 FluentDesignWPF\obj\Debug\net8.0-windows\AnimationTest_MarkupCompile.cache
D:\Project\05 FluentDesignWPF\obj\Debug\net8.0-windows\AnimationTest_MarkupCompile.lref
