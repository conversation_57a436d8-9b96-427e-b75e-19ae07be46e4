using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Controls;

namespace FluentSystemDesign.Examples.Pages.Examples
{
    /// <summary>
    /// ValueConvertersDemoPage.xaml 的交互逻辑
    /// </summary>
    public partial class ValueConvertersDemoPage : Page, INotifyPropertyChanged
    {
        public ValueConvertersDemoPage()
        {
            InitializeComponent();
            DataContext = this;
            
            // 初始化测试数据
            InitializeTestData();
        }

        #region 属性

        private bool _isTestBoolValue = true;
        public bool IsTestBoolValue
        {
            get => _isTestBoolValue;
            set
            {
                _isTestBoolValue = value;
                OnPropertyChanged(nameof(IsTestBoolValue));
            }
        }

        private double _testNumber = 123.45;
        public double TestNumber
        {
            get => _testNumber;
            set
            {
                _testNumber = value;
                OnPropertyChanged(nameof(TestNumber));
            }
        }

        private string _testString = "Hello World";
        public string TestString
        {
            get => _testString;
            set
            {
                _testString = value;
                OnPropertyChanged(nameof(TestString));
            }
        }

        private string _hexColor = "#FF5722";
        public string HexColor
        {
            get => _hexColor;
            set
            {
                _hexColor = value;
                OnPropertyChanged(nameof(HexColor));
            }
        }

        private TestStatus _currentStatus = TestStatus.Active;
        public TestStatus CurrentStatus
        {
            get => _currentStatus;
            set
            {
                _currentStatus = value;
                OnPropertyChanged(nameof(CurrentStatus));
            }
        }

        private ObservableCollection<string> _testItems = new();
        public ObservableCollection<string> TestItems
        {
            get => _testItems;
            set
            {
                _testItems = value;
                OnPropertyChanged(nameof(TestItems));
            }
        }

        private int _itemCount = 3;
        public int ItemCount
        {
            get => _itemCount;
            set
            {
                _itemCount = value;
                OnPropertyChanged(nameof(ItemCount));
                UpdateTestItems();
            }
        }

        private string _currentTheme = "Light";
        public string CurrentTheme
        {
            get => _currentTheme;
            set
            {
                _currentTheme = value;
                OnPropertyChanged(nameof(CurrentTheme));
            }
        }

        #endregion

        #region 方法

        private void InitializeTestData()
        {
            UpdateTestItems();
        }

        private void UpdateTestItems()
        {
            TestItems.Clear();
            for (int i = 0; i < ItemCount; i++)
            {
                TestItems.Add($"项目 {i + 1}");
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    /// <summary>
    /// 测试状态枚举
    /// </summary>
    public enum TestStatus
    {
        [Description("活跃")]
        Active,

        [Description("非活跃")]
        Inactive,

        [Description("处理中")]
        Processing,

        [Description("已完成")]
        Completed,

        [Description("已禁用")]
        Disabled
    }
}
