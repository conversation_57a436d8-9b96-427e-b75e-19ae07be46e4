using System;
using System.Windows;
using System.Windows.Media;
using System.Windows.Media.Effects;

namespace FluentSystemDesign.WPF.Effects.Helpers
{
    /// <summary>
    /// 提供视觉效果的辅助方法和动态应用功能
    /// </summary>
    public static class EffectHelper
    {
        #region 阴影效果方法

        /// <summary>
        /// 创建指定高度的阴影效果
        /// </summary>
        /// <param name="elevation">高度级别 (0-24)</param>
        /// <param name="isDarkTheme">是否为深色主题</param>
        /// <returns>阴影效果</returns>
        public static DropShadowEffect CreateElevationShadow(int elevation, bool isDarkTheme = false)
        {
            if (elevation < 0) elevation = 0;
            if (elevation > 24) elevation = 24;

            var shadowData = GetShadowData(elevation);
            var opacityMultiplier = isDarkTheme ? 2.0 : 1.0;

            return new DropShadowEffect
            {
                ShadowDepth = shadowData.Depth,
                BlurRadius = shadowData.BlurRadius,
                Opacity = Math.Min(shadowData.Opacity * opacityMultiplier, 1.0),
                Color = Colors.Black
            };
        }

        /// <summary>
        /// 创建自定义阴影效果
        /// </summary>
        /// <param name="depth">阴影深度</param>
        /// <param name="blurRadius">模糊半径</param>
        /// <param name="opacity">不透明度</param>
        /// <param name="color">阴影颜色</param>
        /// <returns>阴影效果</returns>
        public static DropShadowEffect CreateCustomShadow(double depth, double blurRadius, double opacity, Color color)
        {
            return new DropShadowEffect
            {
                ShadowDepth = depth,
                BlurRadius = blurRadius,
                Opacity = Math.Max(0, Math.Min(1, opacity)),
                Color = color
            };
        }

        #endregion

        #region 发光效果方法

        /// <summary>
        /// 创建发光效果
        /// </summary>
        /// <param name="color">发光颜色</param>
        /// <param name="intensity">发光强度 (0-1)</param>
        /// <param name="radius">发光半径</param>
        /// <returns>发光效果</returns>
        public static DropShadowEffect CreateGlowEffect(Color color, double intensity = 0.5, double radius = 10)
        {
            return new DropShadowEffect
            {
                ShadowDepth = 0,
                BlurRadius = radius,
                Opacity = Math.Max(0, Math.Min(1, intensity)),
                Color = color
            };
        }

        /// <summary>
        /// 创建主题色发光效果
        /// </summary>
        /// <param name="colorType">颜色类型</param>
        /// <param name="intensity">发光强度</param>
        /// <param name="radius">发光半径</param>
        /// <returns>发光效果</returns>
        public static DropShadowEffect CreateThemeGlow(ThemeColorType colorType, double intensity = 0.5, double radius = 10)
        {
            var color = GetThemeColor(colorType);
            return CreateGlowEffect(color, intensity, radius);
        }

        #endregion

        #region 模糊效果方法

        /// <summary>
        /// 创建模糊效果
        /// </summary>
        /// <param name="radius">模糊半径</param>
        /// <param name="kernelType">模糊核心类型</param>
        /// <returns>模糊效果</returns>
        public static BlurEffect CreateBlurEffect(double radius, KernelType kernelType = KernelType.Gaussian)
        {
            return new BlurEffect
            {
                Radius = Math.Max(0, radius),
                KernelType = kernelType
            };
        }

        /// <summary>
        /// 创建预定义强度的模糊效果
        /// </summary>
        /// <param name="intensity">模糊强度</param>
        /// <returns>模糊效果</returns>
        public static BlurEffect CreateBlurEffect(BlurIntensity intensity)
        {
            var radius = intensity switch
            {
                BlurIntensity.Subtle => 2.0,
                BlurIntensity.Light => 4.0,
                BlurIntensity.Medium => 8.0,
                BlurIntensity.Strong => 16.0,
                BlurIntensity.Heavy => 32.0,
                _ => 8.0
            };

            return CreateBlurEffect(radius);
        }

        #endregion

        #region 渐变效果方法

        /// <summary>
        /// 创建线性渐变画刷
        /// </summary>
        /// <param name="startColor">起始颜色</param>
        /// <param name="endColor">结束颜色</param>
        /// <param name="angle">渐变角度</param>
        /// <returns>线性渐变画刷</returns>
        public static LinearGradientBrush CreateLinearGradient(Color startColor, Color endColor, double angle = 90)
        {
            var radians = angle * Math.PI / 180;
            var startPoint = new Point(0.5 - Math.Sin(radians) * 0.5, 0.5 + Math.Cos(radians) * 0.5);
            var endPoint = new Point(0.5 + Math.Sin(radians) * 0.5, 0.5 - Math.Cos(radians) * 0.5);

            return new LinearGradientBrush
            {
                StartPoint = startPoint,
                EndPoint = endPoint,
                GradientStops = new GradientStopCollection
                {
                    new GradientStop(startColor, 0),
                    new GradientStop(endColor, 1)
                }
            };
        }

        /// <summary>
        /// 创建径向渐变画刷
        /// </summary>
        /// <param name="centerColor">中心颜色</param>
        /// <param name="edgeColor">边缘颜色</param>
        /// <param name="center">中心点</param>
        /// <param name="radiusX">X轴半径</param>
        /// <param name="radiusY">Y轴半径</param>
        /// <returns>径向渐变画刷</returns>
        public static RadialGradientBrush CreateRadialGradient(Color centerColor, Color edgeColor, 
            Point? center = null, double radiusX = 0.8, double radiusY = 0.8)
        {
            return new RadialGradientBrush
            {
                Center = center ?? new Point(0.5, 0.5),
                RadiusX = radiusX,
                RadiusY = radiusY,
                GradientStops = new GradientStopCollection
                {
                    new GradientStop(centerColor, 0),
                    new GradientStop(edgeColor, 1)
                }
            };
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 获取阴影数据
        /// </summary>
        /// <param name="elevation">高度级别</param>
        /// <returns>阴影数据</returns>
        private static (double Depth, double BlurRadius, double Opacity) GetShadowData(int elevation)
        {
            return elevation switch
            {
                0 => (0, 0, 0),
                1 => (1, 3, 0.12),
                2 => (1, 5, 0.14),
                3 => (1, 8, 0.15),
                4 => (2, 10, 0.16),
                6 => (3, 14, 0.18),
                8 => (4, 18, 0.20),
                12 => (6, 24, 0.22),
                16 => (8, 32, 0.24),
                24 => (12, 48, 0.26),
                _ => (elevation / 2.0, elevation * 2.0, 0.12 + elevation * 0.006)
            };
        }

        /// <summary>
        /// 获取主题颜色
        /// </summary>
        /// <param name="colorType">颜色类型</param>
        /// <returns>颜色</returns>
        private static Color GetThemeColor(ThemeColorType colorType)
        {
            return colorType switch
            {
                ThemeColorType.Primary => Color.FromRgb(33, 150, 243),
                ThemeColorType.Secondary => Color.FromRgb(156, 39, 176),
                ThemeColorType.Accent => Color.FromRgb(255, 152, 0),
                ThemeColorType.Success => Color.FromRgb(76, 175, 80),
                ThemeColorType.Warning => Color.FromRgb(255, 143, 0),
                ThemeColorType.Error => Color.FromRgb(244, 67, 54),
                ThemeColorType.Info => Color.FromRgb(0, 188, 212),
                _ => Color.FromRgb(33, 150, 243)
            };
        }

        #endregion
    }

    #region 枚举定义

    /// <summary>
    /// 主题颜色类型
    /// </summary>
    public enum ThemeColorType
    {
        Primary,
        Secondary,
        Accent,
        Success,
        Warning,
        Error,
        Info
    }

    /// <summary>
    /// 模糊强度
    /// </summary>
    public enum BlurIntensity
    {
        Subtle,
        Light,
        Medium,
        Strong,
        Heavy
    }

    #endregion
}
