# FluentSystemDesign WPF控件库 - 几何形状资源系统

## 概述

FluentSystemDesign WPF控件库提供了完整的几何形状资源系统，包含各种矢量几何形状，用于构建现代化的用户界面。所有几何形状都遵循Fluent Design System设计语言，支持无损缩放，可在不同DPI设置下保持清晰。

## 特性

- **矢量格式**：所有几何形状都是矢量格式，支持无损缩放
- **分类组织**：按功能分类，便于查找和使用
- **统一命名**：遵循一致的命名规范，易于记忆
- **主题兼容**：与深色/浅色主题系统完美集成
- **性能优化**：使用WPF原生几何类型，确保最佳性能

## 几何形状分类

### 1. 基础几何形状 (BasicGeometries.xaml)

基础几何形状包含常用的基本图形，如圆形、矩形、三角形等。

#### 圆形几何形状
- `CircleGeometry` - 标准圆形 (24x24)
- `SmallCircleGeometry` - 小圆形 (16x16)
- `LargeCircleGeometry` - 大圆形 (32x32)

#### 矩形几何形状
- `RectangleGeometry` - 标准矩形
- `RoundedRectangleGeometry` - 圆角矩形
- `SmallRoundedRectangleGeometry` - 小圆角矩形
- `LargeRoundedRectangleGeometry` - 大圆角矩形
- `SquareGeometry` - 正方形

#### 三角形几何形状
- `TriangleUpGeometry` - 向上三角形
- `TriangleDownGeometry` - 向下三角形
- `TriangleLeftGeometry` - 向左三角形
- `TriangleRightGeometry` - 向右三角形
- `EquilateralTriangleGeometry` - 等边三角形

#### 多边形几何形状
- `PentagonGeometry` - 五边形
- `HexagonGeometry` - 六边形
- `OctagonGeometry` - 八边形
- `StarGeometry` - 星形
- `FivePointStarGeometry` - 五角星
- `DiamondGeometry` - 菱形
- `HeartGeometry` - 心形

### 2. 图标几何形状 (IconGeometries.xaml)

图标几何形状包含常用的UI图标，如箭头、勾选、关闭等。

#### 箭头图标
- `ChevronRightGeometry` - 向右箭头
- `ChevronLeftGeometry` - 向左箭头
- `ChevronUpGeometry` - 向上箭头
- `ChevronDownGeometry` - 向下箭头
- `DoubleChevronRightGeometry` - 双向右箭头
- `DoubleChevronLeftGeometry` - 双向左箭头

#### 操作图标
- `CheckMarkGeometry` - 勾选图标
- `ThickCheckMarkGeometry` - 粗勾选图标
- `CloseGeometry` - 关闭图标
- `PlusGeometry` - 加号图标
- `MinusGeometry` - 减号图标

#### 功能图标
- `MenuGeometry` - 菜单图标（汉堡菜单）
- `SearchGeometry` - 搜索图标
- `DetailedSearchGeometry` - 详细搜索图标
- `SettingsGeometry` - 设置图标（齿轮）

#### 状态图标
- `InfoGeometry` - 信息图标
- `WarningGeometry` - 警告图标
- `ErrorGeometry` - 错误图标
- `SuccessGeometry` - 成功图标

#### 导航图标
- `HomeGeometry` - 主页图标
- `UserGeometry` - 用户图标
- `FileGeometry` - 文件图标
- `FolderGeometry` - 文件夹图标

### 3. 装饰几何形状 (DecorativeGeometries.xaml)

装饰几何形状用于UI装饰和视觉分隔。

#### 分隔线
- `HorizontalSeparatorGeometry` - 水平分隔线
- `VerticalSeparatorGeometry` - 垂直分隔线
- `ShortHorizontalSeparatorGeometry` - 短水平分隔线
- `ShortVerticalSeparatorGeometry` - 短垂直分隔线

#### 边框
- `CircleBorderGeometry` - 圆形边框
- `RectangleBorderGeometry` - 矩形边框
- `RoundedRectangleBorderGeometry` - 圆角矩形边框
- `DashedBorderGeometry` - 虚线边框

#### 装饰元素
- `DecorativeDotGeometry` - 装饰点
- `ThreeDotsGeometry` - 三个装饰点
- `VerticalThreeDotsGeometry` - 垂直三个装饰点
- `DecorativeLineGeometry` - 装饰线条
- `WaveLineGeometry` - 波浪线
- `ZigzagLineGeometry` - 锯齿线

#### 角标
- `CircleBadgeGeometry` - 圆形角标
- `SmallCircleBadgeGeometry` - 小圆形角标
- `RectangleBadgeGeometry` - 矩形角标
- `TriangleBadgeGeometry` - 三角形角标

#### 控件元素
- `CircularProgressBackgroundGeometry` - 圆形进度条背景
- `LinearProgressBackgroundGeometry` - 线性进度条背景
- `SliderTrackGeometry` - 滑块轨道
- `SliderThumbGeometry` - 滑块拇指
- `ToggleSwitchBackgroundGeometry` - 开关背景
- `ToggleSwitchThumbGeometry` - 开关拇指
- `CheckBoxBackgroundGeometry` - 复选框背景
- `RadioButtonBackgroundGeometry` - 单选按钮背景
- `RadioButtonCheckGeometry` - 单选按钮选中标记

#### 箭头装饰
- `ExpandArrowGeometry` - 展开箭头
- `CollapseArrowGeometry` - 折叠箭头
- `SortAscendingGeometry` - 升序排序箭头
- `SortDescendingGeometry` - 降序排序箭头

#### 网格
- `GridLinesGeometry` - 网格线
- `DotGridGeometry` - 点状网格

### 4. 路径几何形状 (PathGeometries.xaml)

路径几何形状包含复杂的矢量图形路径。

#### 复杂图标
- `CloudGeometry` - 云朵图标
- `MailGeometry` - 邮件图标
- `PhoneGeometry` - 电话图标
- `CalendarGeometry` - 日历图标
- `ClockGeometry` - 时钟图标
- `LocationGeometry` - 位置图标
- `ShoppingCartGeometry` - 购物车图标
- `HeartbeatGeometry` - 心跳图标

#### 媒体图标
- `MusicGeometry` - 音乐图标
- `CameraGeometry` - 相机图标
- `VideoGeometry` - 视频图标
- `ImageGeometry` - 图片图标

#### 操作图标
- `DownloadGeometry` - 下载图标
- `UploadGeometry` - 上传图标
- `ShareGeometry` - 分享图标
- `LinkGeometry` - 链接图标

## 使用方法

### 在XAML中使用

#### 作为Path的Data属性
```xml
<Path Data="{StaticResource CheckMarkGeometry}" 
      Fill="{StaticResource AccentBrush}" 
      Stretch="Uniform" 
      Width="16" Height="16" />
```

#### 在控件模板中使用
```xml
<ControlTemplate TargetType="Button">
    <Border Background="{TemplateBinding Background}">
        <Grid>
            <Path Data="{StaticResource ChevronRightGeometry}" 
                  Fill="{TemplateBinding Foreground}" 
                  Stretch="Uniform" 
                  Width="12" Height="12" 
                  HorizontalAlignment="Center" 
                  VerticalAlignment="Center" />
        </Grid>
    </Border>
</ControlTemplate>
```

#### 在样式中使用
```xml
<Style TargetType="Button" x:Key="IconButton">
    <Setter Property="Template">
        <Setter.Value>
            <ControlTemplate TargetType="Button">
                <Border Background="{TemplateBinding Background}" 
                        CornerRadius="4">
                    <Path Data="{Binding Tag, RelativeSource={RelativeSource TemplatedParent}}" 
                          Fill="{TemplateBinding Foreground}" 
                          Stretch="Uniform" 
                          Width="16" Height="16" 
                          HorizontalAlignment="Center" 
                          VerticalAlignment="Center" />
                </Border>
            </ControlTemplate>
        </Setter.Value>
    </Setter>
</Style>

<!-- 使用 -->
<Button Style="{StaticResource IconButton}" 
        Tag="{StaticResource SearchGeometry}" />
```

### 在代码中使用

```csharp
// 获取几何形状资源
var checkGeometry = (Geometry)Application.Current.FindResource("CheckMarkGeometry");

// 创建Path控件
var path = new Path
{
    Data = checkGeometry,
    Fill = Brushes.Green,
    Stretch = Stretch.Uniform,
    Width = 16,
    Height = 16
};

// 添加到容器
container.Children.Add(path);
```

### 动态切换几何形状

```csharp
// 在代码中动态切换几何形状
private void ToggleIcon()
{
    var currentGeometry = iconPath.Data;
    var expandGeometry = (Geometry)FindResource("ExpandArrowGeometry");
    var collapseGeometry = (Geometry)FindResource("CollapseArrowGeometry");
    
    iconPath.Data = currentGeometry == expandGeometry ? 
        collapseGeometry : expandGeometry;
}
```

## 自定义几何形状

### 添加新的几何形状

1. 在相应的资源字典文件中添加新的几何形状定义
2. 使用统一的命名规范：`[描述]Geometry`
3. 确保几何形状适合24x24的标准尺寸
4. 添加XML注释说明用途

```xml
<!-- 自定义星形图标 -->
<PathGeometry x:Key="CustomStarGeometry">
    <PathFigure StartPoint="12,2" IsClosed="True">
        <LineSegment Point="15.09,8.09" />
        <LineSegment Point="23,8.09" />
        <LineSegment Point="16.95,13.18" />
        <LineSegment Point="19.09,21.27" />
        <LineSegment Point="12,16.18" />
        <LineSegment Point="4.91,21.27" />
        <LineSegment Point="7.05,13.18" />
        <LineSegment Point="1,8.09" />
        <LineSegment Point="8.91,8.09" />
    </PathFigure>
</PathGeometry>
```

### 创建组合几何形状

```xml
<!-- 组合多个几何形状 -->
<GeometryGroup x:Key="CompositeIconGeometry">
    <EllipseGeometry Center="12,12" RadiusX="10" RadiusY="10" />
    <PathGeometry>
        <PathFigure StartPoint="8,12">
            <LineSegment Point="11,15" />
            <LineSegment Point="16,10" />
        </PathFigure>
    </PathGeometry>
</GeometryGroup>
```

## 性能建议

1. **资源重用**：尽量重用现有的几何形状资源，避免重复定义
2. **合理缓存**：几何形状资源会自动缓存，无需手动管理
3. **避免过度复杂**：保持几何形状的简洁性，避免过多的路径点
4. **使用StreamGeometry**：对于动态生成的几何形状，考虑使用StreamGeometry提高性能

## 辅助功能支持

在使用几何形状图标时，请确保提供适当的辅助功能支持：

```xml
<Path Data="{StaticResource CheckMarkGeometry}" 
      Fill="{StaticResource AccentBrush}" 
      AutomationProperties.Name="已选中"
      AutomationProperties.HelpText="此项已被选中" />
```

## 主题适配

几何形状本身是无色的，颜色通过Fill属性设置。建议使用主题颜色资源：

```xml
<Path Data="{StaticResource InfoGeometry}" 
      Fill="{StaticResource AccentBrush}" />

<Path Data="{StaticResource WarningGeometry}" 
      Fill="{StaticResource WarningBrush}" />

<Path Data="{StaticResource ErrorGeometry}" 
      Fill="{StaticResource ErrorBrush}" />
```

## 相关资源

- [色彩系统文档](ColorSystem.md)
- [视觉效果文档](Effects.md)
- [字体系统文档](Typography.md)
- [值转换器文档](ValueConverters.md)

---

[返回主文档](../README.md)
