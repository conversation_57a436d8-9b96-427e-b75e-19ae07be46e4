<Page x:Class="FluentSystemDesign.Examples.Pages.Examples.BehaviorsDemoPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
      xmlns:behaviors="clr-namespace:FluentSystemDesign.WPF.Behaviors;assembly=FluentSystemDesign.WPF"
      Title="行为类示例">

    <ScrollViewer>
        <StackPanel Margin="20">
            <!-- 页面标题 -->
            <TextBlock Text="行为类示例" 
                       FontSize="24" 
                       FontWeight="Bold" 
                       Margin="0,0,0,20" />

            <!-- 动画行为示例 -->
            <GroupBox Header="动画行为 (AnimationBehavior)" Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="点击按钮查看不同的动画效果：" Margin="0,0,0,10" />
                    
                    <WrapPanel>
                        <Button Content="淡入动画" Margin="5" Click="FadeInButton_Click" />
                        <Button Content="滑入动画" Margin="5" Click="SlideInButton_Click" />
                        <Button Content="缩放动画" Margin="5" Click="ScaleInButton_Click" />
                        <Button Content="重置" Margin="5" Click="ResetButton_Click" Background="LightCoral" />
                    </WrapPanel>
                    
                    <Border x:Name="AnimationTarget" 
                            Width="200" Height="100" 
                            Background="LightBlue" 
                            Margin="0,10,0,0"
                            HorizontalAlignment="Left">
                        <TextBlock Text="动画目标" 
                                   HorizontalAlignment="Center" 
                                   VerticalAlignment="Center" />
                    </Border>
                </StackPanel>
            </GroupBox>

            <!-- 验证行为示例 -->
            <GroupBox Header="验证行为 (ValidationBehavior)" Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="输入验证示例：" Margin="0,0,0,10" />
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="邮箱：" VerticalAlignment="Center" Margin="0,0,10,5" />
                        <TextBox Grid.Row="0" Grid.Column="1" Margin="0,0,0,5">
                            <i:Interaction.Behaviors>
                                <behaviors:ValidationBehavior 
                                    ValidationType="Email"
                                    IsRequired="True"
                                    ValidateOnTextChanged="True" />
                                <behaviors:WatermarkBehavior 
                                    WatermarkText="请输入邮箱地址"
                                    WatermarkForeground="Gray" />
                            </i:Interaction.Behaviors>
                        </TextBox>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="手机号：" VerticalAlignment="Center" Margin="0,0,10,5" />
                        <TextBox Grid.Row="1" Grid.Column="1" Margin="0,0,0,5">
                            <i:Interaction.Behaviors>
                                <behaviors:ValidationBehavior 
                                    ValidationType="Phone"
                                    IsRequired="True"
                                    ValidateOnTextChanged="True" />
                                <behaviors:WatermarkBehavior 
                                    WatermarkText="请输入手机号码"
                                    WatermarkForeground="Gray" />
                            </i:Interaction.Behaviors>
                        </TextBox>
                    </Grid>
                </StackPanel>
            </GroupBox>

            <!-- 水印行为示例 -->
            <GroupBox Header="水印行为 (WatermarkBehavior)" Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="水印文本示例：" Margin="0,0,0,10" />
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="基本水印：" VerticalAlignment="Center" Margin="0,0,10,5" />
                        <TextBox Grid.Row="0" Grid.Column="1" Margin="0,0,0,5">
                            <i:Interaction.Behaviors>
                                <behaviors:WatermarkBehavior 
                                    WatermarkText="请输入文本"
                                    WatermarkForeground="Gray" />
                            </i:Interaction.Behaviors>
                        </TextBox>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="焦点隐藏：" VerticalAlignment="Center" Margin="0,0,10,5" />
                        <TextBox Grid.Row="1" Grid.Column="1" Margin="0,0,0,5">
                            <i:Interaction.Behaviors>
                                <behaviors:WatermarkBehavior 
                                    WatermarkText="获得焦点时隐藏"
                                    WatermarkForeground="LightBlue"
                                    HideOnFocus="True" />
                            </i:Interaction.Behaviors>
                        </TextBox>
                    </Grid>
                </StackPanel>
            </GroupBox>

            <!-- 使用说明 -->
            <GroupBox Header="使用说明">
                <StackPanel>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,5">
                        这些行为类可以通过XAML或代码方式添加到任何UI元素上。每个行为都提供了丰富的配置选项，
                        可以根据具体需求进行定制。
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,5">
                        • 动画行为：提供多种预定义动画效果，支持自定义触发器和缓动函数
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,5">
                        • 验证行为：支持多种验证规则，提供实时验证反馈
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap">
                        • 水印行为：为输入控件提供美观的水印提示功能
                    </TextBlock>
                </StackPanel>
            </GroupBox>
        </StackPanel>
    </ScrollViewer>
</Page>
