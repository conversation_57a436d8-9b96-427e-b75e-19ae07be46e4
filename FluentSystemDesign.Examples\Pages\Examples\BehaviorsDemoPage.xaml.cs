using System;
using System.Windows;
using System.Windows.Controls;
using FluentSystemDesign.WPF.Behaviors;
using Microsoft.Xaml.Behaviors;

namespace FluentSystemDesign.Examples.Pages.Examples
{
    /// <summary>
    /// BehaviorsDemoPage.xaml 的交互逻辑
    /// </summary>
    public partial class BehaviorsDemoPage : Page
    {
        public BehaviorsDemoPage()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 获取动画目标元素
        /// </summary>
        private Border GetAnimationTarget()
        {
            return this.FindName("AnimationTarget") as Border;
        }

        /// <summary>
        /// 淡入动画按钮点击事件
        /// </summary>
        private void FadeInButton_Click(object sender, RoutedEventArgs e)
        {
            var animationTarget = GetAnimationTarget();
            if (animationTarget == null) return;

            // 清除现有的动画行为
            var behaviors = Interaction.GetBehaviors(animationTarget);
            behaviors.Clear();

            // 添加淡入动画行为
            var fadeInBehavior = new AnimationBehavior
            {
                AnimationType = AnimationType.FadeIn,
                Duration = TimeSpan.FromMilliseconds(500),
                Trigger = AnimationTrigger.Manual
            };

            behaviors.Add(fadeInBehavior);

            // 重置透明度并开始动画
            animationTarget.Opacity = 0;
            fadeInBehavior.StartAnimation();
        }

        /// <summary>
        /// 滑入动画按钮点击事件
        /// </summary>
        private void SlideInButton_Click(object sender, RoutedEventArgs e)
        {
            var animationTarget = GetAnimationTarget();
            if (animationTarget == null) return;

            // 清除现有的动画行为
            var behaviors = Interaction.GetBehaviors(animationTarget);
            behaviors.Clear();

            // 停止任何正在进行的动画
            if (animationTarget.RenderTransform != null)
            {
                animationTarget.BeginAnimation(UIElement.RenderTransformProperty, null);
            }

            // 添加滑入动画行为
            var slideInBehavior = new AnimationBehavior
            {
                AnimationType = AnimationType.SlideInFromLeft,
                Duration = TimeSpan.FromMilliseconds(800), // 适中的动画时间
                Trigger = AnimationTrigger.Manual
            };

            behaviors.Add(slideInBehavior);

            // 立即开始动画
            slideInBehavior.StartAnimation();
        }

        /// <summary>
        /// 缩放动画按钮点击事件
        /// </summary>
        private void ScaleInButton_Click(object sender, RoutedEventArgs e)
        {
            var animationTarget = GetAnimationTarget();
            if (animationTarget == null) return;

            // 清除现有的动画行为
            var behaviors = Interaction.GetBehaviors(animationTarget);
            behaviors.Clear();

            // 停止任何正在进行的动画
            if (animationTarget.RenderTransform != null)
            {
                animationTarget.BeginAnimation(UIElement.RenderTransformProperty, null);
            }

            // 添加缩放动画行为
            var scaleInBehavior = new AnimationBehavior
            {
                AnimationType = AnimationType.ScaleIn,
                Duration = TimeSpan.FromMilliseconds(800), // 适中的动画时间
                Trigger = AnimationTrigger.Manual
            };

            behaviors.Add(scaleInBehavior);

            // 立即开始动画
            scaleInBehavior.StartAnimation();
        }

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void ResetButton_Click(object sender, RoutedEventArgs e)
        {
            var animationTarget = GetAnimationTarget();
            if (animationTarget == null) return;

            // 停止所有动画
            animationTarget.BeginAnimation(UIElement.OpacityProperty, null);
            animationTarget.BeginAnimation(UIElement.RenderTransformProperty, null);

            // 清除所有行为
            var behaviors = Interaction.GetBehaviors(animationTarget);
            behaviors.Clear();

            // 重置所有属性到默认状态
            animationTarget.Opacity = 1.0;
            animationTarget.RenderTransform = null;
            animationTarget.RenderTransformOrigin = new Point(0.5, 0.5);
        }
    }
}
