<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Merge base colors -->
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Colors.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <!-- ========================================== -->
    <!-- Light Theme Color Mappings -->
    <!-- ========================================== -->

    <!-- Primary Theme Colors -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource Primary500}"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="{StaticResource Primary300}"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="{StaticResource Primary700}"/>
    <SolidColorBrush x:Key="PrimaryHoverBrush" Color="{StaticResource Primary400}"/>
    <SolidColorBrush x:Key="PrimaryPressedBrush" Color="{StaticResource Primary600}"/>

    <!-- Secondary Theme Colors -->
    <SolidColorBrush x:Key="SecondaryBrush" Color="{StaticResource Secondary500}"/>
    <SolidColorBrush x:Key="SecondaryLightBrush" Color="{StaticResource Secondary300}"/>
    <SolidColorBrush x:Key="SecondaryDarkBrush" Color="{StaticResource Secondary700}"/>
    <SolidColorBrush x:Key="SecondaryHoverBrush" Color="{StaticResource Secondary400}"/>
    <SolidColorBrush x:Key="SecondaryPressedBrush" Color="{StaticResource Secondary600}"/>

    <!-- Accent Theme Colors -->
    <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource Accent500}"/>
    <SolidColorBrush x:Key="AccentLightBrush" Color="{StaticResource Accent300}"/>
    <SolidColorBrush x:Key="AccentDarkBrush" Color="{StaticResource Accent700}"/>
    <SolidColorBrush x:Key="AccentHoverBrush" Color="{StaticResource Accent400}"/>
    <SolidColorBrush x:Key="AccentPressedBrush" Color="{StaticResource Accent600}"/>

    <!-- Background Colors -->
    <SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource Neutral50}"/>
    <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="CardBackgroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="DialogBackgroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="PopupBackgroundBrush" Color="#FFFFFF"/>

    <!-- Text Colors -->
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="{StaticResource Neutral900}"/>
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="{StaticResource Neutral700}"/>
    <SolidColorBrush x:Key="TextTertiaryBrush" Color="{StaticResource Neutral600}"/>
    <SolidColorBrush x:Key="TextDisabledBrush" Color="{StaticResource Neutral400}"/>
    <SolidColorBrush x:Key="TextOnPrimaryBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="TextOnSecondaryBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="TextOnAccentBrush" Color="#FFFFFF"/>

    <!-- Border Colors -->
    <SolidColorBrush x:Key="BorderBrush" Color="{StaticResource Neutral300}"/>
    <SolidColorBrush x:Key="BorderLightBrush" Color="{StaticResource Neutral200}"/>
    <SolidColorBrush x:Key="BorderDarkBrush" Color="{StaticResource Neutral400}"/>
    <SolidColorBrush x:Key="DividerBrush" Color="{StaticResource Neutral200}"/>

    <!-- Control Colors -->
    <SolidColorBrush x:Key="ControlBackgroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="ControlHoverBrush" Color="{StaticResource Neutral100}"/>
    <SolidColorBrush x:Key="ControlPressedBrush" Color="{StaticResource Neutral200}"/>
    <SolidColorBrush x:Key="ControlDisabledBrush" Color="{StaticResource Neutral100}"/>
    <SolidColorBrush x:Key="ControlFocusBrush" Color="{StaticResource Primary500}"/>

    <!-- Input Colors -->
    <SolidColorBrush x:Key="InputBackgroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="InputBorderBrush" Color="{StaticResource Neutral300}"/>
    <SolidColorBrush x:Key="InputHoverBorderBrush" Color="{StaticResource Neutral400}"/>
    <SolidColorBrush x:Key="InputFocusBorderBrush" Color="{StaticResource Primary500}"/>
    <SolidColorBrush x:Key="InputDisabledBackgroundBrush" Color="{StaticResource Neutral100}"/>
    <SolidColorBrush x:Key="InputDisabledBorderBrush" Color="{StaticResource Neutral200}"/>

    <!-- Shadow Colors -->
    <SolidColorBrush x:Key="ShadowBrush" Color="#1A000000"/>
    <SolidColorBrush x:Key="ShadowLightBrush" Color="#0D000000"/>
    <SolidColorBrush x:Key="ShadowDarkBrush" Color="#26000000"/>

    <!-- Semantic State Colors -->
    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource Success500}"/>
    <SolidColorBrush x:Key="SuccessLightBrush" Color="{StaticResource Success100}"/>
    <SolidColorBrush x:Key="SuccessTextBrush" Color="{StaticResource Success700}"/>

    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource Warning500}"/>
    <SolidColorBrush x:Key="WarningLightBrush" Color="{StaticResource Warning100}"/>
    <SolidColorBrush x:Key="WarningTextBrush" Color="{StaticResource Warning700}"/>

    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource Error500}"/>
    <SolidColorBrush x:Key="ErrorLightBrush" Color="{StaticResource Error100}"/>
    <SolidColorBrush x:Key="ErrorTextBrush" Color="{StaticResource Error700}"/>

    <SolidColorBrush x:Key="InfoBrush" Color="{StaticResource Info500}"/>
    <SolidColorBrush x:Key="InfoLightBrush" Color="{StaticResource Info100}"/>
    <SolidColorBrush x:Key="InfoTextBrush" Color="{StaticResource Info700}"/>

    <!-- Navigation Colors -->
    <SolidColorBrush x:Key="NavigationBackgroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="NavigationItemHoverBrush" Color="{StaticResource Neutral100}"/>
    <SolidColorBrush x:Key="NavigationItemSelectedBrush" Color="{StaticResource Primary100}"/>
    <SolidColorBrush x:Key="NavigationItemPressedBrush" Color="{StaticResource Neutral200}"/>

    <!-- Menu Colors -->
    <SolidColorBrush x:Key="MenuBackgroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="MenuItemHoverBrush" Color="{StaticResource Neutral100}"/>
    <SolidColorBrush x:Key="MenuItemPressedBrush" Color="{StaticResource Neutral200}"/>
    <SolidColorBrush x:Key="MenuSeparatorBrush" Color="{StaticResource Neutral200}"/>

    <!-- Overlay Colors -->
    <SolidColorBrush x:Key="OverlayBrush" Color="#80000000"/>
    <SolidColorBrush x:Key="OverlayLightBrush" Color="#40000000"/>

    <!-- ========================================== -->
    <!-- Light Theme Effect Overrides -->
    <!-- ========================================== -->

    <!-- 浅色主题下的阴影效果别名 -->
    <StaticResource x:Key="ThemeElevation1" ResourceKey="Elevation1"/>
    <StaticResource x:Key="ThemeElevation2" ResourceKey="Elevation2"/>
    <StaticResource x:Key="ThemeElevation3" ResourceKey="Elevation3"/>
    <StaticResource x:Key="ThemeElevation4" ResourceKey="Elevation4"/>
    <StaticResource x:Key="ThemeElevation6" ResourceKey="Elevation6"/>
    <StaticResource x:Key="ThemeElevation8" ResourceKey="Elevation8"/>
    <StaticResource x:Key="ThemeElevation12" ResourceKey="Elevation12"/>
    <StaticResource x:Key="ThemeElevation16" ResourceKey="Elevation16"/>
    <StaticResource x:Key="ThemeElevation24" ResourceKey="Elevation24"/>

    <!-- 浅色主题下的渐变效果 -->
    <StaticResource x:Key="ThemeBackgroundGradient" ResourceKey="AppBackgroundLightGradient"/>
    <StaticResource x:Key="ThemeAcrylicGradient" ResourceKey="LightAcrylicGradient"/>

</ResourceDictionary>
