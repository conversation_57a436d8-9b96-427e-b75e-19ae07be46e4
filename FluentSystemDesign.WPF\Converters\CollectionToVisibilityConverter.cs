using System;
using System.Collections;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Data;

namespace FluentSystemDesign.WPF.Converters
{
    /// <summary>
    /// 集合到可见性转换器
    /// 根据集合是否为空来控制元素的可见性
    /// </summary>
    /// <remarks>
    /// 默认行为：
    /// - 集合有元素时显示（Visible）
    /// - 集合为空或null时隐藏（Collapsed）
    /// 
    /// 支持的参数：
    /// - "Invert": 反转逻辑（空时显示，有元素时隐藏）
    /// - "Hidden": 使用Hidden而不是Collapsed
    /// - "MinCount=N": 最小元素数量阈值
    /// - "MaxCount=N": 最大元素数量阈值
    /// </remarks>
    /// <example>
    /// 在XAML中使用：
    /// <code>
    /// &lt;TextBlock Visibility="{Binding Items, Converter={StaticResource CollectionToVisibilityConverter}}" Text="有数据"/&gt;
    /// &lt;TextBlock Visibility="{Binding Items, Converter={StaticResource CollectionToVisibilityConverter}, ConverterParameter=Invert}" Text="无数据"/&gt;
    /// &lt;Button Visibility="{Binding Items, Converter={StaticResource CollectionToVisibilityConverter}, ConverterParameter=MinCount=5}"/&gt;
    /// </code>
    /// </example>
    [ValueConversion(typeof(IEnumerable), typeof(Visibility))]
    public class CollectionToVisibilityConverter : IValueConverter
    {
        /// <summary>
        /// 默认的隐藏方式
        /// </summary>
        public Visibility DefaultHiddenVisibility { get; set; } = Visibility.Collapsed;

        /// <summary>
        /// 将集合转换为可见性
        /// </summary>
        /// <param name="value">集合对象</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>可见性值</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var (isInverted, hiddenVisibility, minCount, maxCount) = ParseParameter(parameter?.ToString());

            // 获取集合元素数量
            var count = GetCollectionCount(value);

            // 判断是否满足条件
            var meetsCondition = count >= minCount && (maxCount == null || count <= maxCount.Value);

            // 应用反转逻辑
            var shouldBeVisible = isInverted ? !meetsCondition : meetsCondition;

            return shouldBeVisible ? Visibility.Visible : hiddenVisibility;
        }

        /// <summary>
        /// 反向转换（不支持）
        /// </summary>
        /// <param name="value">可见性值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>不支持的操作异常</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotSupportedException("CollectionToVisibilityConverter does not support ConvertBack operation.");
        }

        /// <summary>
        /// 获取集合的元素数量
        /// </summary>
        /// <param name="collection">集合对象</param>
        /// <returns>元素数量</returns>
        private static int GetCollectionCount(object collection)
        {
            if (collection == null)
            {
                return 0;
            }

            // 优先使用Count属性（性能更好）
            if (collection is ICollection collectionWithCount)
            {
                return collectionWithCount.Count;
            }

            // 如果是字符串，返回长度
            if (collection is string str)
            {
                return str.Length;
            }

            // 对于其他IEnumerable，使用LINQ Count
            if (collection is IEnumerable enumerable)
            {
                try
                {
                    return enumerable.Cast<object>().Count();
                }
                catch
                {
                    // 如果转换失败，尝试手动计数
                    var count = 0;
                    var enumerator = enumerable.GetEnumerator();
                    try
                    {
                        while (enumerator.MoveNext())
                        {
                            count++;
                        }
                    }
                    finally
                    {
                        if (enumerator is IDisposable disposable)
                        {
                            disposable.Dispose();
                        }
                    }
                    return count;
                }
            }

            // 如果不是集合类型，视为单个元素
            return 1;
        }

        /// <summary>
        /// 解析参数字符串
        /// </summary>
        /// <param name="parameter">参数字符串</param>
        /// <returns>解析结果</returns>
        private (bool isInverted, Visibility hiddenVisibility, int minCount, int? maxCount) ParseParameter(string parameter)
        {
            var isInverted = false;
            var hiddenVisibility = DefaultHiddenVisibility;
            var minCount = 1; // 默认最小数量为1（即非空）
            int? maxCount = null;

            if (string.IsNullOrWhiteSpace(parameter))
            {
                return (isInverted, hiddenVisibility, minCount, maxCount);
            }

            var lowerParam = parameter.ToLowerInvariant();

            // 检查反转标志
            if (lowerParam.Contains("invert"))
            {
                isInverted = true;
                minCount = 0; // 反转时默认最小数量为0（即空集合）
            }

            // 检查隐藏方式
            if (lowerParam.Contains("hidden"))
            {
                hiddenVisibility = Visibility.Hidden;
            }
            else if (lowerParam.Contains("collapsed"))
            {
                hiddenVisibility = Visibility.Collapsed;
            }

            // 解析最小数量
            var minCountMatch = System.Text.RegularExpressions.Regex.Match(parameter, @"mincount\s*=\s*(\d+)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            if (minCountMatch.Success && int.TryParse(minCountMatch.Groups[1].Value, out int parsedMinCount))
            {
                minCount = Math.Max(0, parsedMinCount);
            }

            // 解析最大数量
            var maxCountMatch = System.Text.RegularExpressions.Regex.Match(parameter, @"maxcount\s*=\s*(\d+)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            if (maxCountMatch.Success && int.TryParse(maxCountMatch.Groups[1].Value, out int parsedMaxCount))
            {
                maxCount = Math.Max(0, parsedMaxCount);
            }

            return (isInverted, hiddenVisibility, minCount, maxCount);
        }
    }
}
