<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 
    FluentSystemDesign WPF控件库 - 几何形状资源字典
    
    这个资源字典包含了控件库中使用的所有几何形状资源，
    按功能分类组织，支持矢量缩放，遵循Fluent Design System设计语言。
    
    分类：
    - 基础几何形状：圆形、矩形、三角形、多边形等
    - 图标几何形状：箭头、勾选、关闭、菜单等常用图标
    - 装饰几何形状：边框、分隔线、装饰元素等
    - 路径几何形状：复杂的矢量图形路径
    -->

    <ResourceDictionary.MergedDictionaries>
        <!-- 基础几何形状 -->
        <ResourceDictionary Source="BasicGeometries.xaml" />
        
        <!-- 图标几何形状 -->
        <ResourceDictionary Source="IconGeometries.xaml" />
        
        <!-- 装饰几何形状 -->
        <ResourceDictionary Source="DecorativeGeometries.xaml" />
        
        <!-- 路径几何形状 -->
        <ResourceDictionary Source="PathGeometries.xaml" />
    </ResourceDictionary.MergedDictionaries>

</ResourceDictionary>
