using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Microsoft.Xaml.Behaviors;

namespace FluentSystemDesign.WPF.Behaviors
{
    /// <summary>
    /// 焦点管理行为，提供高级的焦点管理功能
    /// </summary>
    public class FocusManagementBehavior : Behavior<FrameworkElement>
    {
        #region 依赖属性

        /// <summary>
        /// 是否在加载时自动获取焦点
        /// </summary>
        public static readonly DependencyProperty FocusOnLoadProperty =
            DependencyProperty.Register(nameof(FocusOnLoad), typeof(bool), typeof(FocusManagementBehavior),
                new PropertyMetadata(false));

        /// <summary>
        /// 是否在鼠标进入时获取焦点
        /// </summary>
        public static readonly DependencyProperty FocusOnMouseEnterProperty =
            DependencyProperty.Register(nameof(FocusOnMouseEnter), typeof(bool), typeof(FocusManagementBehavior),
                new PropertyMetadata(false));

        /// <summary>
        /// 是否选择所有文本（仅适用于TextBox）
        /// </summary>
        public static readonly DependencyProperty SelectAllOnFocusProperty =
            DependencyProperty.Register(nameof(SelectAllOnFocus), typeof(bool), typeof(FocusManagementBehavior),
                new PropertyMetadata(false));

        /// <summary>
        /// Tab键导航顺序
        /// </summary>
        public static readonly DependencyProperty TabIndexProperty =
            DependencyProperty.Register(nameof(TabIndex), typeof(int), typeof(FocusManagementBehavior),
                new PropertyMetadata(-1, OnTabIndexChanged));

        /// <summary>
        /// 是否参与Tab键导航
        /// </summary>
        public static readonly DependencyProperty IsTabStopProperty =
            DependencyProperty.Register(nameof(IsTabStop), typeof(bool), typeof(FocusManagementBehavior),
                new PropertyMetadata(true, OnIsTabStopChanged));

        /// <summary>
        /// 焦点延迟时间（毫秒）
        /// </summary>
        public static readonly DependencyProperty FocusDelayProperty =
            DependencyProperty.Register(nameof(FocusDelay), typeof(int), typeof(FocusManagementBehavior),
                new PropertyMetadata(0));

        /// <summary>
        /// 下一个焦点元素
        /// </summary>
        public static readonly DependencyProperty NextFocusElementProperty =
            DependencyProperty.Register(nameof(NextFocusElement), typeof(FrameworkElement), typeof(FocusManagementBehavior),
                new PropertyMetadata(null));

        /// <summary>
        /// 上一个焦点元素
        /// </summary>
        public static readonly DependencyProperty PreviousFocusElementProperty =
            DependencyProperty.Register(nameof(PreviousFocusElement), typeof(FrameworkElement), typeof(FocusManagementBehavior),
                new PropertyMetadata(null));

        #endregion

        #region 属性

        /// <summary>
        /// 获取或设置是否在加载时自动获取焦点
        /// </summary>
        public bool FocusOnLoad
        {
            get => (bool)GetValue(FocusOnLoadProperty);
            set => SetValue(FocusOnLoadProperty, value);
        }

        /// <summary>
        /// 获取或设置是否在鼠标进入时获取焦点
        /// </summary>
        public bool FocusOnMouseEnter
        {
            get => (bool)GetValue(FocusOnMouseEnterProperty);
            set => SetValue(FocusOnMouseEnterProperty, value);
        }

        /// <summary>
        /// 获取或设置是否选择所有文本
        /// </summary>
        public bool SelectAllOnFocus
        {
            get => (bool)GetValue(SelectAllOnFocusProperty);
            set => SetValue(SelectAllOnFocusProperty, value);
        }

        /// <summary>
        /// 获取或设置Tab键导航顺序
        /// </summary>
        public int TabIndex
        {
            get => (int)GetValue(TabIndexProperty);
            set => SetValue(TabIndexProperty, value);
        }

        /// <summary>
        /// 获取或设置是否参与Tab键导航
        /// </summary>
        public bool IsTabStop
        {
            get => (bool)GetValue(IsTabStopProperty);
            set => SetValue(IsTabStopProperty, value);
        }

        /// <summary>
        /// 获取或设置焦点延迟时间
        /// </summary>
        public int FocusDelay
        {
            get => (int)GetValue(FocusDelayProperty);
            set => SetValue(FocusDelayProperty, value);
        }

        /// <summary>
        /// 获取或设置下一个焦点元素
        /// </summary>
        public FrameworkElement NextFocusElement
        {
            get => (FrameworkElement)GetValue(NextFocusElementProperty);
            set => SetValue(NextFocusElementProperty, value);
        }

        /// <summary>
        /// 获取或设置上一个焦点元素
        /// </summary>
        public FrameworkElement PreviousFocusElement
        {
            get => (FrameworkElement)GetValue(PreviousFocusElementProperty);
            set => SetValue(PreviousFocusElementProperty, value);
        }

        #endregion

        #region 事件

        /// <summary>
        /// 获得焦点事件
        /// </summary>
        public event EventHandler<FocusEventArgs> FocusReceived;

        /// <summary>
        /// 失去焦点事件
        /// </summary>
        public event EventHandler<FocusEventArgs> FocusLost;

        #endregion

        #region 重写方法

        /// <summary>
        /// 附加到关联对象时调用
        /// </summary>
        protected override void OnAttached()
        {
            base.OnAttached();
            
            if (AssociatedObject != null)
            {
                AssociatedObject.Loaded += OnLoaded;
                AssociatedObject.GotFocus += OnGotFocus;
                AssociatedObject.LostFocus += OnLostFocus;
                AssociatedObject.MouseEnter += OnMouseEnter;
                AssociatedObject.KeyDown += OnKeyDown;
                
                // 应用Tab属性
                ApplyTabProperties();
            }
        }

        /// <summary>
        /// 从关联对象分离时调用
        /// </summary>
        protected override void OnDetaching()
        {
            if (AssociatedObject != null)
            {
                AssociatedObject.Loaded -= OnLoaded;
                AssociatedObject.GotFocus -= OnGotFocus;
                AssociatedObject.LostFocus -= OnLostFocus;
                AssociatedObject.MouseEnter -= OnMouseEnter;
                AssociatedObject.KeyDown -= OnKeyDown;
            }
            
            base.OnDetaching();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 设置焦点到关联对象
        /// </summary>
        public void SetFocus()
        {
            if (AssociatedObject != null)
            {
                if (FocusDelay > 0)
                {
                    var timer = new System.Windows.Threading.DispatcherTimer
                    {
                        Interval = TimeSpan.FromMilliseconds(FocusDelay)
                    };
                    timer.Tick += (s, e) =>
                    {
                        timer.Stop();
                        AssociatedObject.Focus();
                    };
                    timer.Start();
                }
                else
                {
                    AssociatedObject.Focus();
                }
            }
        }

        /// <summary>
        /// 移动焦点到下一个元素
        /// </summary>
        public void MoveFocusToNext()
        {
            if (NextFocusElement != null)
            {
                NextFocusElement.Focus();
            }
            else
            {
                var request = new TraversalRequest(FocusNavigationDirection.Next);
                AssociatedObject?.MoveFocus(request);
            }
        }

        /// <summary>
        /// 移动焦点到上一个元素
        /// </summary>
        public void MoveFocusToPrevious()
        {
            if (PreviousFocusElement != null)
            {
                PreviousFocusElement.Focus();
            }
            else
            {
                var request = new TraversalRequest(FocusNavigationDirection.Previous);
                AssociatedObject?.MoveFocus(request);
            }
        }

        #endregion

        #region 事件处理

        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            if (FocusOnLoad)
            {
                SetFocus();
            }
        }

        private void OnGotFocus(object sender, RoutedEventArgs e)
        {
            if (SelectAllOnFocus && AssociatedObject is TextBox textBox)
            {
                textBox.SelectAll();
            }
            
            FocusReceived?.Invoke(this, new FocusEventArgs(AssociatedObject));
        }

        private void OnLostFocus(object sender, RoutedEventArgs e)
        {
            FocusLost?.Invoke(this, new FocusEventArgs(AssociatedObject));
        }

        private void OnMouseEnter(object sender, System.Windows.Input.MouseEventArgs e)
        {
            if (FocusOnMouseEnter)
            {
                SetFocus();
            }
        }

        private void OnKeyDown(object sender, KeyEventArgs e)
        {
            switch (e.Key)
            {
                case Key.Tab:
                    if (Keyboard.Modifiers == ModifierKeys.Shift)
                    {
                        if (PreviousFocusElement != null)
                        {
                            e.Handled = true;
                            MoveFocusToPrevious();
                        }
                    }
                    else
                    {
                        if (NextFocusElement != null)
                        {
                            e.Handled = true;
                            MoveFocusToNext();
                        }
                    }
                    break;
                    
                case Key.Enter:
                    if (AssociatedObject is TextBox)
                    {
                        MoveFocusToNext();
                        e.Handled = true;
                    }
                    break;
            }
        }

        #endregion

        #region 私有方法

        private void ApplyTabProperties()
        {
            if (AssociatedObject != null)
            {
                if (TabIndex >= 0)
                {
                    KeyboardNavigation.SetTabIndex(AssociatedObject, TabIndex);
                }
                
                KeyboardNavigation.SetIsTabStop(AssociatedObject, IsTabStop);
            }
        }

        #endregion

        #region 依赖属性回调

        private static void OnTabIndexChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is FocusManagementBehavior behavior && behavior.AssociatedObject != null)
            {
                KeyboardNavigation.SetTabIndex(behavior.AssociatedObject, (int)e.NewValue);
            }
        }

        private static void OnIsTabStopChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is FocusManagementBehavior behavior && behavior.AssociatedObject != null)
            {
                KeyboardNavigation.SetIsTabStop(behavior.AssociatedObject, (bool)e.NewValue);
            }
        }

        #endregion
    }

    /// <summary>
    /// 焦点事件参数
    /// </summary>
    public class FocusEventArgs : EventArgs
    {
        /// <summary>
        /// 焦点元素
        /// </summary>
        public FrameworkElement Element { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="element">焦点元素</param>
        public FocusEventArgs(FrameworkElement element)
        {
            Element = element;
        }
    }
}
