{"version": 3, "targets": {"net8.0-windows7.0": {"Microsoft.Xaml.Behaviors.Wpf/1.1.77": {"type": "package", "compile": {"lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "FluentSystemDesign.WPF/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Microsoft.Xaml.Behaviors.Wpf": "1.1.77"}, "compile": {"bin/placeholder/FluentSystemDesign.WPF.dll": {}}, "runtime": {"bin/placeholder/FluentSystemDesign.WPF.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}}}, "libraries": {"Microsoft.Xaml.Behaviors.Wpf/1.1.77": {"sha512": "MCu674ZETgU18EbxfwIlRpUPJ02YbZenLsMCXTkpeA7KUBpXfFaOUDlEO+7UWu5AFnUoydg+aQENJkuaZPheMQ==", "type": "package", "path": "microsoft.xaml.behaviors.wpf/1.1.77", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Design/Microsoft.Xaml.Behaviors.Design.dll", "lib/net462/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/net462/Microsoft.Xaml.Behaviors.dll", "lib/net462/Microsoft.Xaml.Behaviors.pdb", "lib/net462/Microsoft.Xaml.Behaviors.xml", "lib/net6.0-windows7.0/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.dll", "lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.pdb", "lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.xml", "microsoft.xaml.behaviors.wpf.1.1.77.nupkg.sha512", "microsoft.xaml.behaviors.wpf.nuspec", "tools/Install.ps1"]}, "FluentSystemDesign.WPF/1.0.0": {"type": "project", "path": "../FluentSystemDesign.WPF/FluentSystemDesign.WPF.csproj", "msbuildProject": "../FluentSystemDesign.WPF/FluentSystemDesign.WPF.csproj"}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["FluentSystemDesign.WPF >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "D:\\Microsoft Visual Studio\\2022\\Shared\\NuGetPackages": {}, "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\05 FluentDesignWPF\\FluentSystemDesign.Examples\\FluentSystemDesign.Examples.csproj", "projectName": "FluentSystemDesign.Examples", "projectPath": "D:\\Project\\05 FluentDesignWPF\\FluentSystemDesign.Examples\\FluentSystemDesign.Examples.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\05 FluentDesignWPF\\FluentSystemDesign.Examples\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Microsoft Visual Studio\\2022\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"D:\\Project\\05 FluentDesignWPF\\FluentSystemDesign.WPF\\FluentSystemDesign.WPF.csproj": {"projectPath": "D:\\Project\\05 FluentDesignWPF\\FluentSystemDesign.WPF\\FluentSystemDesign.WPF.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}