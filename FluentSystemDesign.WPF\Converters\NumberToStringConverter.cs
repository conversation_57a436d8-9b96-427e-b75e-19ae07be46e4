using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace FluentSystemDesign.WPF.Converters
{
    /// <summary>
    /// 数值到字符串转换器
    /// 将数值转换为格式化的字符串，支持自定义格式
    /// </summary>
    /// <remarks>
    /// 支持标准数值格式字符串和自定义格式字符串
    /// 常用格式：
    /// - "N" 或 "N2": 数字格式（带千位分隔符）
    /// - "C" 或 "C2": 货币格式
    /// - "P" 或 "P2": 百分比格式
    /// - "F" 或 "F2": 固定点格式
    /// - "0.00": 自定义格式
    /// </remarks>
    /// <example>
    /// 在XAML中使用：
    /// <code>
    /// &lt;TextBlock Text="{Binding Price, Converter={StaticResource NumberToStringConverter}, ConverterParameter=C2}"/&gt;
    /// &lt;TextBlock Text="{Binding Percentage, Converter={StaticResource NumberToStringConverter}, ConverterParameter=P1}"/&gt;
    /// &lt;TextBlock Text="{Binding Count, Converter={StaticResource NumberToStringConverter}, ConverterParameter=N0}"/&gt;
    /// </code>
    /// </example>
    [ValueConversion(typeof(double), typeof(string))]
    [ValueConversion(typeof(int), typeof(string))]
    [ValueConversion(typeof(decimal), typeof(string))]
    [ValueConversion(typeof(float), typeof(string))]
    public class NumberToStringConverter : IValueConverter
    {
        /// <summary>
        /// 默认格式字符串
        /// </summary>
        public string DefaultFormat { get; set; } = "G";

        /// <summary>
        /// 将数值转换为格式化字符串
        /// </summary>
        /// <param name="value">数值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">格式字符串参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>格式化的字符串</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
            {
                return string.Empty;
            }

            var format = parameter?.ToString() ?? DefaultFormat;

            try
            {
                return value switch
                {
                    double d => d.ToString(format, culture),
                    float f => f.ToString(format, culture),
                    decimal dec => dec.ToString(format, culture),
                    int i => i.ToString(format, culture),
                    long l => l.ToString(format, culture),
                    short s => s.ToString(format, culture),
                    byte b => b.ToString(format, culture),
                    uint ui => ui.ToString(format, culture),
                    ulong ul => ul.ToString(format, culture),
                    ushort us => us.ToString(format, culture),
                    sbyte sb => sb.ToString(format, culture),
                    _ => value.ToString()
                };
            }
            catch (FormatException)
            {
                // 如果格式字符串无效，返回默认字符串表示
                return value.ToString();
            }
        }

        /// <summary>
        /// 将格式化字符串转换回数值
        /// </summary>
        /// <param name="value">格式化字符串</param>
        /// <param name="targetType">目标数值类型</param>
        /// <param name="parameter">格式字符串参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>数值</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not string stringValue || string.IsNullOrWhiteSpace(stringValue))
            {
                return DependencyProperty.UnsetValue;
            }

            // 移除可能的格式化字符（如货币符号、百分号等）
            var cleanString = CleanNumericString(stringValue, culture);

            try
            {
                if (targetType == typeof(double) || targetType == typeof(double?))
                {
                    return double.Parse(cleanString, NumberStyles.Any, culture);
                }
                else if (targetType == typeof(float) || targetType == typeof(float?))
                {
                    return float.Parse(cleanString, NumberStyles.Any, culture);
                }
                else if (targetType == typeof(decimal) || targetType == typeof(decimal?))
                {
                    return decimal.Parse(cleanString, NumberStyles.Any, culture);
                }
                else if (targetType == typeof(int) || targetType == typeof(int?))
                {
                    return int.Parse(cleanString, NumberStyles.Any, culture);
                }
                else if (targetType == typeof(long) || targetType == typeof(long?))
                {
                    return long.Parse(cleanString, NumberStyles.Any, culture);
                }
                else if (targetType == typeof(short) || targetType == typeof(short?))
                {
                    return short.Parse(cleanString, NumberStyles.Any, culture);
                }
                else if (targetType == typeof(byte) || targetType == typeof(byte?))
                {
                    return byte.Parse(cleanString, NumberStyles.Any, culture);
                }
            }
            catch (Exception ex) when (ex is FormatException || ex is OverflowException)
            {
                return DependencyProperty.UnsetValue;
            }

            return DependencyProperty.UnsetValue;
        }

        /// <summary>
        /// 清理数值字符串，移除格式化字符
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <param name="culture">文化信息</param>
        /// <returns>清理后的数值字符串</returns>
        private static string CleanNumericString(string input, CultureInfo culture)
        {
            var result = input.Trim();
            
            // 移除货币符号
            result = result.Replace(culture.NumberFormat.CurrencySymbol, "");
            
            // 移除百分号并调整值（如果包含百分号，需要除以100）
            var hasPercent = result.Contains(culture.NumberFormat.PercentSymbol);
            result = result.Replace(culture.NumberFormat.PercentSymbol, "");
            
            // 移除其他可能的符号
            result = result.Replace("(", "-").Replace(")", "");
            
            result = result.Trim();
            
            // 如果原来有百分号，需要将值除以100
            if (hasPercent && double.TryParse(result, NumberStyles.Any, culture, out double percentValue))
            {
                result = (percentValue / 100).ToString(culture);
            }
            
            return result;
        }
    }
}
