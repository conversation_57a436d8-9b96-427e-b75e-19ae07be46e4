using System;
using System.Collections;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Data;

namespace FluentSystemDesign.WPF.Converters
{
    /// <summary>
    /// 空值或空集合检查转换器
    /// 检查对象是否为null、空字符串或空集合，返回相应的布尔值
    /// </summary>
    /// <remarks>
    /// 检查规则：
    /// - null -> true
    /// - 空字符串 -> true
    /// - 空集合 -> true
    /// - 其他情况 -> false
    /// 
    /// 支持的参数：
    /// - "Invert": 反转结果
    /// - "TrimString": 对字符串进行Trim后再检查
    /// - "IgnoreWhitespace": 将只包含空白字符的字符串视为空
    /// </remarks>
    /// <example>
    /// 在XAML中使用：
    /// <code>
    /// &lt;Button IsEnabled="{Binding UserName, Converter={StaticResource IsNullOrEmptyConverter}, ConverterParameter=Invert}"/&gt;
    /// &lt;TextBlock Visibility="{Binding Items, Converter={StaticResource IsNullOrEmptyConverter}}" Text="无数据"/&gt;
    /// &lt;TextBox BorderBrush="Red" Visibility="{Binding ErrorMessage, Converter={StaticResource IsNullOrEmptyConverter}, ConverterParameter=Invert,IgnoreWhitespace}"/&gt;
    /// </code>
    /// </example>
    [ValueConversion(typeof(object), typeof(bool))]
    public class IsNullOrEmptyConverter : IValueConverter
    {
        /// <summary>
        /// 默认是否忽略空白字符
        /// </summary>
        public bool DefaultIgnoreWhitespace { get; set; } = false;

        /// <summary>
        /// 默认是否对字符串进行Trim
        /// </summary>
        public bool DefaultTrimString { get; set; } = false;

        /// <summary>
        /// 检查对象是否为null或空
        /// </summary>
        /// <param name="value">要检查的对象</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>是否为null或空</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var (isInverted, trimString, ignoreWhitespace) = ParseParameter(parameter?.ToString());

            var isEmpty = IsNullOrEmpty(value, trimString, ignoreWhitespace);

            var result = isInverted ? !isEmpty : isEmpty;

            // 如果目标类型是Visibility，转换为相应的可见性
            if (targetType == typeof(Visibility))
            {
                return result ? Visibility.Visible : Visibility.Collapsed;
            }

            return result;
        }

        /// <summary>
        /// 反向转换
        /// </summary>
        /// <param name="value">布尔值或可见性值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>转换结果</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var (isInverted, _, _) = ParseParameter(parameter?.ToString());

            bool boolValue;
            if (value is bool directBool)
            {
                boolValue = directBool;
            }
            else if (value is Visibility visibility)
            {
                boolValue = visibility == Visibility.Visible;
            }
            else
            {
                return DependencyProperty.UnsetValue;
            }

            // 应用反转逻辑
            var isEmpty = isInverted ? !boolValue : boolValue;

            // 根据目标类型返回相应的"空"值
            if (targetType == typeof(string))
            {
                return isEmpty ? string.Empty : "NotEmpty";
            }

            if (targetType.IsGenericType && targetType.GetGenericTypeDefinition() == typeof(System.Collections.Generic.List<>))
            {
                var listType = typeof(System.Collections.Generic.List<>).MakeGenericType(targetType.GetGenericArguments());
                return isEmpty ? Activator.CreateInstance(listType) : null;
            }

            return isEmpty ? null : new object();
        }

        /// <summary>
        /// 检查对象是否为null或空
        /// </summary>
        /// <param name="value">要检查的对象</param>
        /// <param name="trimString">是否对字符串进行Trim</param>
        /// <param name="ignoreWhitespace">是否忽略空白字符</param>
        /// <returns>是否为null或空</returns>
        private static bool IsNullOrEmpty(object value, bool trimString, bool ignoreWhitespace)
        {
            // null检查
            if (value == null)
            {
                return true;
            }

            // 字符串检查
            if (value is string stringValue)
            {
                if (trimString)
                {
                    stringValue = stringValue.Trim();
                }

                if (ignoreWhitespace)
                {
                    return string.IsNullOrWhiteSpace(stringValue);
                }
                else
                {
                    return string.IsNullOrEmpty(stringValue);
                }
            }

            // 集合检查
            if (value is ICollection collection)
            {
                return collection.Count == 0;
            }

            // 其他IEnumerable检查
            if (value is IEnumerable enumerable)
            {
                try
                {
                    return !enumerable.Cast<object>().Any();
                }
                catch
                {
                    // 如果Cast失败，尝试手动检查
                    var enumerator = enumerable.GetEnumerator();
                    try
                    {
                        return !enumerator.MoveNext();
                    }
                    finally
                    {
                        if (enumerator is IDisposable disposable)
                        {
                            disposable.Dispose();
                        }
                    }
                }
            }

            // 数值类型的特殊处理
            if (IsNumericType(value.GetType()))
            {
                return System.Convert.ToDouble(value) == 0;
            }

            // 其他类型视为非空
            return false;
        }

        /// <summary>
        /// 检查类型是否为数值类型
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>是否为数值类型</returns>
        private static bool IsNumericType(Type type)
        {
            return type == typeof(int) || type == typeof(double) || type == typeof(float) ||
                   type == typeof(decimal) || type == typeof(long) || type == typeof(short) ||
                   type == typeof(byte) || type == typeof(uint) || type == typeof(ulong) ||
                   type == typeof(ushort) || type == typeof(sbyte) ||
                   type == typeof(int?) || type == typeof(double?) || type == typeof(float?) ||
                   type == typeof(decimal?) || type == typeof(long?) || type == typeof(short?) ||
                   type == typeof(byte?) || type == typeof(uint?) || type == typeof(ulong?) ||
                   type == typeof(ushort?) || type == typeof(sbyte?);
        }

        /// <summary>
        /// 解析参数字符串
        /// </summary>
        /// <param name="parameter">参数字符串</param>
        /// <returns>解析结果</returns>
        private (bool isInverted, bool trimString, bool ignoreWhitespace) ParseParameter(string parameter)
        {
            var isInverted = false;
            var trimString = DefaultTrimString;
            var ignoreWhitespace = DefaultIgnoreWhitespace;

            if (string.IsNullOrWhiteSpace(parameter))
            {
                return (isInverted, trimString, ignoreWhitespace);
            }

            var lowerParam = parameter.ToLowerInvariant();

            // 检查各种标志
            if (lowerParam.Contains("invert"))
            {
                isInverted = true;
            }

            if (lowerParam.Contains("trimstring"))
            {
                trimString = true;
            }

            if (lowerParam.Contains("ignorewhitespace"))
            {
                ignoreWhitespace = true;
            }

            return (isInverted, trimString, ignoreWhitespace);
        }
    }
}
