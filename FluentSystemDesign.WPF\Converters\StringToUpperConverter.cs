using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace FluentSystemDesign.WPF.Converters
{
    /// <summary>
    /// 字符串转大写转换器
    /// 将字符串转换为大写形式
    /// </summary>
    /// <remarks>
    /// 支持的参数：
    /// - "Invariant": 使用不变区域性进行转换
    /// - "Current": 使用当前区域性进行转换（默认）
    /// - "FirstOnly": 只将首字母转换为大写
    /// - "Words": 将每个单词的首字母转换为大写（标题格式）
    /// </remarks>
    /// <example>
    /// 在XAML中使用：
    /// <code>
    /// &lt;TextBlock Text="{Binding UserName, Converter={StaticResource StringToUpperConverter}}"/&gt;
    /// &lt;TextBlock Text="{Binding Title, Converter={StaticResource StringToUpperConverter}, ConverterParameter=Words}"/&gt;
    /// &lt;TextBlock Text="{Binding Code, Converter={StaticResource StringToUpperConverter}, ConverterParameter=Invariant}"/&gt;
    /// </code>
    /// </example>
    [ValueConversion(typeof(string), typeof(string))]
    public class StringToUpperConverter : IValueConverter
    {
        /// <summary>
        /// 默认的转换模式
        /// </summary>
        public UpperCaseMode DefaultMode { get; set; } = UpperCaseMode.All;

        /// <summary>
        /// 默认是否使用不变区域性
        /// </summary>
        public bool UseInvariantCulture { get; set; } = false;

        /// <summary>
        /// 将字符串转换为大写
        /// </summary>
        /// <param name="value">字符串值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>大写字符串</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not string stringValue)
            {
                return value?.ToString() ?? string.Empty;
            }

            if (string.IsNullOrEmpty(stringValue))
            {
                return stringValue;
            }

            var (mode, useInvariant) = ParseParameter(parameter?.ToString());
            var cultureToUse = useInvariant ? CultureInfo.InvariantCulture : culture;

            return mode switch
            {
                UpperCaseMode.All => stringValue.ToUpper(cultureToUse),
                UpperCaseMode.FirstOnly => ConvertFirstLetterToUpper(stringValue, cultureToUse),
                UpperCaseMode.Words => ConvertToTitleCase(stringValue, cultureToUse),
                _ => stringValue.ToUpper(cultureToUse)
            };
        }

        /// <summary>
        /// 将大写字符串转换回原始形式（不支持完全恢复）
        /// </summary>
        /// <param name="value">大写字符串</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>转换后的字符串</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not string stringValue)
            {
                return DependencyProperty.UnsetValue;
            }

            if (string.IsNullOrEmpty(stringValue))
            {
                return stringValue;
            }

            var (mode, useInvariant) = ParseParameter(parameter?.ToString());
            var cultureToUse = useInvariant ? CultureInfo.InvariantCulture : culture;

            // 注意：ConvertBack无法完全恢复原始大小写，只能提供合理的转换
            return mode switch
            {
                UpperCaseMode.All => stringValue.ToLower(cultureToUse),
                UpperCaseMode.FirstOnly => ConvertFirstLetterToLower(stringValue, cultureToUse),
                UpperCaseMode.Words => stringValue.ToLower(cultureToUse),
                _ => stringValue.ToLower(cultureToUse)
            };
        }

        /// <summary>
        /// 将首字母转换为大写
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <param name="culture">文化信息</param>
        /// <returns>首字母大写的字符串</returns>
        private static string ConvertFirstLetterToUpper(string input, CultureInfo culture)
        {
            if (string.IsNullOrEmpty(input))
            {
                return input;
            }

            if (input.Length == 1)
            {
                return input.ToUpper(culture);
            }

            return char.ToUpper(input[0], culture) + input.Substring(1);
        }

        /// <summary>
        /// 将首字母转换为小写
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <param name="culture">文化信息</param>
        /// <returns>首字母小写的字符串</returns>
        private static string ConvertFirstLetterToLower(string input, CultureInfo culture)
        {
            if (string.IsNullOrEmpty(input))
            {
                return input;
            }

            if (input.Length == 1)
            {
                return input.ToLower(culture);
            }

            return char.ToLower(input[0], culture) + input.Substring(1);
        }

        /// <summary>
        /// 转换为标题格式（每个单词首字母大写）
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <param name="culture">文化信息</param>
        /// <returns>标题格式的字符串</returns>
        private static string ConvertToTitleCase(string input, CultureInfo culture)
        {
            if (string.IsNullOrEmpty(input))
            {
                return input;
            }

            return culture.TextInfo.ToTitleCase(input.ToLower(culture));
        }

        /// <summary>
        /// 解析参数字符串
        /// </summary>
        /// <param name="parameter">参数字符串</param>
        /// <returns>转换模式和区域性设置</returns>
        private (UpperCaseMode mode, bool useInvariant) ParseParameter(string parameter)
        {
            var mode = DefaultMode;
            var useInvariant = UseInvariantCulture;

            if (string.IsNullOrWhiteSpace(parameter))
            {
                return (mode, useInvariant);
            }

            var lowerParam = parameter.ToLowerInvariant();

            // 解析转换模式
            if (lowerParam.Contains("firstonly"))
            {
                mode = UpperCaseMode.FirstOnly;
            }
            else if (lowerParam.Contains("words"))
            {
                mode = UpperCaseMode.Words;
            }
            else if (lowerParam.Contains("all"))
            {
                mode = UpperCaseMode.All;
            }

            // 解析区域性设置
            if (lowerParam.Contains("invariant"))
            {
                useInvariant = true;
            }
            else if (lowerParam.Contains("current"))
            {
                useInvariant = false;
            }

            return (mode, useInvariant);
        }

        /// <summary>
        /// 大写转换模式
        /// </summary>
        public enum UpperCaseMode
        {
            /// <summary>
            /// 全部转换为大写
            /// </summary>
            All,

            /// <summary>
            /// 只将首字母转换为大写
            /// </summary>
            FirstOnly,

            /// <summary>
            /// 将每个单词的首字母转换为大写
            /// </summary>
            Words
        }
    }
}
