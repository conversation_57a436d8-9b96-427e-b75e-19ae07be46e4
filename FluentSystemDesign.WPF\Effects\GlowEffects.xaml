<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ========================================== -->
    <!-- Fluent Design System Glow Effects -->
    <!-- For emphasis and interaction feedback -->
    <!-- ========================================== -->

    <!-- Subtle Glow: Light emphasis -->
    <DropShadowEffect x:Key="SubtleGlow" 
                      ShadowDepth="0" 
                      BlurRadius="4" 
                      Opacity="0.3" 
                      Color="#2196F3"/>

    <!-- Light Glow: Gentle emphasis -->
    <DropShadowEffect x:Key="LightGlow" 
                      ShadowDepth="0" 
                      BlurRadius="8" 
                      Opacity="0.4" 
                      Color="#2196F3"/>

    <!-- Medium Glow: Standard emphasis -->
    <DropShadowEffect x:Key="MediumGlow" 
                      ShadowDepth="0" 
                      BlurRadius="12" 
                      Opacity="0.5" 
                      Color="#2196F3"/>

    <!-- Strong Glow: Heavy emphasis -->
    <DropShadowEffect x:Key="StrongGlow" 
                      ShadowDepth="0" 
                      BlurRadius="16" 
                      Opacity="0.6" 
                      Color="#2196F3"/>

    <!-- ========================================== -->
    <!-- Colored Glow Effects -->
    <!-- ========================================== -->

    <!-- Primary Glow: Using primary color -->
    <DropShadowEffect x:Key="PrimaryGlow" 
                      ShadowDepth="0" 
                      BlurRadius="10" 
                      Opacity="0.5" 
                      Color="#2196F3"/>

    <!-- Secondary Glow: Using secondary color -->
    <DropShadowEffect x:Key="SecondaryGlow" 
                      ShadowDepth="0" 
                      BlurRadius="10" 
                      Opacity="0.5" 
                      Color="#9C27B0"/>

    <!-- Accent Glow: Using accent color -->
    <DropShadowEffect x:Key="AccentGlow" 
                      ShadowDepth="0" 
                      BlurRadius="10" 
                      Opacity="0.5" 
                      Color="#FF9800"/>

    <!-- ========================================== -->
    <!-- Semantic Glow Effects -->
    <!-- ========================================== -->

    <!-- Success Glow: For success states -->
    <DropShadowEffect x:Key="SuccessGlow" 
                      ShadowDepth="0" 
                      BlurRadius="8" 
                      Opacity="0.4" 
                      Color="#4CAF50"/>

    <!-- Warning Glow: For warning states -->
    <DropShadowEffect x:Key="WarningGlow" 
                      ShadowDepth="0" 
                      BlurRadius="8" 
                      Opacity="0.4" 
                      Color="#FF8F00"/>

    <!-- Error Glow: For error states -->
    <DropShadowEffect x:Key="ErrorGlow" 
                      ShadowDepth="0" 
                      BlurRadius="8" 
                      Opacity="0.4" 
                      Color="#F44336"/>

    <!-- Info Glow: For info states -->
    <DropShadowEffect x:Key="InfoGlow" 
                      ShadowDepth="0" 
                      BlurRadius="8" 
                      Opacity="0.4" 
                      Color="#00BCD4"/>

    <!-- ========================================== -->
    <!-- Interactive Glow Effects -->
    <!-- ========================================== -->

    <!-- Focus Glow: For focused elements -->
    <DropShadowEffect x:Key="FocusGlow" 
                      ShadowDepth="0" 
                      BlurRadius="6" 
                      Opacity="0.6" 
                      Color="#2196F3"/>

    <!-- Hover Glow: For hover states -->
    <DropShadowEffect x:Key="HoverGlow" 
                      ShadowDepth="0" 
                      BlurRadius="8" 
                      Opacity="0.4" 
                      Color="#2196F3"/>

    <!-- Active Glow: For active/pressed states -->
    <DropShadowEffect x:Key="ActiveGlow" 
                      ShadowDepth="0" 
                      BlurRadius="4" 
                      Opacity="0.7" 
                      Color="#2196F3"/>

    <!-- Selection Glow: For selected elements -->
    <DropShadowEffect x:Key="SelectionGlow" 
                      ShadowDepth="0" 
                      BlurRadius="10" 
                      Opacity="0.5" 
                      Color="#2196F3"/>

    <!-- ========================================== -->
    <!-- Special Glow Effects -->
    <!-- ========================================== -->

    <!-- Notification Glow: For notifications -->
    <DropShadowEffect x:Key="NotificationGlow" 
                      ShadowDepth="0" 
                      BlurRadius="12" 
                      Opacity="0.6" 
                      Color="#FF9800"/>

    <!-- Badge Glow: For badges and indicators -->
    <DropShadowEffect x:Key="BadgeGlow" 
                      ShadowDepth="0" 
                      BlurRadius="6" 
                      Opacity="0.5" 
                      Color="#F44336"/>

    <!-- Loading Glow: For loading indicators -->
    <DropShadowEffect x:Key="LoadingGlow" 
                      ShadowDepth="0" 
                      BlurRadius="8" 
                      Opacity="0.4" 
                      Color="#2196F3"/>

    <!-- ========================================== -->
    <!-- White Glow Effects (for dark themes) -->
    <!-- ========================================== -->

    <!-- White Subtle Glow: Light white emphasis -->
    <DropShadowEffect x:Key="WhiteSubtleGlow" 
                      ShadowDepth="0" 
                      BlurRadius="4" 
                      Opacity="0.2" 
                      Color="White"/>

    <!-- White Light Glow: Gentle white emphasis -->
    <DropShadowEffect x:Key="WhiteLightGlow" 
                      ShadowDepth="0" 
                      BlurRadius="8" 
                      Opacity="0.3" 
                      Color="White"/>

    <!-- White Medium Glow: Standard white emphasis -->
    <DropShadowEffect x:Key="WhiteMediumGlow" 
                      ShadowDepth="0" 
                      BlurRadius="12" 
                      Opacity="0.4" 
                      Color="White"/>

</ResourceDictionary>
