using System;
using System.Windows;
using System.Windows.Input;
using Microsoft.Xaml.Behaviors;

namespace FluentSystemDesign.WPF.Behaviors
{
    /// <summary>
    /// 拖拽行为，为UI元素提供拖拽功能
    /// </summary>
    public class DragBehavior : Behavior<FrameworkElement>
    {
        #region 依赖属性

        /// <summary>
        /// 是否启用拖拽
        /// </summary>
        public static readonly DependencyProperty IsEnabledProperty =
            DependencyProperty.Register(nameof(IsEnabled), typeof(bool), typeof(DragBehavior),
                new PropertyMetadata(true));

        /// <summary>
        /// 拖拽数据
        /// </summary>
        public static readonly DependencyProperty DragDataProperty =
            DependencyProperty.Register(nameof(DragData), typeof(object), typeof(DragBehavior),
                new PropertyMetadata(null));

        /// <summary>
        /// 拖拽效果
        /// </summary>
        public static readonly DependencyProperty DragEffectsProperty =
            DependencyProperty.Register(nameof(DragEffects), typeof(DragDropEffects), typeof(DragBehavior),
                new PropertyMetadata(DragDropEffects.Move));

        /// <summary>
        /// 拖拽开始阈值（像素）
        /// </summary>
        public static readonly DependencyProperty DragThresholdProperty =
            DependencyProperty.Register(nameof(DragThreshold), typeof(double), typeof(DragBehavior),
                new PropertyMetadata(5.0));

        #endregion

        #region 属性

        /// <summary>
        /// 获取或设置是否启用拖拽
        /// </summary>
        public bool IsEnabled
        {
            get => (bool)GetValue(IsEnabledProperty);
            set => SetValue(IsEnabledProperty, value);
        }

        /// <summary>
        /// 获取或设置拖拽数据
        /// </summary>
        public object DragData
        {
            get => GetValue(DragDataProperty);
            set => SetValue(DragDataProperty, value);
        }

        /// <summary>
        /// 获取或设置拖拽效果
        /// </summary>
        public DragDropEffects DragEffects
        {
            get => (DragDropEffects)GetValue(DragEffectsProperty);
            set => SetValue(DragEffectsProperty, value);
        }

        /// <summary>
        /// 获取或设置拖拽开始阈值
        /// </summary>
        public double DragThreshold
        {
            get => (double)GetValue(DragThresholdProperty);
            set => SetValue(DragThresholdProperty, value);
        }

        #endregion

        #region 事件

        /// <summary>
        /// 拖拽开始事件
        /// </summary>
        public event EventHandler<DragStartedEventArgs> DragStarted;

        /// <summary>
        /// 拖拽完成事件
        /// </summary>
        public event EventHandler<DragCompletedEventArgs> DragCompleted;

        #endregion

        #region 私有字段

        private bool _isDragging;
        private Point _startPoint;

        #endregion

        #region 重写方法

        /// <summary>
        /// 附加到关联对象时调用
        /// </summary>
        protected override void OnAttached()
        {
            base.OnAttached();
            
            if (AssociatedObject != null)
            {
                AssociatedObject.MouseLeftButtonDown += OnMouseLeftButtonDown;
                AssociatedObject.MouseMove += OnMouseMove;
                AssociatedObject.MouseLeftButtonUp += OnMouseLeftButtonUp;
            }
        }

        /// <summary>
        /// 从关联对象分离时调用
        /// </summary>
        protected override void OnDetaching()
        {
            if (AssociatedObject != null)
            {
                AssociatedObject.MouseLeftButtonDown -= OnMouseLeftButtonDown;
                AssociatedObject.MouseMove -= OnMouseMove;
                AssociatedObject.MouseLeftButtonUp -= OnMouseLeftButtonUp;
            }
            
            base.OnDetaching();
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 鼠标左键按下事件处理
        /// </summary>
        private void OnMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (!IsEnabled)
                return;

            _startPoint = e.GetPosition(AssociatedObject);
            _isDragging = false;
        }

        /// <summary>
        /// 鼠标移动事件处理
        /// </summary>
        private void OnMouseMove(object sender, MouseEventArgs e)
        {
            if (!IsEnabled || e.LeftButton != MouseButtonState.Pressed)
                return;

            var currentPoint = e.GetPosition(AssociatedObject);
            var distance = Math.Sqrt(Math.Pow(currentPoint.X - _startPoint.X, 2) + 
                                   Math.Pow(currentPoint.Y - _startPoint.Y, 2));

            if (!_isDragging && distance >= DragThreshold)
            {
                _isDragging = true;
                StartDrag();
            }
        }

        /// <summary>
        /// 鼠标左键释放事件处理
        /// </summary>
        private void OnMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            _isDragging = false;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 开始拖拽操作
        /// </summary>
        private void StartDrag()
        {
            var dragData = DragData ?? AssociatedObject.DataContext;
            var eventArgs = new DragStartedEventArgs(dragData);
            
            DragStarted?.Invoke(this, eventArgs);
            
            if (!eventArgs.Cancel)
            {
                var dataObject = new DataObject(dragData);
                var result = DragDrop.DoDragDrop(AssociatedObject, dataObject, DragEffects);
                
                DragCompleted?.Invoke(this, new DragCompletedEventArgs(dragData, result));
            }
        }

        #endregion
    }

    /// <summary>
    /// 拖拽开始事件参数
    /// </summary>
    public class DragStartedEventArgs : EventArgs
    {
        /// <summary>
        /// 拖拽数据
        /// </summary>
        public object Data { get; }

        /// <summary>
        /// 是否取消拖拽
        /// </summary>
        public bool Cancel { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="data">拖拽数据</param>
        public DragStartedEventArgs(object data)
        {
            Data = data;
        }
    }

    /// <summary>
    /// 拖拽完成事件参数
    /// </summary>
    public class DragCompletedEventArgs : EventArgs
    {
        /// <summary>
        /// 拖拽数据
        /// </summary>
        public object Data { get; }

        /// <summary>
        /// 拖拽结果
        /// </summary>
        public DragDropEffects Result { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="data">拖拽数据</param>
        /// <param name="result">拖拽结果</param>
        public DragCompletedEventArgs(object data, DragDropEffects result)
        {
            Data = data;
            Result = result;
        }
    }
}
