using System.Linq;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Shapes;

namespace FluentSystemDesign.Examples.Pages.Examples
{
    /// <summary>
    /// 几何形状展示页面
    /// 展示FluentSystemDesign WPF控件库中的所有几何形状资源
    /// </summary>
    public partial class GeometriesDemoPage : Page
    {
        public GeometriesDemoPage()
        {
            InitializeComponent();
            InitializeEventHandlers();
        }

        /// <summary>
        /// 初始化事件处理程序
        /// </summary>
        private void InitializeEventHandlers()
        {
            // 为所有几何形状项添加点击事件处理程序
            this.Loaded += (s, e) => AttachClickHandlers();
        }

        /// <summary>
        /// 为几何形状项附加点击事件处理程序
        /// </summary>
        private void AttachClickHandlers()
        {
            // 查找所有几何形状展示项
            var geometryItems = FindVisualChildren<Border>(this)
                .Where(b => b.Style?.Equals(FindResource("GeometryItemStyle")) == true);

            foreach (var item in geometryItems)
            {
                item.MouseLeftButtonUp += OnGeometryItemClick;
                item.Cursor = Cursors.Hand;
            }
        }

        /// <summary>
        /// 几何形状项点击事件处理程序
        /// </summary>
        private void OnGeometryItemClick(object sender, MouseButtonEventArgs e)
        {
            if (sender is Border border && border.Child is StackPanel stackPanel)
            {
                var path = stackPanel.Children.OfType<Path>().FirstOrDefault();
                var textBlock = stackPanel.Children.OfType<TextBlock>().FirstOrDefault();

                if (path?.Data != null && textBlock?.Text != null)
                {
                    // 复制XAML代码到剪贴板
                    var xamlCode = GenerateXamlCode(textBlock.Text, path);
                    CopyToClipboard(xamlCode);

                    // 显示复制成功提示
                    ShowCopySuccessMessage(textBlock.Text);
                }
            }
        }

        /// <summary>
        /// 生成XAML代码
        /// </summary>
        private string GenerateXamlCode(string geometryName, Path path)
        {
            var hasStroke = path.Stroke != null;
            var hasFill = path.Fill != null && path.Fill != Brushes.Transparent;

            var xaml = $"<Path Data=\"{{StaticResource {geometryName}}}\"";
            
            if (hasFill)
            {
                xaml += "\n      Fill=\"{StaticResource AccentBrush}\"";
            }
            
            if (hasStroke)
            {
                xaml += "\n      Stroke=\"{StaticResource AccentBrush}\"";
                xaml += "\n      StrokeThickness=\"2\"";
                if (!hasFill)
                {
                    xaml += "\n      Fill=\"Transparent\"";
                }
            }
            
            xaml += "\n      Stretch=\"Uniform\"";
            xaml += "\n      Width=\"24\" Height=\"24\" />";

            return xaml;
        }

        /// <summary>
        /// 复制文本到剪贴板
        /// </summary>
        private void CopyToClipboard(string text)
        {
            try
            {
                Clipboard.SetText(text);
            }
            catch (System.Exception ex)
            {
                // 处理剪贴板访问异常
                System.Diagnostics.Debug.WriteLine($"复制到剪贴板失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示复制成功消息
        /// </summary>
        private void ShowCopySuccessMessage(string geometryName)
        {
            // 创建临时提示消息
            var messagePanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Background = (Brush)FindResource("SuccessBrush"),
                Margin = new Thickness(24, 16, 24, 0),
                HorizontalAlignment = HorizontalAlignment.Center
            };

            var messageText = new TextBlock
            {
                Text = $"已复制 {geometryName} 的XAML代码到剪贴板",
                Foreground = Brushes.White,
                Padding = new Thickness(16, 8, 16, 8),
                FontWeight = FontWeights.Medium
            };

            messagePanel.Children.Add(messageText);

            // 将消息添加到页面顶部
            if (this.Content is Grid mainGrid && mainGrid.Children.Count > 0)
            {
                var firstChild = mainGrid.Children[0];
                if (firstChild is Border titleBorder)
                {
                    var titleContent = titleBorder.Child;
                    var newContent = new StackPanel();
                    newContent.Children.Add(titleContent);
                    newContent.Children.Add(messagePanel);
                    titleBorder.Child = newContent;

                    // 3秒后移除消息
                    var timer = new System.Windows.Threading.DispatcherTimer
                    {
                        Interval = System.TimeSpan.FromSeconds(3)
                    };
                    timer.Tick += (s, e) =>
                    {
                        timer.Stop();
                        newContent.Children.Remove(messagePanel);
                        titleBorder.Child = titleContent;
                    };
                    timer.Start();
                }
            }
        }

        /// <summary>
        /// 查找可视化树中的子元素
        /// </summary>
        private static IEnumerable<T> FindVisualChildren<T>(DependencyObject depObj) where T : DependencyObject
        {
            if (depObj != null)
            {
                for (int i = 0; i < VisualTreeHelper.GetChildrenCount(depObj); i++)
                {
                    DependencyObject child = VisualTreeHelper.GetChild(depObj, i);
                    if (child != null && child is T)
                    {
                        yield return (T)child;
                    }

                    foreach (T childOfChild in FindVisualChildren<T>(child))
                    {
                        yield return childOfChild;
                    }
                }
            }
        }
    }
}
