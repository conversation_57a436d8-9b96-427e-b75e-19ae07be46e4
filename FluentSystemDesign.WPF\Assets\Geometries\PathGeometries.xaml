<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 
    路径几何形状资源字典
    包含复杂的矢量图形路径和组合几何形状
    用于创建复杂的UI元素和图标，遵循Fluent Design System设计语言
    -->

    <!-- 复杂图标路径 -->
    
    <!-- 云朵图标 -->
    <PathGeometry x:Key="CloudGeometry">
        <PathFigure StartPoint="18.5,10" IsClosed="True">
            <BezierSegment Point1="18.5,6.5" Point2="15.5,4" Point3="12,4" />
            <BezierSegment Point1="8.5,4" Point2="5.5,6.5" Point3="5.5,10" />
            <BezierSegment Point1="3,10" Point2="1,12" Point3="1,14.5" />
            <BezierSegment Point1="1,17" Point2="3,19" Point3="5.5,19" />
            <LineSegment Point="18.5,19" />
            <BezierSegment Point1="21,19" Point2="23,17" Point3="23,14.5" />
            <BezierSegment Point1="23,12" Point2="21,10" Point3="18.5,10" />
        </PathFigure>
    </PathGeometry>

    <!-- 邮件图标 -->
    <PathGeometry x:Key="MailGeometry">
        <PathFigure StartPoint="4,4" IsClosed="True">
            <LineSegment Point="20,4" />
            <LineSegment Point="20,18" />
            <LineSegment Point="4,18" />
        </PathFigure>
        <PathFigure StartPoint="4,4">
            <LineSegment Point="12,12" />
            <LineSegment Point="20,4" />
        </PathFigure>
    </PathGeometry>

    <!-- 电话图标 -->
    <PathGeometry x:Key="PhoneGeometry">
        <PathFigure StartPoint="22,16.92" IsClosed="True">
            <BezierSegment Point1="22,18.32" Point2="20.96,19.5" Point3="19.59,19.5" />
            <BezierSegment Point1="15.4,19.5" Point2="11.26,17.93" Point3="7.5,14.17" />
            <BezierSegment Point1="3.74,10.41" Point2="2.17,6.27" Point3="2.17,2.08" />
            <BezierSegment Point1="2.17,0.71" Point2="3.35,-0.33" Point3="4.75,-0.33" />
            <LineSegment Point="8.33,1.25" />
            <BezierSegment Point1="9.73,1.25" Point2="10.77,2.29" Point3="10.77,3.69" />
            <LineSegment Point="10.77,6.23" />
            <BezierSegment Point1="10.77,7.63" Point2="9.73,8.67" Point3="8.33,8.67" />
            <LineSegment Point="6.23,8.67" />
            <BezierSegment Point1="7.63,10.77" Point2="9.73,12.87" Point3="11.83,14.27" />
            <LineSegment Point="11.83,12.17" />
            <BezierSegment Point1="11.83,10.77" Point2="12.87,9.73" Point3="14.27,9.73" />
            <LineSegment Point="16.81,9.73" />
            <BezierSegment Point1="18.21,9.73" Point2="19.25,10.77" Point3="19.25,12.17" />
            <LineSegment Point="21.83,15.75" />
            <BezierSegment Point1="21.83,17.15" Point2="22.87,18.19" Point3="22,16.92" />
        </PathFigure>
    </PathGeometry>

    <!-- 日历图标 -->
    <PathGeometry x:Key="CalendarGeometry">
        <PathFigure StartPoint="19,3" IsClosed="True">
            <LineSegment Point="5,3" />
            <BezierSegment Point1="3.9,3" Point2="3,3.9" Point3="3,5" />
            <LineSegment Point="3,19" />
            <BezierSegment Point1="3,20.1" Point2="3.9,21" Point3="5,21" />
            <LineSegment Point="19,21" />
            <BezierSegment Point1="20.1,21" Point2="21,20.1" Point3="21,19" />
            <LineSegment Point="21,5" />
            <BezierSegment Point1="21,3.9" Point2="20.1,3" Point3="19,3" />
        </PathFigure>
        <PathFigure StartPoint="16,1">
            <LineSegment Point="16,5" />
        </PathFigure>
        <PathFigure StartPoint="8,1">
            <LineSegment Point="8,5" />
        </PathFigure>
        <PathFigure StartPoint="3,9">
            <LineSegment Point="21,9" />
        </PathFigure>
    </PathGeometry>

    <!-- 时钟图标 -->
    <PathGeometry x:Key="ClockGeometry">
        <PathFigure StartPoint="12,2" IsClosed="True">
            <ArcSegment Point="12,2" Size="10,10" SweepDirection="Clockwise" IsLargeArc="True" />
        </PathFigure>
        <PathFigure StartPoint="12,6">
            <LineSegment Point="12,12" />
            <LineSegment Point="16,12" />
        </PathFigure>
    </PathGeometry>

    <!-- 位置图标 -->
    <PathGeometry x:Key="LocationGeometry">
        <PathFigure StartPoint="21,10" IsClosed="True">
            <BezierSegment Point1="21,16" Point2="12,23" Point3="12,23" />
            <BezierSegment Point1="12,23" Point2="3,16" Point3="3,10" />
            <BezierSegment Point1="3,5.03" Point2="7.03,1" Point3="12,1" />
            <BezierSegment Point1="16.97,1" Point2="21,5.03" Point3="21,10" />
        </PathFigure>
        <PathFigure StartPoint="12,13" IsClosed="True">
            <ArcSegment Point="12,13" Size="3,3" SweepDirection="Clockwise" IsLargeArc="True" />
        </PathFigure>
    </PathGeometry>

    <!-- 购物车图标 -->
    <PathGeometry x:Key="ShoppingCartGeometry">
        <PathFigure StartPoint="9,19" IsClosed="True">
            <ArcSegment Point="9,19" Size="1,1" SweepDirection="Clockwise" IsLargeArc="True" />
        </PathFigure>
        <PathFigure StartPoint="20,19" IsClosed="True">
            <ArcSegment Point="20,19" Size="1,1" SweepDirection="Clockwise" IsLargeArc="True" />
        </PathFigure>
        <PathFigure StartPoint="1,1">
            <LineSegment Point="3,1" />
            <LineSegment Point="6,13" />
            <LineSegment Point="17,13" />
            <LineSegment Point="20,5" />
            <LineSegment Point="6,5" />
        </PathFigure>
    </PathGeometry>

    <!-- 心跳图标 -->
    <PathGeometry x:Key="HeartbeatGeometry">
        <PathFigure StartPoint="22,12">
            <LineSegment Point="18,12" />
            <LineSegment Point="15,21" />
            <LineSegment Point="13,3" />
            <LineSegment Point="11,12" />
            <LineSegment Point="9,12" />
            <LineSegment Point="7,18" />
            <LineSegment Point="5,6" />
            <LineSegment Point="3,12" />
            <LineSegment Point="2,12" />
        </PathFigure>
    </PathGeometry>

    <!-- 音乐图标 -->
    <PathGeometry x:Key="MusicGeometry">
        <PathFigure StartPoint="9,18" IsClosed="True">
            <ArcSegment Point="9,18" Size="3,3" SweepDirection="Clockwise" IsLargeArc="True" />
        </PathFigure>
        <PathFigure StartPoint="21,18" IsClosed="True">
            <ArcSegment Point="21,18" Size="3,3" SweepDirection="Clockwise" IsLargeArc="True" />
        </PathFigure>
        <PathFigure StartPoint="12,2">
            <LineSegment Point="12,15" />
        </PathFigure>
        <PathFigure StartPoint="12,6">
            <LineSegment Point="21,4" />
            <LineSegment Point="21,15" />
        </PathFigure>
    </PathGeometry>

    <!-- 相机图标 -->
    <PathGeometry x:Key="CameraGeometry">
        <PathFigure StartPoint="23,19" IsClosed="True">
            <LineSegment Point="1,19" />
            <BezierSegment Point1="0.45,19" Point2="0,18.55" Point3="0,18" />
            <LineSegment Point="0,8" />
            <BezierSegment Point1="0,7.45" Point2="0.45,7" Point3="1,7" />
            <LineSegment Point="5.17,7" />
            <LineSegment Point="7.59,3.59" />
            <BezierSegment Point1="7.78,3.4" Point2="8.05,3.29" Point3="8.34,3.29" />
            <LineSegment Point="15.66,3.29" />
            <BezierSegment Point1="15.95,3.29" Point2="16.22,3.4" Point3="16.41,3.59" />
            <LineSegment Point="18.83,7" />
            <LineSegment Point="23,7" />
            <BezierSegment Point1="23.55,7" Point2="24,7.45" Point3="24,8" />
            <LineSegment Point="24,18" />
            <BezierSegment Point1="24,18.55" Point2="23.55,19" Point3="23,19" />
        </PathFigure>
        <PathFigure StartPoint="12,16" IsClosed="True">
            <ArcSegment Point="12,16" Size="4,4" SweepDirection="Clockwise" IsLargeArc="True" />
        </PathFigure>
    </PathGeometry>

    <!-- 视频图标 -->
    <PathGeometry x:Key="VideoGeometry">
        <PathFigure StartPoint="23,7" IsClosed="True">
            <LineSegment Point="16,12" />
            <LineSegment Point="23,17" />
        </PathFigure>
        <PathFigure StartPoint="14,5" IsClosed="True">
            <LineSegment Point="3,5" />
            <BezierSegment Point1="1.9,5" Point2="1,5.9" Point3="1,7" />
            <LineSegment Point="1,17" />
            <BezierSegment Point1="1,18.1" Point2="1.9,19" Point3="3,19" />
            <LineSegment Point="14,19" />
            <BezierSegment Point1="15.1,19" Point2="16,18.1" Point3="16,17" />
            <LineSegment Point="16,7" />
            <BezierSegment Point1="16,5.9" Point2="15.1,5" Point3="14,5" />
        </PathFigure>
    </PathGeometry>

    <!-- 图片图标 -->
    <PathGeometry x:Key="ImageGeometry">
        <PathFigure StartPoint="21,19" IsClosed="True">
            <LineSegment Point="3,19" />
            <BezierSegment Point1="1.9,19" Point2="1,18.1" Point3="1,17" />
            <LineSegment Point="1,7" />
            <BezierSegment Point1="1,5.9" Point2="1.9,5" Point3="3,5" />
            <LineSegment Point="21,5" />
            <BezierSegment Point1="22.1,5" Point2="23,5.9" Point3="23,7" />
            <LineSegment Point="23,17" />
            <BezierSegment Point1="23,18.1" Point2="22.1,19" Point3="21,19" />
        </PathFigure>
        <PathFigure StartPoint="8.5,13.5" IsClosed="True">
            <ArcSegment Point="8.5,13.5" Size="1.5,1.5" SweepDirection="Clockwise" IsLargeArc="True" />
        </PathFigure>
        <PathFigure StartPoint="21,15">
            <LineSegment Point="16,10" />
            <LineSegment Point="5,21" />
        </PathFigure>
    </PathGeometry>

    <!-- 下载图标 -->
    <PathGeometry x:Key="DownloadGeometry">
        <PathFigure StartPoint="21,15">
            <LineSegment Point="21,19" />
            <BezierSegment Point1="21,20.1" Point2="20.1,21" Point3="19,21" />
            <LineSegment Point="5,21" />
            <BezierSegment Point1="3.9,21" Point2="3,20.1" Point3="3,19" />
            <LineSegment Point="3,15" />
        </PathFigure>
        <PathFigure StartPoint="7,10">
            <LineSegment Point="12,15" />
            <LineSegment Point="17,10" />
        </PathFigure>
        <PathFigure StartPoint="12,15">
            <LineSegment Point="12,3" />
        </PathFigure>
    </PathGeometry>

    <!-- 上传图标 -->
    <PathGeometry x:Key="UploadGeometry">
        <PathFigure StartPoint="21,15">
            <LineSegment Point="21,19" />
            <BezierSegment Point1="21,20.1" Point2="20.1,21" Point3="19,21" />
            <LineSegment Point="5,21" />
            <BezierSegment Point1="3.9,21" Point2="3,20.1" Point3="3,19" />
            <LineSegment Point="3,15" />
        </PathFigure>
        <PathFigure StartPoint="17,8">
            <LineSegment Point="12,3" />
            <LineSegment Point="7,8" />
        </PathFigure>
        <PathFigure StartPoint="12,3">
            <LineSegment Point="12,15" />
        </PathFigure>
    </PathGeometry>

    <!-- 分享图标 -->
    <PathGeometry x:Key="ShareGeometry">
        <PathFigure StartPoint="18,16.08" IsClosed="True">
            <BezierSegment Point1="17.24,16.08" Point2="16.56,16.3" Point3="16,16.68" />
            <LineSegment Point="8.91,12.7" />
            <BezierSegment Point1="9.57,11.81" Point2="10,10.73" Point3="10,9.54" />
            <BezierSegment Point1="10,8.35" Point2="9.57,7.27" Point3="8.91,6.38" />
            <LineSegment Point="16,2.4" />
            <BezierSegment Point1="16.56,2.78" Point2="17.24,3" Point3="18,3" />
            <BezierSegment Point1="20.21,3" Point2="22,4.79" Point3="22,7" />
            <BezierSegment Point1="22,9.21" Point2="20.21,11" Point3="18,11" />
            <BezierSegment Point1="15.79,11" Point2="14,9.21" Point3="14,7" />
            <BezierSegment Point1="14,6.46" Point2="14.12,5.95" Point3="14.34,5.5" />
            <LineSegment Point="6.91,9.32" />
            <BezierSegment Point1="6.35,8.94" Point2="5.68,8.72" Point3="4.92,8.72" />
            <BezierSegment Point1="2.71,8.72" Point2="0.92,10.51" Point3="0.92,12.72" />
            <BezierSegment Point1="0.92,14.93" Point2="2.71,16.72" Point3="4.92,16.72" />
            <BezierSegment Point1="5.68,16.72" Point2="6.35,16.5" Point3="6.91,16.12" />
            <LineSegment Point="14.34,19.94" />
            <BezierSegment Point1="14.12,19.49" Point2="14,18.98" Point3="14,18.44" />
            <BezierSegment Point1="14,16.23" Point2="15.79,14.44" Point3="18,14.44" />
            <BezierSegment Point1="20.21,14.44" Point2="22,16.23" Point3="22,18.44" />
            <BezierSegment Point1="22,20.65" Point2="20.21,22.44" Point3="18,22.44" />
            <BezierSegment Point1="15.79,22.44" Point2="14,20.65" Point3="14,18.44" />
        </PathFigure>
    </PathGeometry>

    <!-- 链接图标 -->
    <PathGeometry x:Key="LinkGeometry">
        <PathFigure StartPoint="10,13">
            <BezierSegment Point1="10.89,13.89" Point2="12.11,13.89" Point3="13,13" />
            <LineSegment Point="15,11" />
            <BezierSegment Point1="16.89,9.11" Point2="16.89,6.11" Point3="15,4.22" />
            <BezierSegment Point1="13.11,2.33" Point2="10.11,2.33" Point3="8.22,4.22" />
            <LineSegment Point="7,5.44" />
        </PathFigure>
        <PathFigure StartPoint="14,11">
            <BezierSegment Point1="13.11,10.11" Point2="11.89,10.11" Point3="11,11" />
            <LineSegment Point="9,13" />
            <BezierSegment Point1="7.11,14.89" Point2="7.11,17.89" Point3="9,19.78" />
            <BezierSegment Point1="10.89,21.67" Point2="13.89,21.67" Point3="15.78,19.78" />
            <LineSegment Point="17,18.56" />
        </PathFigure>
    </PathGeometry>

</ResourceDictionary>
