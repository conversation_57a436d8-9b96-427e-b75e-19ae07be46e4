using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace FluentSystemDesign.WPF.Converters
{
    /// <summary>
    /// 布尔值到可见性转换器
    /// 将布尔值转换为Visibility枚举值，支持反向转换
    /// </summary>
    /// <remarks>
    /// 默认行为：true -> Visible, false -> Collapsed
    /// 使用参数"Invert"可以反转行为：true -> Collapsed, false -> Visible
    /// 使用参数"Hidden"可以将false转换为Hidden而不是Collapsed
    /// </remarks>
    /// <example>
    /// 在XAML中使用：
    /// <code>
    /// &lt;TextBlock Visibility="{Binding IsVisible, Converter={StaticResource BoolToVisibilityConverter}}"/&gt;
    /// &lt;TextBlock Visibility="{Binding IsHidden, Converter={StaticResource BoolToVisibilityConverter}, ConverterParameter=Invert}"/&gt;
    /// &lt;TextBlock Visibility="{Binding IsVisible, Converter={StaticResource BoolToVisibilityConverter}, ConverterParameter=Hidden}"/&gt;
    /// </code>
    /// </example>
    [ValueConversion(typeof(bool), typeof(Visibility))]
    public class BoolToVisibilityConverter : IValueConverter
    {
        /// <summary>
        /// 将布尔值转换为可见性
        /// </summary>
        /// <param name="value">布尔值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数：Invert（反转）、Hidden（使用Hidden而不是Collapsed）</param>
        /// <param name="culture">文化信息</param>
        /// <returns>可见性值</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not bool boolValue)
            {
                return DependencyProperty.UnsetValue;
            }

            var parameterString = parameter?.ToString()?.ToLowerInvariant();
            var isInverted = parameterString?.Contains("invert") == true;
            var useHidden = parameterString?.Contains("hidden") == true;

            var result = isInverted ? !boolValue : boolValue;

            if (result)
            {
                return Visibility.Visible;
            }
            else
            {
                return useHidden ? Visibility.Hidden : Visibility.Collapsed;
            }
        }

        /// <summary>
        /// 将可见性转换为布尔值
        /// </summary>
        /// <param name="value">可见性值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>布尔值</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not Visibility visibility)
            {
                return DependencyProperty.UnsetValue;
            }

            var parameterString = parameter?.ToString()?.ToLowerInvariant();
            var isInverted = parameterString?.Contains("invert") == true;

            var result = visibility == Visibility.Visible;
            return isInverted ? !result : result;
        }
    }
}
