using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Media;
using FluentSystemDesign.WPF.Converters;

namespace FluentSystemDesign.Examples
{
    /// <summary>
    /// FluentSystemDesign WPF 值转换器使用示例
    /// 展示各种转换器的C#代码使用方法
    /// </summary>
    public class ValueConvertersUsageExamples
    {
        /// <summary>
        /// 布尔转换器使用示例
        /// </summary>
        public void BooleanConverterExamples()
        {
            // BoolToVisibilityConverter 示例
            var boolToVisibilityConverter = new BoolToVisibilityConverter();
            
            // 基本转换
            var visibility1 = boolToVisibilityConverter.Convert(true, typeof(Visibility), null, null);
            // 结果: Visibility.Visible
            
            var visibility2 = boolToVisibilityConverter.Convert(false, typeof(Visibility), null, null);
            // 结果: Visibility.Collapsed
            
            // 使用参数反转逻辑
            var visibility3 = boolToVisibilityConverter.Convert(true, typeof(Visibility), "Invert", null);
            // 结果: Visibility.Collapsed
            
            // 使用Hidden模式
            var visibility4 = boolToVisibilityConverter.Convert(false, typeof(Visibility), "Hidden", null);
            // 结果: Visibility.Hidden

            // InverseBoolConverter 示例
            var inverseBoolConverter = new InverseBoolConverter();
            var inversedValue = inverseBoolConverter.Convert(true, typeof(bool), null, null);
            // 结果: false

            // BoolToOpacityConverter 示例
            var boolToOpacityConverter = new BoolToOpacityConverter();
            var opacity1 = boolToOpacityConverter.Convert(true, typeof(double), null, null);
            // 结果: 1.0
            
            var opacity2 = boolToOpacityConverter.Convert(false, typeof(double), "1.0,0.3", null);
            // 结果: 0.3
        }

        /// <summary>
        /// 数值转换器使用示例
        /// </summary>
        public void NumericConverterExamples()
        {
            // NumberToStringConverter 示例
            var numberToStringConverter = new NumberToStringConverter();
            
            // 货币格式
            var currency = numberToStringConverter.Convert(123.45, typeof(string), "C2", null);
            // 结果: "$123.45" (根据当前文化)
            
            // 百分比格式
            var percentage = numberToStringConverter.Convert(0.1234, typeof(string), "P1", null);
            // 结果: "12.3%"
            
            // 整数格式
            var integer = numberToStringConverter.Convert(1234567, typeof(string), "N0", null);
            // 结果: "1,234,567"

            // PercentageConverter 示例
            var percentageConverter = new PercentageConverter();
            var percent1 = percentageConverter.Convert(0.75, typeof(string), null, null);
            // 结果: "75%"
            
            var percent2 = percentageConverter.Convert(85, typeof(string), "0-100", null);
            // 结果: "85%"

            // ThicknessConverter 示例
            var thicknessConverter = new ThicknessConverter();
            var thickness1 = thicknessConverter.Convert(10, typeof(Thickness), null, null);
            // 结果: new Thickness(10)
            
            var thickness2 = thicknessConverter.Convert(5, typeof(Thickness), "Horizontal", null);
            // 结果: new Thickness(5, 0, 5, 0)
        }

        /// <summary>
        /// 颜色转换器使用示例
        /// </summary>
        public void ColorConverterExamples()
        {
            // HexToColorConverter 示例
            var hexToColorConverter = new HexToColorConverter();
            var color1 = hexToColorConverter.Convert("#FF5722", typeof(Color), null, null);
            // 结果: Color.FromRgb(255, 87, 34)
            
            var color2 = hexToColorConverter.Convert("FF5722", typeof(Color), null, null);
            // 结果: Color.FromRgb(255, 87, 34)

            // ColorToBrushConverter 示例
            var colorToBrushConverter = new ColorToBrushConverter();
            var brush1 = colorToBrushConverter.Convert(Colors.Blue, typeof(Brush), null, null);
            // 结果: new SolidColorBrush(Colors.Blue)
            
            var brush2 = colorToBrushConverter.Convert("#FF5722", typeof(Brush), "Alpha=0.5", null);
            // 结果: 半透明的橙色画刷

            // ColorToContrastConverter 示例
            var colorToContrastConverter = new ColorToContrastConverter();
            var contrastColor1 = colorToContrastConverter.Convert(Colors.White, typeof(Color), null, null);
            // 结果: Colors.Black (白色背景用黑色文字)
            
            var contrastColor2 = colorToContrastConverter.Convert(Colors.Black, typeof(Color), null, null);
            // 结果: Colors.White (黑色背景用白色文字)
        }

        /// <summary>
        /// 枚举转换器使用示例
        /// </summary>
        public void EnumConverterExamples()
        {
            // 定义测试枚举
            var status = TestStatus.Active;

            // EnumToStringConverter 示例
            var enumToStringConverter = new EnumToStringConverter();
            var statusString1 = enumToStringConverter.Convert(status, typeof(string), null, null);
            // 结果: "活跃" (如果有Description特性) 或 "Active"
            
            var statusString2 = enumToStringConverter.Convert(status, typeof(string), "ToUpper", null);
            // 结果: "ACTIVE"

            // EnumToBoolConverter 示例
            var enumToBoolConverter = new EnumToBoolConverter();
            var isActive = enumToBoolConverter.Convert(status, typeof(bool), "Active", null);
            // 结果: true
            
            var isInactive = enumToBoolConverter.Convert(status, typeof(bool), "Inactive,Disabled", null);
            // 结果: false

            // EnumToVisibilityConverter 示例
            var enumToVisibilityConverter = new EnumToVisibilityConverter();
            var visibility1 = enumToVisibilityConverter.Convert(status, typeof(Visibility), "Active", null);
            // 结果: Visibility.Visible
            
            var visibility2 = enumToVisibilityConverter.Convert(status, typeof(Visibility), "Invert:Active", null);
            // 结果: Visibility.Collapsed
        }

        /// <summary>
        /// 集合转换器使用示例
        /// </summary>
        public void CollectionConverterExamples()
        {
            var items = new ObservableCollection<string> { "Item1", "Item2", "Item3" };
            var emptyItems = new ObservableCollection<string>();

            // CollectionToVisibilityConverter 示例
            var collectionToVisibilityConverter = new CollectionToVisibilityConverter();
            var visibility1 = collectionToVisibilityConverter.Convert(items, typeof(Visibility), null, null);
            // 结果: Visibility.Visible (有元素)
            
            var visibility2 = collectionToVisibilityConverter.Convert(emptyItems, typeof(Visibility), null, null);
            // 结果: Visibility.Collapsed (空集合)
            
            var visibility3 = collectionToVisibilityConverter.Convert(items, typeof(Visibility), "MinCount=5", null);
            // 结果: Visibility.Collapsed (少于5个元素)

            // CountToVisibilityConverter 示例
            var countToVisibilityConverter = new CountToVisibilityConverter();
            var visibility4 = countToVisibilityConverter.Convert(3, typeof(Visibility), ">0", null);
            // 结果: Visibility.Visible
            
            var visibility5 = countToVisibilityConverter.Convert(3, typeof(Visibility), ">=5", null);
            // 结果: Visibility.Collapsed

            // IsNullOrEmptyConverter 示例
            var isNullOrEmptyConverter = new IsNullOrEmptyConverter();
            var isEmpty1 = isNullOrEmptyConverter.Convert(null, typeof(bool), null, null);
            // 结果: true
            
            var isEmpty2 = isNullOrEmptyConverter.Convert("", typeof(bool), null, null);
            // 结果: true
            
            var isEmpty3 = isNullOrEmptyConverter.Convert("Hello", typeof(bool), "Invert", null);
            // 结果: true (非空，但反转了)
        }

        /// <summary>
        /// 字符串转换器使用示例
        /// </summary>
        public void StringConverterExamples()
        {
            var testString = "Hello World";

            // StringToUpperConverter 示例
            var stringToUpperConverter = new StringToUpperConverter();
            var upper1 = stringToUpperConverter.Convert(testString, typeof(string), null, null);
            // 结果: "HELLO WORLD"
            
            var upper2 = stringToUpperConverter.Convert(testString, typeof(string), "Words", null);
            // 结果: "Hello World" (标题格式)

            // StringToLowerConverter 示例
            var stringToLowerConverter = new StringToLowerConverter();
            var lower1 = stringToLowerConverter.Convert(testString, typeof(string), null, null);
            // 结果: "hello world"
            
            var lower2 = stringToLowerConverter.Convert("Hello World", typeof(string), "CamelCase", null);
            // 结果: "helloWorld"

            // StringFormatConverter 示例
            var stringFormatConverter = new StringFormatConverter();
            var formatted1 = stringFormatConverter.Convert(123.45, typeof(string), "价格: {0:C}", null);
            // 结果: "价格: $123.45"
            
            var formatted2 = stringFormatConverter.Convert(null, typeof(string), "用户: {0},Null=未登录", null);
            // 结果: "未登录"
        }

        /// <summary>
        /// 主题转换器使用示例
        /// </summary>
        public void ThemeConverterExamples()
        {
            // ThemeToColorConverter 示例
            var themeToColorConverter = new ThemeToColorConverter();
            var color1 = themeToColorConverter.Convert("Light", typeof(Color), "Background", null);
            // 结果: 浅色主题的背景色
            
            var color2 = themeToColorConverter.Convert("Dark", typeof(Color), "Text", null);
            // 结果: 深色主题的文本色

            // ThemeToBrushConverter 示例
            var themeToBrushConverter = new ThemeToBrushConverter();
            var brush1 = themeToBrushConverter.Convert("Light", typeof(Brush), "Primary", null);
            // 结果: 浅色主题的主色调画刷
            
            var brush2 = themeToBrushConverter.Convert("Dark", typeof(Brush), "Primary,Opacity=0.5", null);
            // 结果: 深色主题的半透明主色调画刷
        }
    }

    /// <summary>
    /// 测试状态枚举
    /// </summary>
    public enum TestStatus
    {
        [Description("活跃")]
        Active,

        [Description("非活跃")]
        Inactive,

        [Description("处理中")]
        Processing,

        [Description("已完成")]
        Completed,

        [Description("已禁用")]
        Disabled
    }
}
