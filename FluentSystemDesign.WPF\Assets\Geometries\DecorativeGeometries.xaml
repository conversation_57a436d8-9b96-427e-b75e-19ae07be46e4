<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 
    装饰几何形状资源字典
    包括边框、分隔线、装饰元素等几何形状
    用于UI装饰和视觉分隔，遵循Fluent Design System设计语言
    -->

    <!-- 分隔线几何形状 -->
    
    <!-- 水平分隔线 -->
    <LineGeometry x:Key="HorizontalSeparatorGeometry" 
                  StartPoint="0,12" 
                  EndPoint="24,12" />

    <!-- 垂直分隔线 -->
    <LineGeometry x:Key="VerticalSeparatorGeometry" 
                  StartPoint="12,0" 
                  EndPoint="12,24" />

    <!-- 短水平分隔线 -->
    <LineGeometry x:Key="ShortHorizontalSeparatorGeometry" 
                  StartPoint="4,12" 
                  EndPoint="20,12" />

    <!-- 短垂直分隔线 -->
    <LineGeometry x:Key="ShortVerticalSeparatorGeometry" 
                  StartPoint="12,4" 
                  EndPoint="12,20" />

    <!-- 边框几何形状 -->
    
    <!-- 圆形边框 -->
    <EllipseGeometry x:Key="CircleBorderGeometry" 
                     Center="12,12" 
                     RadiusX="11" 
                     RadiusY="11" />

    <!-- 矩形边框 -->
    <RectangleGeometry x:Key="RectangleBorderGeometry" 
                       Rect="1,1,22,22" />

    <!-- 圆角矩形边框 -->
    <RectangleGeometry x:Key="RoundedRectangleBorderGeometry" 
                       Rect="1,1,22,22" 
                       RadiusX="4" 
                       RadiusY="4" />

    <!-- 虚线边框 -->
    <PathGeometry x:Key="DashedBorderGeometry">
        <PathFigure StartPoint="2,2">
            <LineSegment Point="6,2" />
        </PathFigure>
        <PathFigure StartPoint="10,2">
            <LineSegment Point="14,2" />
        </PathFigure>
        <PathFigure StartPoint="18,2">
            <LineSegment Point="22,2" />
        </PathFigure>
        <PathFigure StartPoint="22,6">
            <LineSegment Point="22,10" />
        </PathFigure>
        <PathFigure StartPoint="22,14">
            <LineSegment Point="22,18" />
        </PathFigure>
        <PathFigure StartPoint="18,22">
            <LineSegment Point="14,22" />
        </PathFigure>
        <PathFigure StartPoint="10,22">
            <LineSegment Point="6,22" />
        </PathFigure>
        <PathFigure StartPoint="2,18">
            <LineSegment Point="2,14" />
        </PathFigure>
        <PathFigure StartPoint="2,10">
            <LineSegment Point="2,6" />
        </PathFigure>
    </PathGeometry>

    <!-- 装饰元素几何形状 -->
    
    <!-- 装饰点 -->
    <EllipseGeometry x:Key="DecorativeDotGeometry" 
                     Center="12,12" 
                     RadiusX="2" 
                     RadiusY="2" />

    <!-- 三个装饰点 -->
    <GeometryGroup x:Key="ThreeDotsGeometry">
        <EllipseGeometry Center="6,12" RadiusX="2" RadiusY="2" />
        <EllipseGeometry Center="12,12" RadiusX="2" RadiusY="2" />
        <EllipseGeometry Center="18,12" RadiusX="2" RadiusY="2" />
    </GeometryGroup>

    <!-- 垂直三个装饰点 -->
    <GeometryGroup x:Key="VerticalThreeDotsGeometry">
        <EllipseGeometry Center="12,6" RadiusX="2" RadiusY="2" />
        <EllipseGeometry Center="12,12" RadiusX="2" RadiusY="2" />
        <EllipseGeometry Center="12,18" RadiusX="2" RadiusY="2" />
    </GeometryGroup>

    <!-- 装饰线条 -->
    <PathGeometry x:Key="DecorativeLineGeometry">
        <PathFigure StartPoint="2,12">
            <LineSegment Point="8,12" />
        </PathFigure>
        <PathFigure StartPoint="16,12">
            <LineSegment Point="22,12" />
        </PathFigure>
    </PathGeometry>

    <!-- 波浪线 -->
    <PathGeometry x:Key="WaveLineGeometry">
        <PathFigure StartPoint="0,12">
            <BezierSegment Point1="4,8" Point2="8,16" Point3="12,12" />
            <BezierSegment Point1="16,8" Point2="20,16" Point3="24,12" />
        </PathFigure>
    </PathGeometry>

    <!-- 锯齿线 -->
    <PathGeometry x:Key="ZigzagLineGeometry">
        <PathFigure StartPoint="0,12">
            <LineSegment Point="3,8" />
            <LineSegment Point="6,16" />
            <LineSegment Point="9,8" />
            <LineSegment Point="12,16" />
            <LineSegment Point="15,8" />
            <LineSegment Point="18,16" />
            <LineSegment Point="21,8" />
            <LineSegment Point="24,12" />
        </PathFigure>
    </PathGeometry>

    <!-- 角标几何形状 -->
    
    <!-- 圆形角标 -->
    <EllipseGeometry x:Key="CircleBadgeGeometry" 
                     Center="18,6" 
                     RadiusX="6" 
                     RadiusY="6" />

    <!-- 小圆形角标 -->
    <EllipseGeometry x:Key="SmallCircleBadgeGeometry" 
                     Center="18,6" 
                     RadiusX="4" 
                     RadiusY="4" />

    <!-- 矩形角标 -->
    <RectangleGeometry x:Key="RectangleBadgeGeometry" 
                       Rect="12,0,12,6" 
                       RadiusX="3" 
                       RadiusY="3" />

    <!-- 三角形角标 -->
    <PathGeometry x:Key="TriangleBadgeGeometry">
        <PathFigure StartPoint="24,0" IsClosed="True">
            <LineSegment Point="24,12" />
            <LineSegment Point="12,0" />
        </PathFigure>
    </PathGeometry>

    <!-- 进度条几何形状 -->
    
    <!-- 圆形进度条背景 -->
    <EllipseGeometry x:Key="CircularProgressBackgroundGeometry" 
                     Center="12,12" 
                     RadiusX="10" 
                     RadiusY="10" />

    <!-- 线性进度条背景 -->
    <RectangleGeometry x:Key="LinearProgressBackgroundGeometry" 
                       Rect="0,10,24,4" 
                       RadiusX="2" 
                       RadiusY="2" />

    <!-- 滑块轨道 -->
    <RectangleGeometry x:Key="SliderTrackGeometry" 
                       Rect="0,11,24,2" 
                       RadiusX="1" 
                       RadiusY="1" />

    <!-- 滑块拇指 -->
    <EllipseGeometry x:Key="SliderThumbGeometry" 
                     Center="12,12" 
                     RadiusX="6" 
                     RadiusY="6" />

    <!-- 开关几何形状 -->
    
    <!-- 开关背景 -->
    <RectangleGeometry x:Key="ToggleSwitchBackgroundGeometry" 
                       Rect="0,8,24,8" 
                       RadiusX="4" 
                       RadiusY="4" />

    <!-- 开关拇指 -->
    <EllipseGeometry x:Key="ToggleSwitchThumbGeometry" 
                     Center="8,12" 
                     RadiusX="6" 
                     RadiusY="6" />

    <!-- 复选框几何形状 -->
    
    <!-- 复选框背景 -->
    <RectangleGeometry x:Key="CheckBoxBackgroundGeometry" 
                       Rect="4,4,16,16" 
                       RadiusX="2" 
                       RadiusY="2" />

    <!-- 单选按钮背景 -->
    <EllipseGeometry x:Key="RadioButtonBackgroundGeometry" 
                     Center="12,12" 
                     RadiusX="8" 
                     RadiusY="8" />

    <!-- 单选按钮选中标记 -->
    <EllipseGeometry x:Key="RadioButtonCheckGeometry" 
                     Center="12,12" 
                     RadiusX="4" 
                     RadiusY="4" />

    <!-- 箭头装饰 -->
    
    <!-- 展开箭头 -->
    <PathGeometry x:Key="ExpandArrowGeometry">
        <PathFigure StartPoint="8,10">
            <LineSegment Point="12,14" />
            <LineSegment Point="16,10" />
        </PathFigure>
    </PathGeometry>

    <!-- 折叠箭头 -->
    <PathGeometry x:Key="CollapseArrowGeometry">
        <PathFigure StartPoint="8,14">
            <LineSegment Point="12,10" />
            <LineSegment Point="16,14" />
        </PathFigure>
    </PathGeometry>

    <!-- 排序箭头 -->
    <PathGeometry x:Key="SortAscendingGeometry">
        <PathFigure StartPoint="8,16">
            <LineSegment Point="12,8" />
            <LineSegment Point="16,16" />
        </PathFigure>
    </PathGeometry>

    <PathGeometry x:Key="SortDescendingGeometry">
        <PathFigure StartPoint="8,8">
            <LineSegment Point="12,16" />
            <LineSegment Point="16,8" />
        </PathFigure>
    </PathGeometry>

    <!-- 网格线几何形状 -->
    
    <!-- 网格线 -->
    <GeometryGroup x:Key="GridLinesGeometry">
        <!-- 垂直线 -->
        <LineGeometry StartPoint="6,0" EndPoint="6,24" />
        <LineGeometry StartPoint="12,0" EndPoint="12,24" />
        <LineGeometry StartPoint="18,0" EndPoint="18,24" />
        <!-- 水平线 -->
        <LineGeometry StartPoint="0,6" EndPoint="24,6" />
        <LineGeometry StartPoint="0,12" EndPoint="24,12" />
        <LineGeometry StartPoint="0,18" EndPoint="24,18" />
    </GeometryGroup>

    <!-- 点状网格 -->
    <GeometryGroup x:Key="DotGridGeometry">
        <EllipseGeometry Center="6,6" RadiusX="1" RadiusY="1" />
        <EllipseGeometry Center="12,6" RadiusX="1" RadiusY="1" />
        <EllipseGeometry Center="18,6" RadiusX="1" RadiusY="1" />
        <EllipseGeometry Center="6,12" RadiusX="1" RadiusY="1" />
        <EllipseGeometry Center="12,12" RadiusX="1" RadiusY="1" />
        <EllipseGeometry Center="18,12" RadiusX="1" RadiusY="1" />
        <EllipseGeometry Center="6,18" RadiusX="1" RadiusY="1" />
        <EllipseGeometry Center="12,18" RadiusX="1" RadiusY="1" />
        <EllipseGeometry Center="18,18" RadiusX="1" RadiusY="1" />
    </GeometryGroup>

</ResourceDictionary>
